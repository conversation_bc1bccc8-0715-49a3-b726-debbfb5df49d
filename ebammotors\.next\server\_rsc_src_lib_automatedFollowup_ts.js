"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_automatedFollowup_ts";
exports.ids = ["_rsc_src_lib_automatedFollowup_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/automatedFollowup.ts":
/*!**************************************!*\
  !*** ./src/lib/automatedFollowup.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initializeAutomatedFollowups: () => (/* binding */ initializeAutomatedFollowups),\n/* harmony export */   processAutomatedFollowups: () => (/* binding */ processAutomatedFollowups),\n/* harmony export */   scheduleAutoFollowupForCustomer: () => (/* binding */ scheduleAutoFollowupForCustomer),\n/* harmony export */   scheduleAutoFollowupForLead: () => (/* binding */ scheduleAutoFollowupForLead),\n/* harmony export */   triggerFollowupProcessing: () => (/* binding */ triggerFollowupProcessing)\n/* harmony export */ });\n/* harmony import */ var _crmStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./crmStorage */ \"(rsc)/./src/lib/crmStorage.ts\");\n/* harmony import */ var _resendService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./resendService */ \"(rsc)/./src/lib/resendService.ts\");\n\n\nconst DEFAULT_CONFIG = {\n    delayDays: 3,\n    enableEmail: true,\n    enableSMS: true,\n    emailTemplate: 'customer_followup',\n    smsTemplate: 'customer_followup_sms'\n};\n/**\n * Schedule automatic follow-up for new customer\n */ async function scheduleAutoFollowupForCustomer(customerId, customerData) {\n    try {\n        const followupDate = new Date();\n        followupDate.setDate(followupDate.getDate() + DEFAULT_CONFIG.delayDays);\n        // Schedule email follow-up\n        if (DEFAULT_CONFIG.enableEmail) {\n            await (0,_crmStorage__WEBPACK_IMPORTED_MODULE_0__.createFollowUp)({\n                type: 'email',\n                status: 'pending',\n                priority: 'medium',\n                customerId,\n                title: '3-Day Customer Follow-up (Email)',\n                description: `Automated follow-up email to check customer satisfaction and offer assistance`,\n                scheduledDate: followupDate.toISOString(),\n                automationRule: {\n                    trigger: 'customer_created',\n                    delay: DEFAULT_CONFIG.delayDays * 24,\n                    conditions: {\n                        type: 'email',\n                        template: DEFAULT_CONFIG.emailTemplate\n                    }\n                },\n                createdBy: 'system'\n            });\n        }\n        // Schedule SMS follow-up\n        if (DEFAULT_CONFIG.enableSMS && customerData.personalInfo?.phone) {\n            await (0,_crmStorage__WEBPACK_IMPORTED_MODULE_0__.createFollowUp)({\n                type: 'sms',\n                status: 'pending',\n                priority: 'medium',\n                customerId,\n                title: '3-Day Customer Follow-up (SMS)',\n                description: `Automated SMS follow-up to check customer satisfaction`,\n                scheduledDate: followupDate.toISOString(),\n                automationRule: {\n                    trigger: 'customer_created',\n                    delay: DEFAULT_CONFIG.delayDays * 24,\n                    conditions: {\n                        type: 'sms',\n                        template: DEFAULT_CONFIG.smsTemplate,\n                        phone: customerData.personalInfo.phone\n                    }\n                },\n                createdBy: 'system'\n            });\n        }\n        console.log(`📅 Scheduled automated follow-ups for customer ${customerId} in ${DEFAULT_CONFIG.delayDays} days`);\n    } catch (error) {\n        console.error('Error scheduling auto follow-up for customer:', error);\n    }\n}\n/**\n * Schedule automatic follow-up for new lead\n */ async function scheduleAutoFollowupForLead(leadId, leadData) {\n    try {\n        const followupDate = new Date();\n        followupDate.setDate(followupDate.getDate() + DEFAULT_CONFIG.delayDays);\n        // Schedule email follow-up\n        if (DEFAULT_CONFIG.enableEmail && leadData.customerInfo?.email) {\n            await (0,_crmStorage__WEBPACK_IMPORTED_MODULE_0__.createFollowUp)({\n                type: 'email',\n                status: 'pending',\n                priority: 'high',\n                leadId,\n                title: '3-Day Lead Follow-up (Email)',\n                description: `Automated follow-up email for lead nurturing and conversion`,\n                scheduledDate: followupDate.toISOString(),\n                automationRule: {\n                    trigger: 'lead_created',\n                    delay: DEFAULT_CONFIG.delayDays * 24,\n                    conditions: {\n                        type: 'email',\n                        template: 'lead_followup',\n                        leadSource: leadData.source,\n                        productInterest: leadData.inquiry?.productInterest\n                    }\n                },\n                createdBy: 'system'\n            });\n        }\n        // Schedule SMS follow-up\n        if (DEFAULT_CONFIG.enableSMS && leadData.customerInfo?.phone) {\n            await (0,_crmStorage__WEBPACK_IMPORTED_MODULE_0__.createFollowUp)({\n                type: 'sms',\n                status: 'pending',\n                priority: 'high',\n                leadId,\n                title: '3-Day Lead Follow-up (SMS)',\n                description: `Automated SMS follow-up for lead conversion`,\n                scheduledDate: followupDate.toISOString(),\n                automationRule: {\n                    trigger: 'lead_created',\n                    delay: DEFAULT_CONFIG.delayDays * 24,\n                    conditions: {\n                        type: 'sms',\n                        template: 'lead_followup_sms',\n                        phone: leadData.customerInfo.phone,\n                        productInterest: leadData.inquiry?.productInterest\n                    }\n                },\n                createdBy: 'system'\n            });\n        }\n        console.log(`📅 Scheduled automated follow-ups for lead ${leadId} in ${DEFAULT_CONFIG.delayDays} days`);\n    } catch (error) {\n        console.error('Error scheduling auto follow-up for lead:', error);\n    }\n}\n/**\n * Process pending automated follow-ups\n * This should be called periodically (e.g., every hour) to send due follow-ups\n */ async function processAutomatedFollowups() {\n    try {\n        const { getAllFollowUps, updateFollowUp, getCustomerById, getLeadById } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./crmStorage */ \"(rsc)/./src/lib/crmStorage.ts\"));\n        const followups = await getAllFollowUps();\n        const now = new Date();\n        // Filter for pending automated follow-ups that are due\n        const dueFollowups = followups.filter((followup)=>followup.status === 'pending' && followup.automationRule && new Date(followup.scheduledDate) <= now);\n        console.log(`🔄 Processing ${dueFollowups.length} due automated follow-ups`);\n        for (const followup of dueFollowups){\n            try {\n                await processIndividualFollowup(followup);\n                // Mark as completed\n                await updateFollowUp(followup.id, {\n                    status: 'completed',\n                    completedAt: new Date().toISOString(),\n                    notes: `${followup.notes || ''}\\n\\nAutomatically processed on ${new Date().toLocaleString()}`\n                });\n            } catch (error) {\n                console.error(`Error processing followup ${followup.id}:`, error);\n                // Mark as failed\n                await updateFollowUp(followup.id, {\n                    status: 'failed',\n                    notes: `${followup.notes || ''}\\n\\nFailed to process: ${error instanceof Error ? error.message : 'Unknown error'}`\n                });\n            }\n        }\n    } catch (error) {\n        console.error('Error processing automated follow-ups:', error);\n    }\n}\n/**\n * Process individual follow-up (send email/SMS)\n */ async function processIndividualFollowup(followup) {\n    const { getCustomerById, getLeadById } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./crmStorage */ \"(rsc)/./src/lib/crmStorage.ts\"));\n    let recipient = null;\n    let recipientType = '';\n    // Get recipient data\n    if (followup.customerId) {\n        recipient = await getCustomerById(followup.customerId);\n        recipientType = 'customer';\n    } else if (followup.leadId) {\n        recipient = await getLeadById(followup.leadId);\n        recipientType = 'lead';\n    }\n    if (!recipient) {\n        throw new Error(`Recipient not found for followup ${followup.id}`);\n    }\n    const conditions = followup.automationRule?.conditions || {};\n    if (followup.type === 'email') {\n        await sendAutomatedEmail(recipient, recipientType, conditions, followup);\n    } else if (followup.type === 'sms') {\n        await sendAutomatedSMS(recipient, recipientType, conditions, followup);\n    }\n    // Log interaction\n    await (0,_crmStorage__WEBPACK_IMPORTED_MODULE_0__.createInteraction)({\n        customerId: followup.customerId,\n        leadId: followup.leadId,\n        type: followup.type === 'email' ? 'email' : 'sms',\n        direction: 'outbound',\n        channel: 'automation',\n        content: `Automated ${followup.type} follow-up sent: ${followup.title}`,\n        subject: followup.title,\n        tags: [\n            'automated',\n            'follow_up',\n            followup.type\n        ],\n        createdBy: 'system'\n    });\n}\n/**\n * Send automated email\n */ async function sendAutomatedEmail(recipient, recipientType, conditions, followup) {\n    const email = recipientType === 'customer' ? recipient.personalInfo?.email : recipient.customerInfo?.email;\n    if (!email) {\n        throw new Error('No email address found for recipient');\n    }\n    const name = recipientType === 'customer' ? recipient.personalInfo?.name : recipient.customerInfo?.name;\n    // Prepare email data based on template\n    let emailData = {\n        to: email,\n        customerName: name,\n        followupType: followup.title,\n        description: followup.description\n    };\n    if (conditions.template === 'lead_followup') {\n        emailData = {\n            ...emailData,\n            productInterest: conditions.productInterest || 'our vehicles',\n            leadSource: conditions.leadSource || 'website',\n            inquiryDetails: recipient.inquiry?.message || ''\n        };\n    }\n    // Send email using the email service\n    await _resendService__WEBPACK_IMPORTED_MODULE_1__.emailService.sendFollowUpEmail(emailData);\n    console.log(`📧 Automated email sent to ${email}`);\n}\n/**\n * Send automated SMS\n */ async function sendAutomatedSMS(recipient, recipientType, conditions, followup) {\n    const phone = conditions.phone;\n    if (!phone) {\n        throw new Error('No phone number found for recipient');\n    }\n    const name = recipientType === 'customer' ? recipient.personalInfo?.name : recipient.customerInfo?.name;\n    // Prepare SMS message\n    let message = '';\n    if (conditions.template === 'lead_followup_sms') {\n        const productInterest = conditions.productInterest || 'vehicles';\n        message = `Hi ${name}! Following up on your interest in ${productInterest}. We have great options available. Any questions? Reply or call +233245375692. - EBAM Motors`;\n    } else {\n        message = `Hi ${name}! Hope you're satisfied with our service. Need any assistance with your vehicle or have questions? We're here to help! Call +233245375692. - EBAM Motors`;\n    }\n    // For now, we'll log the SMS (you can integrate with SMS service like Twilio)\n    console.log(`📱 Automated SMS would be sent to ${phone}: ${message}`);\n// TODO: Integrate with actual SMS service\n// await smsService.send(phone, message);\n}\n/**\n * Initialize automated follow-up system\n * Call this when the application starts\n */ function initializeAutomatedFollowups() {\n    console.log('🚀 Initializing automated follow-up system...');\n    // Process pending follow-ups every hour\n    setInterval(processAutomatedFollowups, 60 * 60 * 1000);\n    // Also process immediately on startup\n    setTimeout(processAutomatedFollowups, 5000); // Wait 5 seconds after startup\n    console.log('✅ Automated follow-up system initialized');\n}\n/**\n * Manual trigger for processing follow-ups (for testing)\n */ async function triggerFollowupProcessing() {\n    console.log('🔄 Manually triggering follow-up processing...');\n    try {\n        await processAutomatedFollowups();\n        return {\n            processed: 1,\n            errors: 0\n        };\n    } catch (error) {\n        console.error('Error in manual follow-up processing:', error);\n        return {\n            processed: 0,\n            errors: 1\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/automatedFollowup.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/emailTemplates.ts":
/*!***********************************!*\
  !*** ./src/lib/emailTemplates.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmailTemplates: () => (/* binding */ EmailTemplates)\n/* harmony export */ });\n// Base email styles\nconst emailStyles = `\n  <style>\n    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }\n    .container { max-width: 600px; margin: 0 auto; background-color: white; }\n    .header { background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); color: white; padding: 30px; text-align: center; }\n    .logo { font-size: 28px; font-weight: bold; margin-bottom: 10px; }\n    .content { padding: 30px; }\n    .vehicle-card { border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; margin: 20px 0; }\n    .button { display: inline-block; background: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }\n    .footer { background-color: #f9fafb; padding: 20px; text-align: center; color: #6b7280; font-size: 14px; }\n    .highlight { background-color: #eff6ff; padding: 15px; border-radius: 6px; margin: 15px 0; }\n    .status-badge { display: inline-block; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: bold; }\n    .status-confirmed { background-color: #dcfce7; color: #166534; }\n    .divider { height: 1px; background-color: #e5e7eb; margin: 20px 0; }\n  </style>\n`;\nclass EmailTemplates {\n    /**\n   * Generate Order Confirmation HTML\n   */ static generateOrderConfirmationHTML(data) {\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>Order Confirmation - ${data.orderNumber}</title>\n        ${emailStyles}\n      </head>\n      <body>\n        <div class=\"container\">\n          <!-- Header -->\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors</div>\n            <p>Your trusted partner for quality vehicles from Japan to Ghana</p>\n          </div>\n\n          <!-- Content -->\n          <div class=\"content\">\n            <h1>Order Confirmation</h1>\n            <p>Dear ${data.customerName},</p>\n            <p>Thank you for your order! We're excited to help you get your new vehicle.</p>\n\n            <div class=\"highlight\">\n              <h3>Order Details</h3>\n              <p><strong>Order Number:</strong> ${data.orderNumber}</p>\n              <p><strong>Order Date:</strong> ${data.orderDate}</p>\n              <p><strong>Status:</strong> <span class=\"status-badge status-confirmed\">Confirmed</span></p>\n            </div>\n\n            <!-- Vehicle Details -->\n            <div class=\"vehicle-card\">\n              <h3>Vehicle Information</h3>\n              <img src=\"${data.vehicle.image}\" alt=\"${data.vehicle.title}\" style=\"width: 100%; max-width: 300px; height: 200px; object-fit: cover; border-radius: 6px; margin-bottom: 15px;\">\n              <h4>${data.vehicle.title}</h4>\n              <p><strong>Price:</strong> ${data.vehicle.price}</p>\n            </div>\n\n            <!-- Shipping Information -->\n            <div class=\"highlight\">\n              <h3>Shipping Address</h3>\n              <p>\n                ${data.shippingAddress.street}<br>\n                ${data.shippingAddress.city}, ${data.shippingAddress.state}<br>\n                ${data.shippingAddress.country} ${data.shippingAddress.postalCode}\n              </p>\n              <p><strong>Estimated Delivery:</strong> ${data.estimatedDelivery}</p>\n            </div>\n\n            <!-- Order Summary -->\n            <div class=\"divider\"></div>\n            <div style=\"text-align: right;\">\n              <h3>Order Total: ${data.total}</h3>\n            </div>\n\n            <!-- Next Steps -->\n            <div class=\"highlight\">\n              <h3>What's Next?</h3>\n              <ul>\n                <li>We'll prepare your vehicle for shipping</li>\n                <li>You'll receive tracking information once shipped</li>\n                <li>Our team will contact you for any updates</li>\n              </ul>\n            </div>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://yourdomain.com/tracking?order=${data.orderNumber}\" class=\"button\">Track Your Order</a>\n            </div>\n\n            <p>If you have any questions, please don't hesitate to contact us:</p>\n            <ul>\n              <li>📧 Email: <EMAIL></li>\n              <li>📱 WhatsApp: +233245375692</li>\n              <li>📍 Location: Kumasi, Ghana</li>\n            </ul>\n          </div>\n\n          <!-- Footer -->\n          <div class=\"footer\">\n            <p>Thank you for choosing EBAM Motors!</p>\n            <p>© 2024 EBAM Motors. All rights reserved.</p>\n            <p>This email was sent to confirm your order. Please keep this for your records.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    /**\n   * Generate Order Confirmation Text\n   */ static generateOrderConfirmationText(data) {\n        return `\nORDER CONFIRMATION - EBAM Motors\n\nDear ${data.customerName},\n\nThank you for your order! We're excited to help you get your new vehicle.\n\nORDER DETAILS:\n- Order Number: ${data.orderNumber}\n- Order Date: ${data.orderDate}\n- Status: Confirmed\n\nVEHICLE:\n- ${data.vehicle.title}\n- Price: ${data.vehicle.price}\n\nSHIPPING ADDRESS:\n${data.shippingAddress.street}\n${data.shippingAddress.city}, ${data.shippingAddress.state}\n${data.shippingAddress.country} ${data.shippingAddress.postalCode}\n\nEstimated Delivery: ${data.estimatedDelivery}\n\nORDER TOTAL: ${data.total}\n\nWHAT'S NEXT:\n- We'll prepare your vehicle for shipping\n- You'll receive tracking information once shipped\n- Our team will contact you for any updates\n\nTrack your order: https://yourdomain.com/tracking?order=${data.orderNumber}\n\nCONTACT US:\n- Email: <EMAIL>\n- WhatsApp: +233245375692\n- Location: Kumasi, Ghana\n\nThank you for choosing EBAM Motors!\n© 2024 EBAM Motors. All rights reserved.\n    `;\n    }\n    /**\n   * Generate Review Notification HTML for Admin\n   */ static generateReviewNotificationHTML(data) {\n        const stars = '⭐'.repeat(data.rating) + '☆'.repeat(5 - data.rating);\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>New Review Submitted</title>\n        ${emailStyles}\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors Admin</div>\n            <p>New Review Notification</p>\n          </div>\n\n          <div class=\"content\">\n            <h1>New Review Submitted</h1>\n            \n            <div class=\"highlight\">\n              <h3>Review Details</h3>\n              <p><strong>Customer:</strong> ${data.customerName}</p>\n              <p><strong>Vehicle:</strong> ${data.vehicleTitle}</p>\n              <p><strong>Rating:</strong> ${stars} (${data.rating}/5)</p>\n              <p><strong>Date:</strong> ${data.reviewDate}</p>\n            </div>\n\n            <div class=\"vehicle-card\">\n              <h3>Review Content</h3>\n              <p>\"${data.review}\"</p>\n            </div>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://yourdomain.com/admin/reviews\" class=\"button\">Review & Approve</a>\n            </div>\n          </div>\n\n          <div class=\"footer\">\n            <p>EBAM Motors Admin Panel</p>\n            <p>Please review and approve/reject this review in the admin panel.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    /**\n   * Generate Review Approval HTML for Customer\n   */ static generateReviewApprovalHTML(data) {\n        const stars = '⭐'.repeat(data.rating) + '☆'.repeat(5 - data.rating);\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>Review Approved</title>\n        ${emailStyles}\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors</div>\n            <p>Thank you for your feedback!</p>\n          </div>\n\n          <div class=\"content\">\n            <h1>Your Review Has Been Approved!</h1>\n            <p>Dear ${data.customerName},</p>\n            <p>Thank you for taking the time to review your experience with us. Your review has been approved and is now live on our website!</p>\n\n            <div class=\"highlight\">\n              <h3>Your Review</h3>\n              <p><strong>Vehicle:</strong> ${data.vehicleTitle}</p>\n              <p><strong>Rating:</strong> ${stars} (${data.rating}/5)</p>\n              <p><strong>Review:</strong> \"${data.review}\"</p>\n            </div>\n\n            <p>Your feedback helps other customers make informed decisions and helps us improve our services.</p>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://yourdomain.com/reviews\" class=\"button\">View All Reviews</a>\n            </div>\n          </div>\n\n          <div class=\"footer\">\n            <p>Thank you for choosing EBAM Motors!</p>\n            <p>© 2024 EBAM Motors. All rights reserved.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    /**\n   * Generate Contact Form Admin Notification HTML\n   */ static generateContactFormAdminHTML(data) {\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>New Contact Form Submission</title>\n        ${emailStyles}\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors Admin</div>\n            <p>New Contact Form Submission</p>\n          </div>\n\n          <div class=\"content\">\n            <h1>New Contact Form Submission</h1>\n            \n            <div class=\"highlight\">\n              <h3>Contact Details</h3>\n              <p><strong>Name:</strong> ${data.name}</p>\n              <p><strong>Email:</strong> ${data.email}</p>\n              <p><strong>Subject:</strong> ${data.subject}</p>\n              <p><strong>Submitted:</strong> ${data.submissionDate}</p>\n            </div>\n\n            <div class=\"vehicle-card\">\n              <h3>Message</h3>\n              <p>${data.message.replace(/\\n/g, '<br>')}</p>\n            </div>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"mailto:${data.email}?subject=Re: ${data.subject}\" class=\"button\">Reply to Customer</a>\n            </div>\n          </div>\n\n          <div class=\"footer\">\n            <p>EBAM Motors Admin Panel</p>\n            <p>Please respond to this customer inquiry promptly.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    /**\n   * Generate Abandoned Cart Follow-up HTML\n   */ static generateAbandonedCartHTML(data) {\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>Complete Your Purchase</title>\n        ${emailStyles}\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors</div>\n            <p>Don't miss out on your perfect vehicle!</p>\n          </div>\n\n          <div class=\"content\">\n            <h1>Complete Your Purchase</h1>\n            <p>Hi ${data.customerName},</p>\n            <p>We noticed you were interested in some amazing vehicles but didn't complete your purchase. Don't worry - we've saved your items!</p>\n\n            <div class=\"highlight\">\n              <h3>Your Saved Items</h3>\n              ${data.data?.items?.map((item)=>`\n                <div class=\"vehicle-card\">\n                  <h4>${item.title}</h4>\n                  <p><strong>Price:</strong> ${item.price}</p>\n                  <p>Quantity: ${item.quantity}</p>\n                </div>\n              `).join('') || '<p>Your selected vehicles are waiting for you!</p>'}\n            </div>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://yourdomain.com/stock\" class=\"button\">Complete Your Purchase</a>\n            </div>\n\n            <div class=\"highlight\">\n              <h3>Why Choose EBAM Motors?</h3>\n              <ul>\n                <li>✅ Quality guaranteed vehicles from Japan</li>\n                <li>✅ Competitive pricing with transparent costs</li>\n                <li>✅ Reliable shipping to Ghana and Africa</li>\n                <li>✅ Expert support throughout the process</li>\n              </ul>\n            </div>\n\n            <p>Need help deciding? Our team is here to assist you:</p>\n            <ul>\n              <li>📱 WhatsApp: +233245375692</li>\n              <li>📧 Email: <EMAIL></li>\n            </ul>\n          </div>\n\n          <div class=\"footer\">\n            <p>This offer won't last forever!</p>\n            <p>© 2024 EBAM Motors. All rights reserved.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    /**\n   * Generate Delivery Update HTML\n   */ static generateDeliveryUpdateHTML(data) {\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>Delivery Update</title>\n        ${emailStyles}\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors</div>\n            <p>Your vehicle is on its way!</p>\n          </div>\n\n          <div class=\"content\">\n            <h1>Delivery Update</h1>\n            <p>Hi ${data.customerName},</p>\n            <p>Great news! We have an update on your vehicle delivery.</p>\n\n            <div class=\"highlight\">\n              <h3>Delivery Status</h3>\n              <p><strong>Current Status:</strong> ${data.data?.status || 'In Transit'}</p>\n              <p><strong>Location:</strong> ${data.data?.location || 'En route to destination'}</p>\n              <p><strong>Estimated Arrival:</strong> ${data.data?.estimatedArrival || 'To be confirmed'}</p>\n            </div>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://yourdomain.com/tracking?order=${data.data?.orderId}\" class=\"button\">Track Your Order</a>\n            </div>\n\n            <div class=\"vehicle-card\">\n              <h3>What to Expect Next</h3>\n              <ul>\n                <li>We'll notify you 24 hours before delivery</li>\n                <li>Our delivery team will contact you directly</li>\n                <li>Ensure someone is available to receive the vehicle</li>\n                <li>Have your ID and order confirmation ready</li>\n              </ul>\n            </div>\n          </div>\n\n          <div class=\"footer\">\n            <p>Thank you for choosing EBAM Motors!</p>\n            <p>© 2024 EBAM Motors. All rights reserved.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    /**\n   * Generate Feedback Request HTML\n   */ static generateFeedbackRequestHTML(data) {\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>How was your experience?</title>\n        ${emailStyles}\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors</div>\n            <p>We'd love to hear from you!</p>\n          </div>\n\n          <div class=\"content\">\n            <h1>How Was Your Experience?</h1>\n            <p>Hi ${data.customerName},</p>\n            <p>We hope you're enjoying your new vehicle! Your feedback helps us improve our services and helps other customers make informed decisions.</p>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://yourdomain.com/reviews\" class=\"button\">Leave a Review</a>\n            </div>\n\n            <div class=\"highlight\">\n              <h3>Share Your Experience</h3>\n              <p>Tell us about:</p>\n              <ul>\n                <li>🚗 Vehicle quality and condition</li>\n                <li>📦 Shipping and delivery experience</li>\n                <li>👥 Customer service quality</li>\n                <li>💰 Value for money</li>\n                <li>🌟 Overall satisfaction</li>\n              </ul>\n            </div>\n\n            <div class=\"vehicle-card\">\n              <h3>Why Your Review Matters</h3>\n              <ul>\n                <li>Helps other customers make confident decisions</li>\n                <li>Helps us improve our services</li>\n                <li>Builds trust in our community</li>\n                <li>Takes less than 2 minutes to complete</li>\n              </ul>\n            </div>\n\n            <p>As a thank you, customers who leave reviews get priority support and exclusive offers!</p>\n          </div>\n\n          <div class=\"footer\">\n            <p>Thank you for choosing EBAM Motors!</p>\n            <p>© 2024 EBAM Motors. All rights reserved.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    /**\n   * Generate Contact Form Customer Confirmation HTML\n   */ static generateContactFormCustomerHTML(data) {\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>Thank you for contacting us</title>\n        ${emailStyles}\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors</div>\n            <p>Thank you for reaching out!</p>\n          </div>\n\n          <div class=\"content\">\n            <h1>Thank You for Contacting Us!</h1>\n            <p>Dear ${data.name},</p>\n            <p>We've received your message and appreciate you taking the time to contact us. Our team will review your inquiry and respond within 24 hours.</p>\n\n            <div class=\"highlight\">\n              <h3>Your Message Summary</h3>\n              <p><strong>Subject:</strong> ${data.subject}</p>\n              <p><strong>Submitted:</strong> ${data.submissionDate}</p>\n              <p><strong>Reference ID:</strong> #${Date.now().toString().slice(-6)}</p>\n            </div>\n\n            <div class=\"vehicle-card\">\n              <h3>What to Expect Next</h3>\n              <ul>\n                <li>Our team will review your inquiry</li>\n                <li>You'll receive a response within 24 hours</li>\n                <li>For urgent matters, contact us on WhatsApp</li>\n              </ul>\n            </div>\n\n            <p>In the meantime, feel free to:</p>\n            <ul>\n              <li>Browse our latest vehicle inventory</li>\n              <li>Check out customer reviews</li>\n              <li>Learn more about our services</li>\n            </ul>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://yourdomain.com/stock\" class=\"button\">Browse Vehicles</a>\n            </div>\n\n            <p><strong>Need immediate assistance?</strong></p>\n            <ul>\n              <li>📱 WhatsApp: +233245375692</li>\n              <li>📧 Email: <EMAIL></li>\n              <li>📍 Location: Kumasi, Ghana</li>\n            </ul>\n          </div>\n\n          <div class=\"footer\">\n            <p>Thank you for choosing EBAM Motors!</p>\n            <p>© 2024 EBAM Motors. All rights reserved.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/emailTemplates.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/resendService.ts":
/*!**********************************!*\
  !*** ./src/lib/resendService.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EMAIL_CONFIG: () => (/* binding */ EMAIL_CONFIG),\n/* harmony export */   ResendEmailService: () => (/* binding */ ResendEmailService),\n/* harmony export */   emailService: () => (/* binding */ emailService)\n/* harmony export */ });\n/* harmony import */ var resend__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! resend */ \"(rsc)/./node_modules/resend/dist/index.mjs\");\n/* harmony import */ var _emailTemplates__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./emailTemplates */ \"(rsc)/./src/lib/emailTemplates.ts\");\n\n\n// Initialize Resend with API key\nconst resend = new resend__WEBPACK_IMPORTED_MODULE_0__.Resend(process.env.RESEND_API_KEY);\n// Email configuration\nconst EMAIL_CONFIG = {\n    from: process.env.RESEND_FROM_EMAIL || 'EBAM Motors <<EMAIL>>',\n    adminEmail: process.env.ADMIN_EMAIL || '<EMAIL>',\n    supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',\n    noReplyEmail: process.env.NO_REPLY_EMAIL || '<EMAIL>'\n};\n// Base email service class\nclass ResendEmailService {\n    constructor(){\n        this.resend = resend;\n    }\n    /**\n   * Send a generic email\n   */ async sendEmail(template) {\n        try {\n            if (!process.env.RESEND_API_KEY) {\n                console.warn('Resend API key not configured. Email not sent.');\n                return {\n                    success: false,\n                    error: 'Resend API key not configured'\n                };\n            }\n            const result = await this.resend.emails.send({\n                from: EMAIL_CONFIG.from,\n                to: template.to,\n                subject: template.subject,\n                html: template.html,\n                text: template.text\n            });\n            if (result.error) {\n                console.error('Resend email error:', result.error);\n                return {\n                    success: false,\n                    error: result.error.message\n                };\n            }\n            console.log('Email sent successfully:', result.data?.id);\n            return {\n                success: true,\n                messageId: result.data?.id\n            };\n        } catch (error) {\n            console.error('Email service error:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    /**\n   * Send order confirmation email\n   */ async sendOrderConfirmation(customerEmail, data) {\n        const template = {\n            to: customerEmail,\n            subject: `Order Confirmation - ${data.orderNumber} | EBAM Motors`,\n            html: this.generateOrderConfirmationHTML(data),\n            text: this.generateOrderConfirmationText(data)\n        };\n        const result = await this.sendEmail(template);\n        return result;\n    }\n    /**\n   * Send review notification to admin\n   */ async sendReviewNotificationToAdmin(data) {\n        const template = {\n            to: EMAIL_CONFIG.adminEmail,\n            subject: `New Review Submitted - ${data.vehicleTitle} | EBAM Motors`,\n            html: this.generateReviewNotificationHTML(data),\n            text: this.generateReviewNotificationText(data)\n        };\n        const result = await this.sendEmail(template);\n        return result;\n    }\n    /**\n   * Send review approval notification to customer\n   */ async sendReviewApprovalNotification(customerEmail, data) {\n        const template = {\n            to: customerEmail,\n            subject: `Your Review Has Been Approved | EBAM Motors`,\n            html: this.generateReviewApprovalHTML(data),\n            text: this.generateReviewApprovalText(data)\n        };\n        const result = await this.sendEmail(template);\n        return result;\n    }\n    /**\n   * Send contact form submission notification\n   */ async sendContactFormNotification(data) {\n        // Send to admin\n        const adminTemplate = {\n            to: EMAIL_CONFIG.adminEmail,\n            subject: `New Contact Form Submission - ${data.subject} | EBAM Motors`,\n            html: this.generateContactFormAdminHTML(data),\n            text: this.generateContactFormAdminText(data)\n        };\n        // Send confirmation to customer\n        const customerTemplate = {\n            to: data.email,\n            subject: `Thank you for contacting EBAM Motors`,\n            html: this.generateContactFormCustomerHTML(data),\n            text: this.generateContactFormCustomerText(data)\n        };\n        const [adminResult, customerResult] = await Promise.all([\n            this.sendEmail(adminTemplate),\n            this.sendEmail(customerTemplate)\n        ]);\n        return {\n            success: adminResult.success && customerResult.success,\n            error: adminResult.error || customerResult.error\n        };\n    }\n    /**\n   * Send automated follow-up email\n   */ async sendFollowUpEmail(data) {\n        try {\n            const isLeadFollowup = data.productInterest || data.leadSource;\n            const subject = isLeadFollowup ? `Following up on your ${data.productInterest || 'vehicle'} inquiry - EBAM Motors` : 'How are you enjoying your EBAM Motors experience?';\n            const template = {\n                to: data.to,\n                subject,\n                html: this.generateFollowUpHTML(data, isLeadFollowup),\n                text: this.generateFollowUpText(data, isLeadFollowup)\n            };\n            const result = await this.sendEmail(template);\n            console.log(`📧 Follow-up email sent to ${data.to}`);\n            return result;\n        } catch (error) {\n            console.error('Error sending follow-up email:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    /**\n   * Send admin notification\n   */ async sendAdminNotification(data) {\n        const template = {\n            to: EMAIL_CONFIG.adminEmail,\n            subject: `${data.title} | EBAM Motors Admin`,\n            html: this.generateAdminNotificationHTML(data),\n            text: this.generateAdminNotificationText(data)\n        };\n        const result = await this.sendEmail(template);\n        return result;\n    }\n    /**\n   * Send follow-up email\n   */ async sendFollowUpEmail(data) {\n        let subject = '';\n        let html = '';\n        let text = '';\n        switch(data.type){\n            case 'abandoned_cart':\n                subject = 'Complete Your Purchase - Items Still Available | EBAM Motors';\n                html = this.generateAbandonedCartHTML(data);\n                text = this.generateAbandonedCartText(data);\n                break;\n            case 'delivery_update':\n                subject = 'Delivery Update for Your Order | EBAM Motors';\n                html = this.generateDeliveryUpdateHTML(data);\n                text = this.generateDeliveryUpdateText(data);\n                break;\n            case 'feedback_request':\n                subject = 'How was your experience with EBAM Motors?';\n                html = this.generateFeedbackRequestHTML(data);\n                text = this.generateFeedbackRequestText(data);\n                break;\n            case 'maintenance_reminder':\n                subject = 'Vehicle Maintenance Reminder | EBAM Motors';\n                html = this.generateMaintenanceReminderHTML(data);\n                text = this.generateMaintenanceReminderText(data);\n                break;\n            default:\n                return {\n                    success: false,\n                    error: 'Unknown follow-up type'\n                };\n        }\n        const template = {\n            to: data.customerEmail,\n            subject,\n            html,\n            text\n        };\n        const result = await this.sendEmail(template);\n        return result;\n    }\n    // HTML template generators using EmailTemplates class\n    generateOrderConfirmationHTML(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateOrderConfirmationHTML(data);\n    }\n    generateOrderConfirmationText(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateOrderConfirmationText(data);\n    }\n    generateReviewNotificationHTML(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateReviewNotificationHTML(data);\n    }\n    generateReviewNotificationText(data) {\n        return `New Review from ${data.customerName} for ${data.vehicleTitle} - Rating: ${data.rating}/5`;\n    }\n    generateReviewApprovalHTML(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateReviewApprovalHTML(data);\n    }\n    generateReviewApprovalText(data) {\n        return `Your review for ${data.vehicleTitle} has been approved. Thank you ${data.customerName}!`;\n    }\n    generateContactFormAdminHTML(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateContactFormAdminHTML(data);\n    }\n    generateContactFormAdminText(data) {\n        return `New Contact Form from ${data.name} (${data.email}) - Subject: ${data.subject}`;\n    }\n    generateContactFormCustomerHTML(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateContactFormCustomerHTML(data);\n    }\n    generateContactFormCustomerText(data) {\n        return `Thank you for contacting EBAM Motors, ${data.name}. We received your message about \"${data.subject}\" and will respond within 24 hours.`;\n    }\n    generateAdminNotificationHTML(data) {\n        return `<h1>${data.title}</h1><p>${data.message}</p>`;\n    }\n    generateAdminNotificationText(data) {\n        return `${data.title}: ${data.message}`;\n    }\n    generateAbandonedCartHTML(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateAbandonedCartHTML(data);\n    }\n    generateAbandonedCartText(data) {\n        return `Hi ${data.customerName}! You left some amazing vehicles in your cart. Complete your purchase at EBAM Motors and get them shipped to Ghana. Don't miss out!`;\n    }\n    generateFollowUpHTML(data, isLeadFollowup) {\n        if (isLeadFollowup) {\n            return `\n        <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n          <div style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;\">\n            <h1 style=\"color: white; margin: 0; font-size: 28px;\">EBAM Motors</h1>\n            <p style=\"color: #f0f0f0; margin: 10px 0 0 0;\">Premium Japanese Cars for Ghana & Africa</p>\n          </div>\n\n          <div style=\"padding: 30px; background: #ffffff;\">\n            <h2 style=\"color: #333; margin-bottom: 20px;\">Hi ${data.customerName}!</h2>\n\n            <p style=\"color: #666; line-height: 1.6; margin-bottom: 20px;\">\n              I hope this email finds you well. I wanted to follow up on your recent inquiry about\n              <strong>${data.productInterest || 'our vehicles'}</strong>.\n            </p>\n\n            ${data.inquiryDetails ? `\n              <div style=\"background: #f8f9fa; padding: 15px; border-left: 4px solid #667eea; margin: 20px 0;\">\n                <p style=\"margin: 0; color: #555; font-style: italic;\">\"${data.inquiryDetails}\"</p>\n              </div>\n            ` : ''}\n\n            <p style=\"color: #666; line-height: 1.6; margin-bottom: 20px;\">\n              We have some excellent options that might interest you:\n            </p>\n\n            <ul style=\"color: #666; line-height: 1.8; margin-bottom: 25px;\">\n              <li>🚗 <strong>Premium Quality:</strong> All vehicles inspected and certified</li>\n              <li>📋 <strong>Complete Documentation:</strong> Full import paperwork handled</li>\n              <li>🚚 <strong>Delivery Service:</strong> Direct shipping to Ghana</li>\n              <li>💰 <strong>Competitive Pricing:</strong> Best value for Japanese imports</li>\n              <li>🛡️ <strong>Warranty Options:</strong> Peace of mind with every purchase</li>\n            </ul>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://websit-ebam1s-projects.vercel.app/en/stock\"\n                 style=\"background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;\">\n                View Our Latest Stock\n              </a>\n            </div>\n\n            <p style=\"color: #666; line-height: 1.6; margin-bottom: 20px;\">\n              Have any questions or need more information? I'm here to help! You can:\n            </p>\n\n            <div style=\"background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n              <p style=\"margin: 0 0 10px 0; color: #333;\"><strong>📞 Call/WhatsApp:</strong> +233245375692</p>\n              <p style=\"margin: 0 0 10px 0; color: #333;\"><strong>📧 Email:</strong> <EMAIL></p>\n              <p style=\"margin: 0; color: #333;\"><strong>🌐 Website:</strong> ebammotors.com</p>\n            </div>\n\n            <p style=\"color: #666; line-height: 1.6;\">\n              Thank you for considering EBAM Motors for your vehicle needs. We look forward to helping you find the perfect car!\n            </p>\n\n            <p style=\"color: #666; margin-top: 30px;\">\n              Best regards,<br>\n              <strong>EBAM Motors Team</strong><br>\n              <em>Your trusted partner for Japanese cars in Ghana</em>\n            </p>\n          </div>\n\n          <div style=\"background: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #eee;\">\n            <p style=\"color: #999; font-size: 12px; margin: 0;\">\n              This is an automated follow-up email. If you no longer wish to receive these emails, please contact us.\n            </p>\n          </div>\n        </div>\n      `;\n        } else {\n            return `\n        <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n          <div style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;\">\n            <h1 style=\"color: white; margin: 0; font-size: 28px;\">EBAM Motors</h1>\n            <p style=\"color: #f0f0f0; margin: 10px 0 0 0;\">Premium Japanese Cars for Ghana & Africa</p>\n          </div>\n\n          <div style=\"padding: 30px; background: #ffffff;\">\n            <h2 style=\"color: #333; margin-bottom: 20px;\">Hi ${data.customerName}!</h2>\n\n            <p style=\"color: #666; line-height: 1.6; margin-bottom: 20px;\">\n              I hope you're enjoying your experience with EBAM Motors! It's been a few days since we last connected,\n              and I wanted to check in to see how everything is going.\n            </p>\n\n            <p style=\"color: #666; line-height: 1.6; margin-bottom: 25px;\">\n              At EBAM Motors, your satisfaction is our top priority. Whether you have questions about:\n            </p>\n\n            <ul style=\"color: #666; line-height: 1.8; margin-bottom: 25px;\">\n              <li>🚗 Vehicle specifications or features</li>\n              <li>📋 Documentation and import process</li>\n              <li>🚚 Shipping and delivery updates</li>\n              <li>💰 Payment options or financing</li>\n              <li>🔧 Maintenance tips and recommendations</li>\n            </ul>\n\n            <p style=\"color: #666; line-height: 1.6; margin-bottom: 20px;\">\n              We're here to help! Don't hesitate to reach out if you need any assistance.\n            </p>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://websit-ebam1s-projects.vercel.app/en/stock\"\n                 style=\"background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;\">\n                Browse Our Latest Arrivals\n              </a>\n            </div>\n\n            <div style=\"background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n              <p style=\"margin: 0 0 10px 0; color: #333;\"><strong>📞 Call/WhatsApp:</strong> +233245375692</p>\n              <p style=\"margin: 0 0 10px 0; color: #333;\"><strong>📧 Email:</strong> <EMAIL></p>\n              <p style=\"margin: 0; color: #333;\"><strong>🌐 Website:</strong> ebammotors.com</p>\n            </div>\n\n            <p style=\"color: #666; line-height: 1.6;\">\n              Thank you for choosing EBAM Motors. We appreciate your business and look forward to serving you again!\n            </p>\n\n            <p style=\"color: #666; margin-top: 30px;\">\n              Best regards,<br>\n              <strong>EBAM Motors Team</strong><br>\n              <em>Your trusted partner for Japanese cars in Ghana</em>\n            </p>\n          </div>\n\n          <div style=\"background: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #eee;\">\n            <p style=\"color: #999; font-size: 12px; margin: 0;\">\n              This is an automated follow-up email. If you no longer wish to receive these emails, please contact us.\n            </p>\n          </div>\n        </div>\n      `;\n        }\n    }\n    generateFollowUpText(data, isLeadFollowup) {\n        if (isLeadFollowup) {\n            return `Hi ${data.customerName}! Following up on your inquiry about ${data.productInterest || 'our vehicles'}. We have excellent options available. Contact us at +233245375692 or visit ebammotors.com. - EBAM Motors`;\n        } else {\n            return `Hi ${data.customerName}! Hope you're enjoying your EBAM Motors experience. Need any assistance? We're here to help! Contact us at +233245375692. - EBAM Motors`;\n        }\n    }\n    generateDeliveryUpdateHTML(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateDeliveryUpdateHTML(data);\n    }\n    generateDeliveryUpdateText(data) {\n        return `Delivery update for ${data.customerName}: ${data.data?.status || 'Your vehicle is on its way'}. Track your order at ebammotors.com`;\n    }\n    generateFeedbackRequestHTML(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateFeedbackRequestHTML(data);\n    }\n    generateFeedbackRequestText(data) {\n        return `Hi ${data.customerName}! How was your experience with EBAM Motors? We'd love to hear your feedback. Leave a review at ebammotors.com/reviews`;\n    }\n    generateMaintenanceReminderHTML(data) {\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>Vehicle Maintenance Reminder</title>\n        <style>\n          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }\n          .container { max-width: 600px; margin: 0 auto; background-color: white; }\n          .header { background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); color: white; padding: 30px; text-align: center; }\n          .logo { font-size: 28px; font-weight: bold; margin-bottom: 10px; }\n          .content { padding: 30px; }\n          .highlight { background-color: #eff6ff; padding: 15px; border-radius: 6px; margin: 15px 0; }\n          .button { display: inline-block; background: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }\n          .footer { background-color: #f9fafb; padding: 20px; text-align: center; color: #6b7280; font-size: 14px; }\n        </style>\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors</div>\n            <p>Keep your vehicle in perfect condition</p>\n          </div>\n          <div class=\"content\">\n            <h1>Vehicle Maintenance Reminder</h1>\n            <p>Hi ${data.customerName},</p>\n            <p>It's time for your vehicle's scheduled maintenance to keep it running smoothly and safely.</p>\n            <div class=\"highlight\">\n              <h3>Recommended Maintenance</h3>\n              <p><strong>Vehicle:</strong> ${data.data?.vehicleTitle || 'Your vehicle'}</p>\n              <p><strong>Mileage:</strong> ${data.data?.currentMileage || 'Check your odometer'}</p>\n              <p><strong>Service Due:</strong> ${data.data?.serviceType || 'Regular maintenance'}</p>\n            </div>\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://yourdomain.com/contact\" class=\"button\">Schedule Service</a>\n            </div>\n            <p>Regular maintenance helps ensure:</p>\n            <ul>\n              <li>🔧 Optimal performance and fuel efficiency</li>\n              <li>🛡️ Safety and reliability</li>\n              <li>💰 Prevention of costly repairs</li>\n              <li>📈 Maintained resale value</li>\n            </ul>\n          </div>\n          <div class=\"footer\">\n            <p>Thank you for choosing EBAM Motors!</p>\n            <p>© 2024 EBAM Motors. All rights reserved.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    generateMaintenanceReminderText(data) {\n        return `Hi ${data.customerName}! It's time for your vehicle's scheduled maintenance. Contact EBAM Motors to schedule service and keep your vehicle running smoothly.`;\n    }\n}\n// Export singleton instance\nconst emailService = new ResendEmailService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/resendService.ts\n");

/***/ })

};
;