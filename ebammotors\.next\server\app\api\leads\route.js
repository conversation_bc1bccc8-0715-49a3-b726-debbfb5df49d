/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/leads/route";
exports.ids = ["app/api/leads/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fleads%2Froute&page=%2Fapi%2Fleads%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fleads%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fleads%2Froute&page=%2Fapi%2Fleads%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fleads%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_User_Desktop_website_ebammotors_src_app_api_leads_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/leads/route.ts */ \"(rsc)/./src/app/api/leads/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/leads/route\",\n        pathname: \"/api/leads\",\n        filename: \"route\",\n        bundlePath: \"app/api/leads/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\app\\\\api\\\\leads\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_User_Desktop_website_ebammotors_src_app_api_leads_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fleads%2Froute&page=%2Fapi%2Fleads%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fleads%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/leads/route.ts":
/*!************************************!*\
  !*** ./src/app/api/leads/route.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_crmStorage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/crmStorage */ \"(rsc)/./src/lib/crmStorage.ts\");\n/* harmony import */ var _lib_adminNotifications__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/adminNotifications */ \"(rsc)/./src/lib/adminNotifications.ts\");\n\n\n\n// GET - Fetch leads\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const adminKey = searchParams.get('adminKey');\n        const leadId = searchParams.get('id');\n        const status = searchParams.get('status');\n        const source = searchParams.get('source');\n        // Verify admin authentication\n        const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';\n        if (adminKey !== validAdminKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // Get specific lead\n        if (leadId) {\n            const lead = await (0,_lib_crmStorage__WEBPACK_IMPORTED_MODULE_1__.getLeadById)(leadId);\n            if (!lead) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    message: 'Lead not found'\n                }, {\n                    status: 404\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                lead\n            });\n        }\n        // Get leads by status\n        if (status) {\n            const leads = await (0,_lib_crmStorage__WEBPACK_IMPORTED_MODULE_1__.getLeadsByStatus)(status);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                leads\n            });\n        }\n        // Get leads by source\n        if (source) {\n            const leads = await (0,_lib_crmStorage__WEBPACK_IMPORTED_MODULE_1__.getLeadsBySource)(source);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                leads\n            });\n        }\n        // Get all leads\n        const leads = await (0,_lib_crmStorage__WEBPACK_IMPORTED_MODULE_1__.getAllLeads)();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            leads\n        });\n    } catch (error) {\n        console.error('Error fetching leads:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Failed to fetch leads'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - Create new lead\nasync function POST(request) {\n    try {\n        const leadData = await request.json();\n        // Validate required fields\n        if (!leadData.customerInfo?.name || !leadData.inquiry?.message) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Name and message are required'\n            }, {\n                status: 400\n            });\n        }\n        // Set defaults\n        const newLeadData = {\n            source: 'manual',\n            status: 'new',\n            priority: 'medium',\n            tags: [],\n            ...leadData\n        };\n        const lead = await (0,_lib_crmStorage__WEBPACK_IMPORTED_MODULE_1__.createLead)(newLeadData);\n        // Log interaction for lead creation\n        const { createInteraction } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/crmStorage */ \"(rsc)/./src/lib/crmStorage.ts\"));\n        await createInteraction({\n            leadId: lead.id,\n            type: 'chat',\n            direction: 'inbound',\n            channel: leadData.source === 'chatbot' ? 'website' : 'manual',\n            content: leadData.inquiry.message,\n            subject: leadData.inquiry.subject || 'New Lead Inquiry',\n            tags: [\n                'lead_creation'\n            ],\n            createdBy: 'system'\n        });\n        // Send admin notification for new lead\n        try {\n            await (0,_lib_adminNotifications__WEBPACK_IMPORTED_MODULE_2__.notifyAdminNewLead)({\n                leadId: lead.id,\n                customerName: lead.customerInfo.name,\n                customerEmail: lead.customerInfo.email || 'No email provided',\n                source: lead.source,\n                priority: lead.priority,\n                inquiry: lead.inquiry.message\n            });\n        } catch (emailError) {\n            console.error('Failed to send admin lead notification:', emailError);\n        // Don't fail the lead creation if email fails\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Lead created successfully',\n            lead\n        });\n    } catch (error) {\n        console.error('Error creating lead:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Failed to create lead'\n        }, {\n            status: 500\n        });\n    }\n}\n// PATCH - Update lead\nasync function PATCH(request) {\n    try {\n        const { leadId, adminKey, ...updates } = await request.json();\n        // Verify admin authentication\n        const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';\n        if (adminKey !== validAdminKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        if (!leadId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Lead ID is required'\n            }, {\n                status: 400\n            });\n        }\n        const success = await (0,_lib_crmStorage__WEBPACK_IMPORTED_MODULE_1__.updateLead)(leadId, updates);\n        if (!success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Lead not found'\n            }, {\n                status: 404\n            });\n        }\n        // Log interaction for lead update\n        const { createInteraction } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/crmStorage */ \"(rsc)/./src/lib/crmStorage.ts\"));\n        await createInteraction({\n            leadId,\n            type: 'support',\n            direction: 'outbound',\n            channel: 'website',\n            content: `Lead updated: ${Object.keys(updates).join(', ')}`,\n            subject: 'Lead Status Update',\n            tags: [\n                'lead_update',\n                'admin_action'\n            ],\n            createdBy: 'admin'\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Lead updated successfully'\n        });\n    } catch (error) {\n        console.error('Error updating lead:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Failed to update lead'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - Delete lead\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const leadId = searchParams.get('id');\n        const adminKey = searchParams.get('adminKey');\n        // Verify admin authentication\n        const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';\n        if (adminKey !== validAdminKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        if (!leadId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Lead ID is required'\n            }, {\n                status: 400\n            });\n        }\n        const success = await (0,_lib_crmStorage__WEBPACK_IMPORTED_MODULE_1__.deleteLead)(leadId);\n        if (!success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Lead not found'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Lead deleted successfully'\n        });\n    } catch (error) {\n        console.error('Error deleting lead:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Failed to delete lead'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/leads/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/adminNotifications.ts":
/*!***************************************!*\
  !*** ./src/lib/adminNotifications.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   notifyAdminContactForm: () => (/* binding */ notifyAdminContactForm),\n/* harmony export */   notifyAdminFailedPayment: () => (/* binding */ notifyAdminFailedPayment),\n/* harmony export */   notifyAdminLowStock: () => (/* binding */ notifyAdminLowStock),\n/* harmony export */   notifyAdminNewLead: () => (/* binding */ notifyAdminNewLead),\n/* harmony export */   notifyAdminNewOrder: () => (/* binding */ notifyAdminNewOrder),\n/* harmony export */   notifyAdminNewReview: () => (/* binding */ notifyAdminNewReview),\n/* harmony export */   notifyAdminShippingUpdate: () => (/* binding */ notifyAdminShippingUpdate),\n/* harmony export */   notifyAdminSystemAlert: () => (/* binding */ notifyAdminSystemAlert),\n/* harmony export */   sendDailySummaryToAdmin: () => (/* binding */ sendDailySummaryToAdmin)\n/* harmony export */ });\n/* harmony import */ var _resendService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./resendService */ \"(rsc)/./src/lib/resendService.ts\");\n\n/**\n * Send admin notification for new orders\n */ async function notifyAdminNewOrder(orderData) {\n    const notification = {\n        type: 'new_order',\n        title: 'New Order Received',\n        message: `Order ${orderData.orderId} has been paid and confirmed. Customer: ${orderData.customerName} (${orderData.customerEmail}). Vehicle: ${orderData.vehicleTitle}. Amount: ¥${orderData.amount.toLocaleString()}`,\n        data: orderData,\n        timestamp: new Date().toISOString()\n    };\n    try {\n        await _resendService__WEBPACK_IMPORTED_MODULE_0__.emailService.sendAdminNotification(notification);\n        console.log('Admin new order notification sent successfully');\n    } catch (error) {\n        console.error('Failed to send admin new order notification:', error);\n    }\n}\n/**\n * Send admin notification for new reviews\n */ async function notifyAdminNewReview(reviewData) {\n    const notification = {\n        type: 'new_review',\n        title: 'New Review Submitted',\n        message: `New review submitted by ${reviewData.customerName} for ${reviewData.vehicleTitle}. Rating: ${reviewData.rating}/5 stars. Review ID: ${reviewData.reviewId}`,\n        data: reviewData,\n        timestamp: new Date().toISOString()\n    };\n    try {\n        await _resendService__WEBPACK_IMPORTED_MODULE_0__.emailService.sendAdminNotification(notification);\n        console.log('Admin new review notification sent successfully');\n    } catch (error) {\n        console.error('Failed to send admin new review notification:', error);\n    }\n}\n/**\n * Send admin notification for contact form submissions\n */ async function notifyAdminContactForm(contactData) {\n    const notification = {\n        type: 'contact_form',\n        title: 'New Contact Form Submission',\n        message: `New contact form submitted by ${contactData.name} (${contactData.email}). Subject: ${contactData.subject}`,\n        data: contactData,\n        timestamp: new Date().toISOString()\n    };\n    try {\n        await _resendService__WEBPACK_IMPORTED_MODULE_0__.emailService.sendAdminNotification(notification);\n        console.log('Admin contact form notification sent successfully');\n    } catch (error) {\n        console.error('Failed to send admin contact form notification:', error);\n    }\n}\n/**\n * Send admin notification for low stock alerts\n */ async function notifyAdminLowStock(stockData) {\n    const notification = {\n        type: 'low_stock',\n        title: 'Low Stock Alert',\n        message: `Low stock alert for ${stockData.vehicleTitle} (ID: ${stockData.vehicleId}). Current stock: ${stockData.currentStock}, Threshold: ${stockData.threshold}`,\n        data: stockData,\n        timestamp: new Date().toISOString()\n    };\n    try {\n        await _resendService__WEBPACK_IMPORTED_MODULE_0__.emailService.sendAdminNotification(notification);\n        console.log('Admin low stock notification sent successfully');\n    } catch (error) {\n        console.error('Failed to send admin low stock notification:', error);\n    }\n}\n/**\n * Send admin notification for system alerts\n */ async function notifyAdminSystemAlert(alertData) {\n    const notification = {\n        type: 'system_alert',\n        title: `System Alert - ${alertData.alertType}`,\n        message: `${alertData.severity.toUpperCase()} severity alert: ${alertData.message}`,\n        data: alertData,\n        timestamp: new Date().toISOString()\n    };\n    try {\n        await _resendService__WEBPACK_IMPORTED_MODULE_0__.emailService.sendAdminNotification(notification);\n        console.log('Admin system alert notification sent successfully');\n    } catch (error) {\n        console.error('Failed to send admin system alert notification:', error);\n    }\n}\n/**\n * Send admin notification for failed payments\n */ async function notifyAdminFailedPayment(paymentData) {\n    const notification = {\n        type: 'system_alert',\n        title: 'Payment Failed',\n        message: `Payment failed for order ${paymentData.orderId}. Customer: ${paymentData.customerName} (${paymentData.customerEmail}). Amount: ¥${paymentData.amount.toLocaleString()}. Error: ${paymentData.errorMessage}`,\n        data: paymentData,\n        timestamp: new Date().toISOString()\n    };\n    try {\n        await _resendService__WEBPACK_IMPORTED_MODULE_0__.emailService.sendAdminNotification(notification);\n        console.log('Admin failed payment notification sent successfully');\n    } catch (error) {\n        console.error('Failed to send admin failed payment notification:', error);\n    }\n}\n/**\n * Send admin notification for new leads from chatbot\n */ async function notifyAdminNewLead(leadData) {\n    const notification = {\n        type: 'new_review',\n        title: 'New Lead Generated',\n        message: `New lead generated from ${leadData.source}. Customer: ${leadData.customerName} (${leadData.customerEmail}). Priority: ${leadData.priority}. Lead ID: ${leadData.leadId}`,\n        data: leadData,\n        timestamp: new Date().toISOString()\n    };\n    try {\n        await _resendService__WEBPACK_IMPORTED_MODULE_0__.emailService.sendAdminNotification(notification);\n        console.log('Admin new lead notification sent successfully');\n    } catch (error) {\n        console.error('Failed to send admin new lead notification:', error);\n    }\n}\n/**\n * Send admin notification for shipping updates\n */ async function notifyAdminShippingUpdate(shippingData) {\n    const notification = {\n        type: 'system_alert',\n        title: 'Shipping Update Required',\n        message: `Shipping update for order ${shippingData.orderId}. Customer: ${shippingData.customerName}. Status: ${shippingData.status}. Location: ${shippingData.location}`,\n        data: shippingData,\n        timestamp: new Date().toISOString()\n    };\n    try {\n        await _resendService__WEBPACK_IMPORTED_MODULE_0__.emailService.sendAdminNotification(notification);\n        console.log('Admin shipping update notification sent successfully');\n    } catch (error) {\n        console.error('Failed to send admin shipping update notification:', error);\n    }\n}\n/**\n * Send daily summary notification to admin\n */ async function sendDailySummaryToAdmin(summaryData) {\n    const notification = {\n        type: 'system_alert',\n        title: 'Daily Summary Report',\n        message: `Daily summary for ${summaryData.date}: ${summaryData.newOrders} new orders, ¥${summaryData.totalRevenue.toLocaleString()} revenue, ${summaryData.newReviews} reviews, ${summaryData.newLeads} leads, ${summaryData.contactForms} contact forms, ${summaryData.pendingTasks} pending tasks.`,\n        data: summaryData,\n        timestamp: new Date().toISOString()\n    };\n    try {\n        await _resendService__WEBPACK_IMPORTED_MODULE_0__.emailService.sendAdminNotification(notification);\n        console.log('Daily summary notification sent successfully');\n    } catch (error) {\n        console.error('Failed to send daily summary notification:', error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/adminNotifications.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/crmStorage.ts":
/*!*******************************!*\
  !*** ./src/lib/crmStorage.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCustomerActivity: () => (/* binding */ createCustomerActivity),\n/* harmony export */   createFollowUp: () => (/* binding */ createFollowUp),\n/* harmony export */   createInteraction: () => (/* binding */ createInteraction),\n/* harmony export */   createLead: () => (/* binding */ createLead),\n/* harmony export */   deleteLead: () => (/* binding */ deleteLead),\n/* harmony export */   getActivitiesByCustomerId: () => (/* binding */ getActivitiesByCustomerId),\n/* harmony export */   getAllCustomers: () => (/* binding */ getAllCustomers),\n/* harmony export */   getAllFollowUps: () => (/* binding */ getAllFollowUps),\n/* harmony export */   getAllInteractions: () => (/* binding */ getAllInteractions),\n/* harmony export */   getAllLeads: () => (/* binding */ getAllLeads),\n/* harmony export */   getCustomerByEmail: () => (/* binding */ getCustomerByEmail),\n/* harmony export */   getCustomerById: () => (/* binding */ getCustomerById),\n/* harmony export */   getCustomerOverview: () => (/* binding */ getCustomerOverview),\n/* harmony export */   getFollowUpById: () => (/* binding */ getFollowUpById),\n/* harmony export */   getFollowUpsByCustomerId: () => (/* binding */ getFollowUpsByCustomerId),\n/* harmony export */   getFollowUpsByStatus: () => (/* binding */ getFollowUpsByStatus),\n/* harmony export */   getInteractionsByCustomerId: () => (/* binding */ getInteractionsByCustomerId),\n/* harmony export */   getInteractionsByLeadId: () => (/* binding */ getInteractionsByLeadId),\n/* harmony export */   getLeadById: () => (/* binding */ getLeadById),\n/* harmony export */   getLeadsBySource: () => (/* binding */ getLeadsBySource),\n/* harmony export */   getLeadsByStatus: () => (/* binding */ getLeadsByStatus),\n/* harmony export */   getPendingFollowUps: () => (/* binding */ getPendingFollowUps),\n/* harmony export */   getRecentActivities: () => (/* binding */ getRecentActivities),\n/* harmony export */   updateCustomer: () => (/* binding */ updateCustomer),\n/* harmony export */   updateFollowUp: () => (/* binding */ updateFollowUp),\n/* harmony export */   updateLead: () => (/* binding */ updateLead),\n/* harmony export */   upsertCustomer: () => (/* binding */ upsertCustomer)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/esm/v4.js\");\n\n\n\n// File paths\nconst DATA_DIR = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'data');\nconst LEADS_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DATA_DIR, 'leads.json');\nconst CUSTOMERS_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DATA_DIR, 'customers.json');\nconst INTERACTIONS_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DATA_DIR, 'interactions.json');\nconst FOLLOWUPS_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DATA_DIR, 'followups.json');\nconst ACTIVITIES_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DATA_DIR, 'activities.json');\n// Check if running in serverless environment\nconst isServerless = process.env.VERCEL || process.env.NETLIFY || process.env.AWS_LAMBDA_FUNCTION_NAME;\n// In-memory storage for serverless environments\nlet leadsMemoryStore = [];\nlet customersMemoryStore = [];\nlet interactionsMemoryStore = [];\nlet followupsMemoryStore = [];\nlet activitiesMemoryStore = [];\n/**\n * Ensure data directory exists\n */ async function ensureDataDirectory() {\n    if (isServerless) return;\n    try {\n        await fs__WEBPACK_IMPORTED_MODULE_0__.promises.access(DATA_DIR);\n    } catch  {\n        await fs__WEBPACK_IMPORTED_MODULE_0__.promises.mkdir(DATA_DIR, {\n            recursive: true\n        });\n    }\n}\n// LEADS MANAGEMENT\n/**\n * Load leads from storage\n */ async function loadLeads() {\n    if (isServerless) {\n        return leadsMemoryStore;\n    }\n    try {\n        await ensureDataDirectory();\n        const data = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.readFile(LEADS_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        return [];\n    }\n}\n/**\n * Save leads to storage\n */ async function saveLeads(leads) {\n    if (isServerless) {\n        leadsMemoryStore = leads;\n        return;\n    }\n    await ensureDataDirectory();\n    await fs__WEBPACK_IMPORTED_MODULE_0__.promises.writeFile(LEADS_FILE, JSON.stringify(leads, null, 2));\n}\n/**\n * Create a new lead\n */ async function createLead(leadData) {\n    const leads = await loadLeads();\n    const newLead = {\n        ...leadData,\n        id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    };\n    leads.push(newLead);\n    await saveLeads(leads);\n    return newLead;\n}\n/**\n * Get all leads\n */ async function getAllLeads() {\n    return await loadLeads();\n}\n/**\n * Get lead by ID\n */ async function getLeadById(leadId) {\n    const leads = await loadLeads();\n    return leads.find((lead)=>lead.id === leadId) || null;\n}\n/**\n * Update lead\n */ async function updateLead(leadId, updates) {\n    const leads = await loadLeads();\n    const leadIndex = leads.findIndex((lead)=>lead.id === leadId);\n    if (leadIndex === -1) return false;\n    leads[leadIndex] = {\n        ...leads[leadIndex],\n        ...updates,\n        updatedAt: new Date().toISOString()\n    };\n    await saveLeads(leads);\n    return true;\n}\n/**\n * Delete lead\n */ async function deleteLead(leadId) {\n    const leads = await loadLeads();\n    const filteredLeads = leads.filter((lead)=>lead.id !== leadId);\n    if (filteredLeads.length === leads.length) return false;\n    await saveLeads(filteredLeads);\n    return true;\n}\n/**\n * Get leads by status\n */ async function getLeadsByStatus(status) {\n    const leads = await loadLeads();\n    return leads.filter((lead)=>lead.status === status);\n}\n/**\n * Get leads by source\n */ async function getLeadsBySource(source) {\n    const leads = await loadLeads();\n    return leads.filter((lead)=>lead.source === source);\n}\n// CUSTOMERS MANAGEMENT\n/**\n * Load customers from storage\n */ async function loadCustomers() {\n    if (isServerless) {\n        return customersMemoryStore;\n    }\n    try {\n        await ensureDataDirectory();\n        const data = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.readFile(CUSTOMERS_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        return [];\n    }\n}\n/**\n * Save customers to storage\n */ async function saveCustomers(customers) {\n    if (isServerless) {\n        customersMemoryStore = customers;\n        return;\n    }\n    await ensureDataDirectory();\n    await fs__WEBPACK_IMPORTED_MODULE_0__.promises.writeFile(CUSTOMERS_FILE, JSON.stringify(customers, null, 2));\n}\n/**\n * Create or update customer\n */ async function upsertCustomer(customerData) {\n    const customers = await loadCustomers();\n    // Check if customer exists by email\n    const existingCustomerIndex = customers.findIndex((c)=>c.personalInfo.email === customerData.personalInfo.email);\n    if (existingCustomerIndex !== -1) {\n        // Update existing customer\n        customers[existingCustomerIndex] = {\n            ...customers[existingCustomerIndex],\n            ...customerData,\n            updatedAt: new Date().toISOString()\n        };\n        await saveCustomers(customers);\n        return customers[existingCustomerIndex];\n    } else {\n        // Create new customer\n        const newCustomer = {\n            ...customerData,\n            id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        customers.push(newCustomer);\n        await saveCustomers(customers);\n        return newCustomer;\n    }\n}\n/**\n * Get all customers\n */ async function getAllCustomers() {\n    return await loadCustomers();\n}\n/**\n * Get customer by ID\n */ async function getCustomerById(customerId) {\n    const customers = await loadCustomers();\n    return customers.find((customer)=>customer.id === customerId) || null;\n}\n/**\n * Get customer by email\n */ async function getCustomerByEmail(email) {\n    const customers = await loadCustomers();\n    return customers.find((customer)=>customer.personalInfo.email === email) || null;\n}\n/**\n * Update customer\n */ async function updateCustomer(customerId, updates) {\n    const customers = await loadCustomers();\n    const customerIndex = customers.findIndex((customer)=>customer.id === customerId);\n    if (customerIndex === -1) return false;\n    customers[customerIndex] = {\n        ...customers[customerIndex],\n        ...updates,\n        updatedAt: new Date().toISOString()\n    };\n    await saveCustomers(customers);\n    return true;\n}\n// INTERACTIONS MANAGEMENT\n/**\n * Load interactions from storage\n */ async function loadInteractions() {\n    if (isServerless) {\n        return interactionsMemoryStore;\n    }\n    try {\n        await ensureDataDirectory();\n        const data = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.readFile(INTERACTIONS_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        return [];\n    }\n}\n/**\n * Save interactions to storage\n */ async function saveInteractions(interactions) {\n    if (isServerless) {\n        interactionsMemoryStore = interactions;\n        return;\n    }\n    await ensureDataDirectory();\n    await fs__WEBPACK_IMPORTED_MODULE_0__.promises.writeFile(INTERACTIONS_FILE, JSON.stringify(interactions, null, 2));\n}\n/**\n * Create a new interaction\n */ async function createInteraction(interactionData) {\n    const interactions = await loadInteractions();\n    const newInteraction = {\n        ...interactionData,\n        id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n        createdAt: new Date().toISOString()\n    };\n    interactions.push(newInteraction);\n    await saveInteractions(interactions);\n    return newInteraction;\n}\n/**\n * Get all interactions\n */ async function getAllInteractions() {\n    return await loadInteractions();\n}\n/**\n * Get interactions by customer ID\n */ async function getInteractionsByCustomerId(customerId) {\n    const interactions = await loadInteractions();\n    return interactions.filter((interaction)=>interaction.customerId === customerId);\n}\n/**\n * Get interactions by lead ID\n */ async function getInteractionsByLeadId(leadId) {\n    const interactions = await loadInteractions();\n    return interactions.filter((interaction)=>interaction.leadId === leadId);\n}\n// FOLLOW-UPS MANAGEMENT\n/**\n * Load follow-ups from storage\n */ async function loadFollowUps() {\n    if (isServerless) {\n        return followupsMemoryStore;\n    }\n    try {\n        await ensureDataDirectory();\n        const data = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.readFile(FOLLOWUPS_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        return [];\n    }\n}\n/**\n * Save follow-ups to storage\n */ async function saveFollowUps(followups) {\n    if (isServerless) {\n        followupsMemoryStore = followups;\n        return;\n    }\n    await ensureDataDirectory();\n    await fs__WEBPACK_IMPORTED_MODULE_0__.promises.writeFile(FOLLOWUPS_FILE, JSON.stringify(followups, null, 2));\n}\n/**\n * Create a new follow-up\n */ async function createFollowUp(followupData) {\n    const followups = await loadFollowUps();\n    const newFollowUp = {\n        ...followupData,\n        id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    };\n    followups.push(newFollowUp);\n    await saveFollowUps(followups);\n    return newFollowUp;\n}\n/**\n * Get all follow-ups\n */ async function getAllFollowUps() {\n    return await loadFollowUps();\n}\n/**\n * Get follow-up by ID\n */ async function getFollowUpById(id) {\n    const followups = await loadFollowUps();\n    return followups.find((f)=>f.id === id) || null;\n}\n/**\n * Get follow-ups by status\n */ async function getFollowUpsByStatus(status) {\n    const followups = await loadFollowUps();\n    return followups.filter((followup)=>followup.status === status);\n}\n/**\n * Get pending follow-ups (due now or overdue)\n */ async function getPendingFollowUps() {\n    const followups = await loadFollowUps();\n    const now = new Date().toISOString();\n    return followups.filter((followup)=>followup.status === 'pending' && followup.scheduledDate <= now);\n}\n/**\n * Update follow-up\n */ async function updateFollowUp(followupId, updates) {\n    const followups = await loadFollowUps();\n    const followupIndex = followups.findIndex((followup)=>followup.id === followupId);\n    if (followupIndex === -1) return false;\n    followups[followupIndex] = {\n        ...followups[followupIndex],\n        ...updates,\n        updatedAt: new Date().toISOString()\n    };\n    await saveFollowUps(followups);\n    return true;\n}\n/**\n * Get follow-ups by customer ID\n */ async function getFollowUpsByCustomerId(customerId) {\n    const followups = await loadFollowUps();\n    return followups.filter((followup)=>followup.customerId === customerId);\n}\n// CUSTOMER ACTIVITIES MANAGEMENT\n/**\n * Load activities from storage\n */ async function loadActivities() {\n    if (isServerless) {\n        return activitiesMemoryStore;\n    }\n    try {\n        await ensureDataDirectory();\n        const data = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.readFile(ACTIVITIES_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        return [];\n    }\n}\n/**\n * Save activities to storage\n */ async function saveActivities(activities) {\n    if (isServerless) {\n        activitiesMemoryStore = activities;\n        return;\n    }\n    await ensureDataDirectory();\n    await fs__WEBPACK_IMPORTED_MODULE_0__.promises.writeFile(ACTIVITIES_FILE, JSON.stringify(activities, null, 2));\n}\n/**\n * Create a new customer activity\n */ async function createCustomerActivity(activityData) {\n    const activities = await loadActivities();\n    const newActivity = {\n        ...activityData,\n        id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n        timestamp: new Date().toISOString()\n    };\n    activities.push(newActivity);\n    await saveActivities(activities);\n    return newActivity;\n}\n/**\n * Get activities by customer ID\n */ async function getActivitiesByCustomerId(customerId) {\n    const activities = await loadActivities();\n    return activities.filter((activity)=>activity.customerId === customerId);\n}\n/**\n * Get recent activities (last 30 days)\n */ async function getRecentActivities(days = 30) {\n    const activities = await loadActivities();\n    const cutoffDate = new Date();\n    cutoffDate.setDate(cutoffDate.getDate() - days);\n    return activities.filter((activity)=>new Date(activity.timestamp) >= cutoffDate).sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());\n}\n// UTILITY FUNCTIONS\n/**\n * Get customer overview with stats\n */ async function getCustomerOverview(customerId) {\n    const customer = await getCustomerById(customerId);\n    if (!customer) return null;\n    const interactions = await getInteractionsByCustomerId(customerId);\n    const followups = await getFollowUpsByCustomerId(customerId);\n    const activities = await getActivitiesByCustomerId(customerId);\n    return {\n        customer,\n        stats: {\n            totalInteractions: interactions.length,\n            pendingFollowUps: followups.filter((f)=>f.status === 'pending').length,\n            recentActivities: activities.filter((a)=>new Date(a.timestamp) >= new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)).length,\n            lastInteraction: interactions.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())[0]?.createdAt\n        },\n        recentInteractions: interactions.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()).slice(0, 5),\n        upcomingFollowUps: followups.filter((f)=>f.status === 'pending').sort((a, b)=>new Date(a.scheduledDate).getTime() - new Date(b.scheduledDate).getTime()).slice(0, 3)\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/crmStorage.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/emailTemplates.ts":
/*!***********************************!*\
  !*** ./src/lib/emailTemplates.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmailTemplates: () => (/* binding */ EmailTemplates)\n/* harmony export */ });\n// Base email styles\nconst emailStyles = `\n  <style>\n    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }\n    .container { max-width: 600px; margin: 0 auto; background-color: white; }\n    .header { background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); color: white; padding: 30px; text-align: center; }\n    .logo { font-size: 28px; font-weight: bold; margin-bottom: 10px; }\n    .content { padding: 30px; }\n    .vehicle-card { border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; margin: 20px 0; }\n    .button { display: inline-block; background: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }\n    .footer { background-color: #f9fafb; padding: 20px; text-align: center; color: #6b7280; font-size: 14px; }\n    .highlight { background-color: #eff6ff; padding: 15px; border-radius: 6px; margin: 15px 0; }\n    .status-badge { display: inline-block; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: bold; }\n    .status-confirmed { background-color: #dcfce7; color: #166534; }\n    .divider { height: 1px; background-color: #e5e7eb; margin: 20px 0; }\n  </style>\n`;\nclass EmailTemplates {\n    /**\n   * Generate Order Confirmation HTML\n   */ static generateOrderConfirmationHTML(data) {\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>Order Confirmation - ${data.orderNumber}</title>\n        ${emailStyles}\n      </head>\n      <body>\n        <div class=\"container\">\n          <!-- Header -->\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors</div>\n            <p>Your trusted partner for quality vehicles from Japan to Ghana</p>\n          </div>\n\n          <!-- Content -->\n          <div class=\"content\">\n            <h1>Order Confirmation</h1>\n            <p>Dear ${data.customerName},</p>\n            <p>Thank you for your order! We're excited to help you get your new vehicle.</p>\n\n            <div class=\"highlight\">\n              <h3>Order Details</h3>\n              <p><strong>Order Number:</strong> ${data.orderNumber}</p>\n              <p><strong>Order Date:</strong> ${data.orderDate}</p>\n              <p><strong>Status:</strong> <span class=\"status-badge status-confirmed\">Confirmed</span></p>\n            </div>\n\n            <!-- Vehicle Details -->\n            <div class=\"vehicle-card\">\n              <h3>Vehicle Information</h3>\n              <img src=\"${data.vehicle.image}\" alt=\"${data.vehicle.title}\" style=\"width: 100%; max-width: 300px; height: 200px; object-fit: cover; border-radius: 6px; margin-bottom: 15px;\">\n              <h4>${data.vehicle.title}</h4>\n              <p><strong>Price:</strong> ${data.vehicle.price}</p>\n            </div>\n\n            <!-- Shipping Information -->\n            <div class=\"highlight\">\n              <h3>Shipping Address</h3>\n              <p>\n                ${data.shippingAddress.street}<br>\n                ${data.shippingAddress.city}, ${data.shippingAddress.state}<br>\n                ${data.shippingAddress.country} ${data.shippingAddress.postalCode}\n              </p>\n              <p><strong>Estimated Delivery:</strong> ${data.estimatedDelivery}</p>\n            </div>\n\n            <!-- Order Summary -->\n            <div class=\"divider\"></div>\n            <div style=\"text-align: right;\">\n              <h3>Order Total: ${data.total}</h3>\n            </div>\n\n            <!-- Next Steps -->\n            <div class=\"highlight\">\n              <h3>What's Next?</h3>\n              <ul>\n                <li>We'll prepare your vehicle for shipping</li>\n                <li>You'll receive tracking information once shipped</li>\n                <li>Our team will contact you for any updates</li>\n              </ul>\n            </div>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://yourdomain.com/tracking?order=${data.orderNumber}\" class=\"button\">Track Your Order</a>\n            </div>\n\n            <p>If you have any questions, please don't hesitate to contact us:</p>\n            <ul>\n              <li>📧 Email: <EMAIL></li>\n              <li>📱 WhatsApp: +233245375692</li>\n              <li>📍 Location: Kumasi, Ghana</li>\n            </ul>\n          </div>\n\n          <!-- Footer -->\n          <div class=\"footer\">\n            <p>Thank you for choosing EBAM Motors!</p>\n            <p>© 2024 EBAM Motors. All rights reserved.</p>\n            <p>This email was sent to confirm your order. Please keep this for your records.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    /**\n   * Generate Order Confirmation Text\n   */ static generateOrderConfirmationText(data) {\n        return `\nORDER CONFIRMATION - EBAM Motors\n\nDear ${data.customerName},\n\nThank you for your order! We're excited to help you get your new vehicle.\n\nORDER DETAILS:\n- Order Number: ${data.orderNumber}\n- Order Date: ${data.orderDate}\n- Status: Confirmed\n\nVEHICLE:\n- ${data.vehicle.title}\n- Price: ${data.vehicle.price}\n\nSHIPPING ADDRESS:\n${data.shippingAddress.street}\n${data.shippingAddress.city}, ${data.shippingAddress.state}\n${data.shippingAddress.country} ${data.shippingAddress.postalCode}\n\nEstimated Delivery: ${data.estimatedDelivery}\n\nORDER TOTAL: ${data.total}\n\nWHAT'S NEXT:\n- We'll prepare your vehicle for shipping\n- You'll receive tracking information once shipped\n- Our team will contact you for any updates\n\nTrack your order: https://yourdomain.com/tracking?order=${data.orderNumber}\n\nCONTACT US:\n- Email: <EMAIL>\n- WhatsApp: +233245375692\n- Location: Kumasi, Ghana\n\nThank you for choosing EBAM Motors!\n© 2024 EBAM Motors. All rights reserved.\n    `;\n    }\n    /**\n   * Generate Review Notification HTML for Admin\n   */ static generateReviewNotificationHTML(data) {\n        const stars = '⭐'.repeat(data.rating) + '☆'.repeat(5 - data.rating);\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>New Review Submitted</title>\n        ${emailStyles}\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors Admin</div>\n            <p>New Review Notification</p>\n          </div>\n\n          <div class=\"content\">\n            <h1>New Review Submitted</h1>\n            \n            <div class=\"highlight\">\n              <h3>Review Details</h3>\n              <p><strong>Customer:</strong> ${data.customerName}</p>\n              <p><strong>Vehicle:</strong> ${data.vehicleTitle}</p>\n              <p><strong>Rating:</strong> ${stars} (${data.rating}/5)</p>\n              <p><strong>Date:</strong> ${data.reviewDate}</p>\n            </div>\n\n            <div class=\"vehicle-card\">\n              <h3>Review Content</h3>\n              <p>\"${data.review}\"</p>\n            </div>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://yourdomain.com/admin/reviews\" class=\"button\">Review & Approve</a>\n            </div>\n          </div>\n\n          <div class=\"footer\">\n            <p>EBAM Motors Admin Panel</p>\n            <p>Please review and approve/reject this review in the admin panel.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    /**\n   * Generate Review Approval HTML for Customer\n   */ static generateReviewApprovalHTML(data) {\n        const stars = '⭐'.repeat(data.rating) + '☆'.repeat(5 - data.rating);\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>Review Approved</title>\n        ${emailStyles}\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors</div>\n            <p>Thank you for your feedback!</p>\n          </div>\n\n          <div class=\"content\">\n            <h1>Your Review Has Been Approved!</h1>\n            <p>Dear ${data.customerName},</p>\n            <p>Thank you for taking the time to review your experience with us. Your review has been approved and is now live on our website!</p>\n\n            <div class=\"highlight\">\n              <h3>Your Review</h3>\n              <p><strong>Vehicle:</strong> ${data.vehicleTitle}</p>\n              <p><strong>Rating:</strong> ${stars} (${data.rating}/5)</p>\n              <p><strong>Review:</strong> \"${data.review}\"</p>\n            </div>\n\n            <p>Your feedback helps other customers make informed decisions and helps us improve our services.</p>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://yourdomain.com/reviews\" class=\"button\">View All Reviews</a>\n            </div>\n          </div>\n\n          <div class=\"footer\">\n            <p>Thank you for choosing EBAM Motors!</p>\n            <p>© 2024 EBAM Motors. All rights reserved.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    /**\n   * Generate Contact Form Admin Notification HTML\n   */ static generateContactFormAdminHTML(data) {\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>New Contact Form Submission</title>\n        ${emailStyles}\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors Admin</div>\n            <p>New Contact Form Submission</p>\n          </div>\n\n          <div class=\"content\">\n            <h1>New Contact Form Submission</h1>\n            \n            <div class=\"highlight\">\n              <h3>Contact Details</h3>\n              <p><strong>Name:</strong> ${data.name}</p>\n              <p><strong>Email:</strong> ${data.email}</p>\n              <p><strong>Subject:</strong> ${data.subject}</p>\n              <p><strong>Submitted:</strong> ${data.submissionDate}</p>\n            </div>\n\n            <div class=\"vehicle-card\">\n              <h3>Message</h3>\n              <p>${data.message.replace(/\\n/g, '<br>')}</p>\n            </div>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"mailto:${data.email}?subject=Re: ${data.subject}\" class=\"button\">Reply to Customer</a>\n            </div>\n          </div>\n\n          <div class=\"footer\">\n            <p>EBAM Motors Admin Panel</p>\n            <p>Please respond to this customer inquiry promptly.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    /**\n   * Generate Abandoned Cart Follow-up HTML\n   */ static generateAbandonedCartHTML(data) {\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>Complete Your Purchase</title>\n        ${emailStyles}\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors</div>\n            <p>Don't miss out on your perfect vehicle!</p>\n          </div>\n\n          <div class=\"content\">\n            <h1>Complete Your Purchase</h1>\n            <p>Hi ${data.customerName},</p>\n            <p>We noticed you were interested in some amazing vehicles but didn't complete your purchase. Don't worry - we've saved your items!</p>\n\n            <div class=\"highlight\">\n              <h3>Your Saved Items</h3>\n              ${data.data?.items?.map((item)=>`\n                <div class=\"vehicle-card\">\n                  <h4>${item.title}</h4>\n                  <p><strong>Price:</strong> ${item.price}</p>\n                  <p>Quantity: ${item.quantity}</p>\n                </div>\n              `).join('') || '<p>Your selected vehicles are waiting for you!</p>'}\n            </div>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://yourdomain.com/stock\" class=\"button\">Complete Your Purchase</a>\n            </div>\n\n            <div class=\"highlight\">\n              <h3>Why Choose EBAM Motors?</h3>\n              <ul>\n                <li>✅ Quality guaranteed vehicles from Japan</li>\n                <li>✅ Competitive pricing with transparent costs</li>\n                <li>✅ Reliable shipping to Ghana and Africa</li>\n                <li>✅ Expert support throughout the process</li>\n              </ul>\n            </div>\n\n            <p>Need help deciding? Our team is here to assist you:</p>\n            <ul>\n              <li>📱 WhatsApp: +233245375692</li>\n              <li>📧 Email: <EMAIL></li>\n            </ul>\n          </div>\n\n          <div class=\"footer\">\n            <p>This offer won't last forever!</p>\n            <p>© 2024 EBAM Motors. All rights reserved.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    /**\n   * Generate Delivery Update HTML\n   */ static generateDeliveryUpdateHTML(data) {\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>Delivery Update</title>\n        ${emailStyles}\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors</div>\n            <p>Your vehicle is on its way!</p>\n          </div>\n\n          <div class=\"content\">\n            <h1>Delivery Update</h1>\n            <p>Hi ${data.customerName},</p>\n            <p>Great news! We have an update on your vehicle delivery.</p>\n\n            <div class=\"highlight\">\n              <h3>Delivery Status</h3>\n              <p><strong>Current Status:</strong> ${data.data?.status || 'In Transit'}</p>\n              <p><strong>Location:</strong> ${data.data?.location || 'En route to destination'}</p>\n              <p><strong>Estimated Arrival:</strong> ${data.data?.estimatedArrival || 'To be confirmed'}</p>\n            </div>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://yourdomain.com/tracking?order=${data.data?.orderId}\" class=\"button\">Track Your Order</a>\n            </div>\n\n            <div class=\"vehicle-card\">\n              <h3>What to Expect Next</h3>\n              <ul>\n                <li>We'll notify you 24 hours before delivery</li>\n                <li>Our delivery team will contact you directly</li>\n                <li>Ensure someone is available to receive the vehicle</li>\n                <li>Have your ID and order confirmation ready</li>\n              </ul>\n            </div>\n          </div>\n\n          <div class=\"footer\">\n            <p>Thank you for choosing EBAM Motors!</p>\n            <p>© 2024 EBAM Motors. All rights reserved.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    /**\n   * Generate Feedback Request HTML\n   */ static generateFeedbackRequestHTML(data) {\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>How was your experience?</title>\n        ${emailStyles}\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors</div>\n            <p>We'd love to hear from you!</p>\n          </div>\n\n          <div class=\"content\">\n            <h1>How Was Your Experience?</h1>\n            <p>Hi ${data.customerName},</p>\n            <p>We hope you're enjoying your new vehicle! Your feedback helps us improve our services and helps other customers make informed decisions.</p>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://yourdomain.com/reviews\" class=\"button\">Leave a Review</a>\n            </div>\n\n            <div class=\"highlight\">\n              <h3>Share Your Experience</h3>\n              <p>Tell us about:</p>\n              <ul>\n                <li>🚗 Vehicle quality and condition</li>\n                <li>📦 Shipping and delivery experience</li>\n                <li>👥 Customer service quality</li>\n                <li>💰 Value for money</li>\n                <li>🌟 Overall satisfaction</li>\n              </ul>\n            </div>\n\n            <div class=\"vehicle-card\">\n              <h3>Why Your Review Matters</h3>\n              <ul>\n                <li>Helps other customers make confident decisions</li>\n                <li>Helps us improve our services</li>\n                <li>Builds trust in our community</li>\n                <li>Takes less than 2 minutes to complete</li>\n              </ul>\n            </div>\n\n            <p>As a thank you, customers who leave reviews get priority support and exclusive offers!</p>\n          </div>\n\n          <div class=\"footer\">\n            <p>Thank you for choosing EBAM Motors!</p>\n            <p>© 2024 EBAM Motors. All rights reserved.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    /**\n   * Generate Contact Form Customer Confirmation HTML\n   */ static generateContactFormCustomerHTML(data) {\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>Thank you for contacting us</title>\n        ${emailStyles}\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors</div>\n            <p>Thank you for reaching out!</p>\n          </div>\n\n          <div class=\"content\">\n            <h1>Thank You for Contacting Us!</h1>\n            <p>Dear ${data.name},</p>\n            <p>We've received your message and appreciate you taking the time to contact us. Our team will review your inquiry and respond within 24 hours.</p>\n\n            <div class=\"highlight\">\n              <h3>Your Message Summary</h3>\n              <p><strong>Subject:</strong> ${data.subject}</p>\n              <p><strong>Submitted:</strong> ${data.submissionDate}</p>\n              <p><strong>Reference ID:</strong> #${Date.now().toString().slice(-6)}</p>\n            </div>\n\n            <div class=\"vehicle-card\">\n              <h3>What to Expect Next</h3>\n              <ul>\n                <li>Our team will review your inquiry</li>\n                <li>You'll receive a response within 24 hours</li>\n                <li>For urgent matters, contact us on WhatsApp</li>\n              </ul>\n            </div>\n\n            <p>In the meantime, feel free to:</p>\n            <ul>\n              <li>Browse our latest vehicle inventory</li>\n              <li>Check out customer reviews</li>\n              <li>Learn more about our services</li>\n            </ul>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://yourdomain.com/stock\" class=\"button\">Browse Vehicles</a>\n            </div>\n\n            <p><strong>Need immediate assistance?</strong></p>\n            <ul>\n              <li>📱 WhatsApp: +233245375692</li>\n              <li>📧 Email: <EMAIL></li>\n              <li>📍 Location: Kumasi, Ghana</li>\n            </ul>\n          </div>\n\n          <div class=\"footer\">\n            <p>Thank you for choosing EBAM Motors!</p>\n            <p>© 2024 EBAM Motors. All rights reserved.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/emailTemplates.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/resendService.ts":
/*!**********************************!*\
  !*** ./src/lib/resendService.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EMAIL_CONFIG: () => (/* binding */ EMAIL_CONFIG),\n/* harmony export */   ResendEmailService: () => (/* binding */ ResendEmailService),\n/* harmony export */   emailService: () => (/* binding */ emailService)\n/* harmony export */ });\n/* harmony import */ var resend__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! resend */ \"(rsc)/./node_modules/resend/dist/index.mjs\");\n/* harmony import */ var _emailTemplates__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./emailTemplates */ \"(rsc)/./src/lib/emailTemplates.ts\");\n\n\n// Initialize Resend with API key\nconst resend = new resend__WEBPACK_IMPORTED_MODULE_0__.Resend(process.env.RESEND_API_KEY);\n// Email configuration\nconst EMAIL_CONFIG = {\n    from: process.env.RESEND_FROM_EMAIL || 'EBAM Motors <<EMAIL>>',\n    adminEmail: process.env.ADMIN_EMAIL || '<EMAIL>',\n    supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',\n    noReplyEmail: process.env.NO_REPLY_EMAIL || '<EMAIL>'\n};\n// Base email service class\nclass ResendEmailService {\n    constructor(){\n        this.resend = resend;\n    }\n    /**\n   * Send a generic email\n   */ async sendEmail(template) {\n        try {\n            if (!process.env.RESEND_API_KEY) {\n                console.warn('Resend API key not configured. Email not sent.');\n                return {\n                    success: false,\n                    error: 'Resend API key not configured'\n                };\n            }\n            const result = await this.resend.emails.send({\n                from: EMAIL_CONFIG.from,\n                to: template.to,\n                subject: template.subject,\n                html: template.html,\n                text: template.text\n            });\n            if (result.error) {\n                console.error('Resend email error:', result.error);\n                return {\n                    success: false,\n                    error: result.error.message\n                };\n            }\n            console.log('Email sent successfully:', result.data?.id);\n            return {\n                success: true,\n                messageId: result.data?.id\n            };\n        } catch (error) {\n            console.error('Email service error:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    /**\n   * Send order confirmation email\n   */ async sendOrderConfirmation(customerEmail, data) {\n        const template = {\n            to: customerEmail,\n            subject: `Order Confirmation - ${data.orderNumber} | EBAM Motors`,\n            html: this.generateOrderConfirmationHTML(data),\n            text: this.generateOrderConfirmationText(data)\n        };\n        const result = await this.sendEmail(template);\n        return result;\n    }\n    /**\n   * Send review notification to admin\n   */ async sendReviewNotificationToAdmin(data) {\n        const template = {\n            to: EMAIL_CONFIG.adminEmail,\n            subject: `New Review Submitted - ${data.vehicleTitle} | EBAM Motors`,\n            html: this.generateReviewNotificationHTML(data),\n            text: this.generateReviewNotificationText(data)\n        };\n        const result = await this.sendEmail(template);\n        return result;\n    }\n    /**\n   * Send review approval notification to customer\n   */ async sendReviewApprovalNotification(customerEmail, data) {\n        const template = {\n            to: customerEmail,\n            subject: `Your Review Has Been Approved | EBAM Motors`,\n            html: this.generateReviewApprovalHTML(data),\n            text: this.generateReviewApprovalText(data)\n        };\n        const result = await this.sendEmail(template);\n        return result;\n    }\n    /**\n   * Send contact form submission notification\n   */ async sendContactFormNotification(data) {\n        // Send to admin\n        const adminTemplate = {\n            to: EMAIL_CONFIG.adminEmail,\n            subject: `New Contact Form Submission - ${data.subject} | EBAM Motors`,\n            html: this.generateContactFormAdminHTML(data),\n            text: this.generateContactFormAdminText(data)\n        };\n        // Send confirmation to customer\n        const customerTemplate = {\n            to: data.email,\n            subject: `Thank you for contacting EBAM Motors`,\n            html: this.generateContactFormCustomerHTML(data),\n            text: this.generateContactFormCustomerText(data)\n        };\n        const [adminResult, customerResult] = await Promise.all([\n            this.sendEmail(adminTemplate),\n            this.sendEmail(customerTemplate)\n        ]);\n        return {\n            success: adminResult.success && customerResult.success,\n            error: adminResult.error || customerResult.error\n        };\n    }\n    /**\n   * Send admin notification\n   */ async sendAdminNotification(data) {\n        const template = {\n            to: EMAIL_CONFIG.adminEmail,\n            subject: `${data.title} | EBAM Motors Admin`,\n            html: this.generateAdminNotificationHTML(data),\n            text: this.generateAdminNotificationText(data)\n        };\n        const result = await this.sendEmail(template);\n        return result;\n    }\n    /**\n   * Send follow-up email\n   */ async sendFollowUpEmail(data) {\n        let subject = '';\n        let html = '';\n        let text = '';\n        switch(data.type){\n            case 'abandoned_cart':\n                subject = 'Complete Your Purchase - Items Still Available | EBAM Motors';\n                html = this.generateAbandonedCartHTML(data);\n                text = this.generateAbandonedCartText(data);\n                break;\n            case 'delivery_update':\n                subject = 'Delivery Update for Your Order | EBAM Motors';\n                html = this.generateDeliveryUpdateHTML(data);\n                text = this.generateDeliveryUpdateText(data);\n                break;\n            case 'feedback_request':\n                subject = 'How was your experience with EBAM Motors?';\n                html = this.generateFeedbackRequestHTML(data);\n                text = this.generateFeedbackRequestText(data);\n                break;\n            case 'maintenance_reminder':\n                subject = 'Vehicle Maintenance Reminder | EBAM Motors';\n                html = this.generateMaintenanceReminderHTML(data);\n                text = this.generateMaintenanceReminderText(data);\n                break;\n            default:\n                return {\n                    success: false,\n                    error: 'Unknown follow-up type'\n                };\n        }\n        const template = {\n            to: data.customerEmail,\n            subject,\n            html,\n            text\n        };\n        const result = await this.sendEmail(template);\n        return result;\n    }\n    // HTML template generators using EmailTemplates class\n    generateOrderConfirmationHTML(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateOrderConfirmationHTML(data);\n    }\n    generateOrderConfirmationText(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateOrderConfirmationText(data);\n    }\n    generateReviewNotificationHTML(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateReviewNotificationHTML(data);\n    }\n    generateReviewNotificationText(data) {\n        return `New Review from ${data.customerName} for ${data.vehicleTitle} - Rating: ${data.rating}/5`;\n    }\n    generateReviewApprovalHTML(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateReviewApprovalHTML(data);\n    }\n    generateReviewApprovalText(data) {\n        return `Your review for ${data.vehicleTitle} has been approved. Thank you ${data.customerName}!`;\n    }\n    generateContactFormAdminHTML(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateContactFormAdminHTML(data);\n    }\n    generateContactFormAdminText(data) {\n        return `New Contact Form from ${data.name} (${data.email}) - Subject: ${data.subject}`;\n    }\n    generateContactFormCustomerHTML(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateContactFormCustomerHTML(data);\n    }\n    generateContactFormCustomerText(data) {\n        return `Thank you for contacting EBAM Motors, ${data.name}. We received your message about \"${data.subject}\" and will respond within 24 hours.`;\n    }\n    generateAdminNotificationHTML(data) {\n        return `<h1>${data.title}</h1><p>${data.message}</p>`;\n    }\n    generateAdminNotificationText(data) {\n        return `${data.title}: ${data.message}`;\n    }\n    generateAbandonedCartHTML(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateAbandonedCartHTML(data);\n    }\n    generateAbandonedCartText(data) {\n        return `Hi ${data.customerName}! You left some amazing vehicles in your cart. Complete your purchase at EBAM Motors and get them shipped to Ghana. Don't miss out!`;\n    }\n    generateDeliveryUpdateHTML(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateDeliveryUpdateHTML(data);\n    }\n    generateDeliveryUpdateText(data) {\n        return `Delivery update for ${data.customerName}: ${data.data?.status || 'Your vehicle is on its way'}. Track your order at ebammotors.com`;\n    }\n    generateFeedbackRequestHTML(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateFeedbackRequestHTML(data);\n    }\n    generateFeedbackRequestText(data) {\n        return `Hi ${data.customerName}! How was your experience with EBAM Motors? We'd love to hear your feedback. Leave a review at ebammotors.com/reviews`;\n    }\n    generateMaintenanceReminderHTML(data) {\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>Vehicle Maintenance Reminder</title>\n        <style>\n          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }\n          .container { max-width: 600px; margin: 0 auto; background-color: white; }\n          .header { background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); color: white; padding: 30px; text-align: center; }\n          .logo { font-size: 28px; font-weight: bold; margin-bottom: 10px; }\n          .content { padding: 30px; }\n          .highlight { background-color: #eff6ff; padding: 15px; border-radius: 6px; margin: 15px 0; }\n          .button { display: inline-block; background: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }\n          .footer { background-color: #f9fafb; padding: 20px; text-align: center; color: #6b7280; font-size: 14px; }\n        </style>\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors</div>\n            <p>Keep your vehicle in perfect condition</p>\n          </div>\n          <div class=\"content\">\n            <h1>Vehicle Maintenance Reminder</h1>\n            <p>Hi ${data.customerName},</p>\n            <p>It's time for your vehicle's scheduled maintenance to keep it running smoothly and safely.</p>\n            <div class=\"highlight\">\n              <h3>Recommended Maintenance</h3>\n              <p><strong>Vehicle:</strong> ${data.data?.vehicleTitle || 'Your vehicle'}</p>\n              <p><strong>Mileage:</strong> ${data.data?.currentMileage || 'Check your odometer'}</p>\n              <p><strong>Service Due:</strong> ${data.data?.serviceType || 'Regular maintenance'}</p>\n            </div>\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://yourdomain.com/contact\" class=\"button\">Schedule Service</a>\n            </div>\n            <p>Regular maintenance helps ensure:</p>\n            <ul>\n              <li>🔧 Optimal performance and fuel efficiency</li>\n              <li>🛡️ Safety and reliability</li>\n              <li>💰 Prevention of costly repairs</li>\n              <li>📈 Maintained resale value</li>\n            </ul>\n          </div>\n          <div class=\"footer\">\n            <p>Thank you for choosing EBAM Motors!</p>\n            <p>© 2024 EBAM Motors. All rights reserved.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    generateMaintenanceReminderText(data) {\n        return `Hi ${data.customerName}! It's time for your vehicle's scheduled maintenance. Contact EBAM Motors to schedule service and keep your vehicle running smoothly.`;\n    }\n}\n// Export singleton instance\nconst emailService = new ResendEmailService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/resendService.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("async_hooks");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "prettier/plugins/html":
/*!****************************************!*\
  !*** external "prettier/plugins/html" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/plugins/html");;

/***/ }),

/***/ "prettier/standalone":
/*!**************************************!*\
  !*** external "prettier/standalone" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/standalone");;

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/uuid","vendor-chunks/resend"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fleads%2Froute&page=%2Fapi%2Fleads%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fleads%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();