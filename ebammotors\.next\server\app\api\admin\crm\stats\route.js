/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/crm/stats/route";
exports.ids = ["app/api/admin/crm/stats/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcrm%2Fstats%2Froute&page=%2Fapi%2Fadmin%2Fcrm%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcrm%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcrm%2Fstats%2Froute&page=%2Fapi%2Fadmin%2Fcrm%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcrm%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_User_Desktop_website_ebammotors_src_app_api_admin_crm_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/crm/stats/route.ts */ \"(rsc)/./src/app/api/admin/crm/stats/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/crm/stats/route\",\n        pathname: \"/api/admin/crm/stats\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/crm/stats/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\app\\\\api\\\\admin\\\\crm\\\\stats\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_User_Desktop_website_ebammotors_src_app_api_admin_crm_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcrm%2Fstats%2Froute&page=%2Fapi%2Fadmin%2Fcrm%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcrm%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/crm/stats/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/admin/crm/stats/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_adminMiddleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/adminMiddleware */ \"(rsc)/./src/lib/adminMiddleware.ts\");\n/* harmony import */ var _lib_crmStorage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/crmStorage */ \"(rsc)/./src/lib/crmStorage.ts\");\n\n\n\nasync function GET(request) {\n    try {\n        // Verify admin authentication\n        const adminAuth = (0,_lib_adminMiddleware__WEBPACK_IMPORTED_MODULE_1__.getAdminAuth)(request);\n        if (!adminAuth.isValid) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // Get CRM statistics\n        const stats = await getCRMStats();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(stats);\n    } catch (error) {\n        console.error('Error fetching CRM stats:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Failed to fetch CRM statistics'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function getCRMStats() {\n    try {\n        // Get all data\n        const [customers, leads, interactions] = await Promise.all([\n            (0,_lib_crmStorage__WEBPACK_IMPORTED_MODULE_2__.getAllCustomers)(),\n            (0,_lib_crmStorage__WEBPACK_IMPORTED_MODULE_2__.getAllLeads)(),\n            (0,_lib_crmStorage__WEBPACK_IMPORTED_MODULE_2__.getAllInteractions)()\n        ]);\n        // Calculate customer stats\n        const totalCustomers = customers.length;\n        const activeCustomers = customers.filter((c)=>c.status === 'active').length;\n        const newCustomers = customers.filter((c)=>{\n            const createdDate = new Date(c.createdAt);\n            const thirtyDaysAgo = new Date();\n            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n            return createdDate > thirtyDaysAgo;\n        }).length;\n        const vipCustomers = customers.filter((c)=>c.membershipTier === 'Gold' || c.membershipTier === 'Platinum' || c.membershipTier === 'VIP').length;\n        // Calculate lead stats\n        const totalLeads = leads.length;\n        const newLeads = leads.filter((l)=>l.status === 'new').length;\n        const qualifiedLeads = leads.filter((l)=>l.status === 'qualified').length;\n        const convertedLeads = leads.filter((l)=>l.status === 'won' || l.status === 'converted').length;\n        // Calculate revenue stats\n        const totalRevenue = customers.reduce((sum, c)=>sum + (c.totalSpent || 0), 0);\n        const thisMonthRevenue = customers.reduce((sum, c)=>{\n            // This is a simplified calculation - in a real system you'd check actual order dates\n            const lastOrderDate = c.lastOrderDate ? new Date(c.lastOrderDate) : null;\n            const thisMonth = new Date();\n            thisMonth.setDate(1);\n            if (lastOrderDate && lastOrderDate >= thisMonth) {\n                return sum + (c.totalSpent || 0);\n            }\n            return sum;\n        }, 0);\n        const customersWithOrders = customers.filter((c)=>c.totalOrders > 0);\n        const averageOrderValue = customersWithOrders.length > 0 ? customersWithOrders.reduce((sum, c)=>sum + c.averageOrderValue, 0) / customersWithOrders.length : 0;\n        // Calculate interaction stats\n        const totalInteractions = interactions.length;\n        const thisWeekInteractions = interactions.filter((i)=>{\n            const interactionDate = new Date(i.createdAt);\n            const sevenDaysAgo = new Date();\n            sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);\n            return interactionDate > sevenDaysAgo;\n        }).length;\n        // Calculate response rate (simplified - percentage of interactions that are outbound)\n        const outboundInteractions = interactions.filter((i)=>i.direction === 'outbound').length;\n        const responseRate = totalInteractions > 0 ? outboundInteractions / totalInteractions * 100 : 0;\n        // Additional analytics\n        const leadConversionRate = totalLeads > 0 ? convertedLeads / totalLeads * 100 : 0;\n        const customerRetentionRate = totalCustomers > 0 ? activeCustomers / totalCustomers * 100 : 0;\n        // Lead sources breakdown\n        const leadSources = leads.reduce((acc, lead)=>{\n            acc[lead.source] = (acc[lead.source] || 0) + 1;\n            return acc;\n        }, {});\n        // Customer segments breakdown\n        const customerSegments = customers.reduce((acc, customer)=>{\n            acc[customer.segment] = (acc[customer.segment] || 0) + 1;\n            return acc;\n        }, {});\n        // Monthly trends (last 6 months)\n        const monthlyTrends = getMonthlyTrends(customers, leads, interactions);\n        // Top performing metrics\n        const topCustomers = customers.sort((a, b)=>(b.totalSpent || 0) - (a.totalSpent || 0)).slice(0, 5).map((c)=>({\n                id: c.id,\n                name: c.personalInfo.name,\n                email: c.personalInfo.email,\n                totalSpent: c.totalSpent || 0,\n                totalOrders: c.totalOrders || 0,\n                membershipTier: c.membershipTier\n            }));\n        return {\n            customers: {\n                total: totalCustomers,\n                active: activeCustomers,\n                new: newCustomers,\n                vip: vipCustomers,\n                retentionRate: Math.round(customerRetentionRate * 10) / 10\n            },\n            leads: {\n                total: totalLeads,\n                new: newLeads,\n                qualified: qualifiedLeads,\n                converted: convertedLeads,\n                conversionRate: Math.round(leadConversionRate * 10) / 10\n            },\n            revenue: {\n                total: Math.round(totalRevenue),\n                thisMonth: Math.round(thisMonthRevenue),\n                averageOrderValue: Math.round(averageOrderValue)\n            },\n            interactions: {\n                total: totalInteractions,\n                thisWeek: thisWeekInteractions,\n                responseRate: Math.round(responseRate * 10) / 10\n            },\n            analytics: {\n                leadSources,\n                customerSegments,\n                monthlyTrends,\n                topCustomers\n            }\n        };\n    } catch (error) {\n        console.error('Error calculating CRM stats:', error);\n        return {\n            customers: {\n                total: 0,\n                active: 0,\n                new: 0,\n                vip: 0,\n                retentionRate: 0\n            },\n            leads: {\n                total: 0,\n                new: 0,\n                qualified: 0,\n                converted: 0,\n                conversionRate: 0\n            },\n            revenue: {\n                total: 0,\n                thisMonth: 0,\n                averageOrderValue: 0\n            },\n            interactions: {\n                total: 0,\n                thisWeek: 0,\n                responseRate: 0\n            },\n            analytics: {\n                leadSources: {},\n                customerSegments: {},\n                monthlyTrends: [],\n                topCustomers: []\n            }\n        };\n    }\n}\nfunction getMonthlyTrends(customers, leads, interactions) {\n    const trends = [];\n    const now = new Date();\n    for(let i = 5; i >= 0; i--){\n        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);\n        const nextDate = new Date(now.getFullYear(), now.getMonth() - i + 1, 1);\n        const monthKey = date.toISOString().slice(0, 7); // YYYY-MM format\n        const customersThisMonth = customers.filter((c)=>{\n            const createdDate = new Date(c.createdAt);\n            return createdDate >= date && createdDate < nextDate;\n        }).length;\n        const leadsThisMonth = leads.filter((l)=>{\n            const createdDate = new Date(l.createdAt);\n            return createdDate >= date && createdDate < nextDate;\n        }).length;\n        const interactionsThisMonth = interactions.filter((i)=>{\n            const createdDate = new Date(i.createdAt);\n            return createdDate >= date && createdDate < nextDate;\n        }).length;\n        const revenueThisMonth = customers.reduce((sum, c)=>{\n            if (c.lastOrderDate) {\n                const orderDate = new Date(c.lastOrderDate);\n                if (orderDate >= date && orderDate < nextDate) {\n                    return sum + (c.totalSpent || 0);\n                }\n            }\n            return sum;\n        }, 0);\n        trends.push({\n            month: monthKey,\n            customers: customersThisMonth,\n            leads: leadsThisMonth,\n            interactions: interactionsThisMonth,\n            revenue: Math.round(revenueThisMonth)\n        });\n    }\n    return trends;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/crm/stats/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/adminMiddleware.ts":
/*!************************************!*\
  !*** ./src/lib/adminMiddleware.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAdminAuth: () => (/* binding */ getAdminAuth),\n/* harmony export */   getAdminFromRequest: () => (/* binding */ getAdminFromRequest),\n/* harmony export */   verifyLegacyAdminKey: () => (/* binding */ verifyLegacyAdminKey),\n/* harmony export */   withAdminAuth: () => (/* binding */ withAdminAuth)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n\n\n/**\n * Middleware to verify admin authentication for API routes\n */ function withAdminAuth(handler) {\n    return async (request, context)=>{\n        try {\n            // Get authentication from headers or cookies\n            const authHeader = request.headers.get('authorization');\n            const sessionId = request.cookies.get('admin_session')?.value;\n            // Verify authentication\n            const authResult = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.verifyAdminAuth)(authHeader, sessionId);\n            if (!authResult.isValid) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    message: authResult.message\n                }, {\n                    status: 401\n                });\n            }\n            // Add admin info to request headers for the handler\n            const requestWithAuth = new next_server__WEBPACK_IMPORTED_MODULE_0__.NextRequest(request.url, {\n                method: request.method,\n                headers: {\n                    ...Object.fromEntries(request.headers.entries()),\n                    'x-admin-id': authResult.adminId || 'admin',\n                    'x-admin-authenticated': 'true'\n                },\n                body: request.body\n            });\n            return handler(requestWithAuth, context);\n        } catch (error) {\n            console.error('Admin middleware error:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Authentication error'\n            }, {\n                status: 500\n            });\n        }\n    };\n}\n/**\n * Extract admin authentication from request\n */ function getAdminFromRequest(request) {\n    const adminId = request.headers.get('x-admin-id') || 'admin';\n    const isAuthenticated = request.headers.get('x-admin-authenticated') === 'true';\n    return {\n        adminId,\n        isAuthenticated\n    };\n}\n/**\n * Verify admin authentication for legacy API routes that use adminKey\n */ function verifyLegacyAdminKey(adminKey) {\n    const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';\n    return adminKey === validAdminKey;\n}\n/**\n * Get admin authentication from request (supports both new and legacy methods)\n */ function getAdminAuth(request, body) {\n    // Try new authentication method first\n    const authHeader = request.headers.get('authorization');\n    const sessionId = request.cookies.get('admin_session')?.value;\n    const authResult = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.verifyAdminAuth)(authHeader, sessionId);\n    if (authResult.isValid) {\n        return {\n            isValid: true,\n            adminId: authResult.adminId,\n            method: 'token/session'\n        };\n    }\n    // Fall back to legacy adminKey method\n    const adminKey = body?.adminKey || request.nextUrl.searchParams.get('adminKey');\n    if (adminKey && verifyLegacyAdminKey(adminKey)) {\n        return {\n            isValid: true,\n            adminId: 'admin',\n            method: 'legacy'\n        };\n    }\n    return {\n        isValid: false,\n        method: 'none'\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/adminMiddleware.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticateAdmin: () => (/* binding */ authenticateAdmin),\n/* harmony export */   checkAuthRateLimit: () => (/* binding */ checkAuthRateLimit),\n/* harmony export */   createAdminSession: () => (/* binding */ createAdminSession),\n/* harmony export */   destroyAdminSession: () => (/* binding */ destroyAdminSession),\n/* harmony export */   generateAdminToken: () => (/* binding */ generateAdminToken),\n/* harmony export */   getAdminPasswordHash: () => (/* binding */ getAdminPasswordHash),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   resetAuthRateLimit: () => (/* binding */ resetAuthRateLimit),\n/* harmony export */   validateAdminSession: () => (/* binding */ validateAdminSession),\n/* harmony export */   verifyAdminAuth: () => (/* binding */ verifyAdminAuth),\n/* harmony export */   verifyAdminToken: () => (/* binding */ verifyAdminToken),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Security configuration\nconst SALT_ROUNDS = 12;\nconst JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';\nconst JWT_EXPIRES_IN = '24h';\nconst SESSION_TIMEOUT = 24 * 60 * 60 * 1000; // 24 hours in milliseconds\n// In-memory session store (replace with Redis in production)\nconst activeSessions = new Map();\n/**\n * Hash a password using bcrypt\n */ async function hashPassword(password) {\n    try {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, SALT_ROUNDS);\n    } catch (error) {\n        console.error('Error hashing password:', error);\n        throw new Error('Failed to hash password');\n    }\n}\n/**\n * Verify a password against its hash\n */ async function verifyPassword(password, hash) {\n    try {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hash);\n    } catch (error) {\n        console.error('Error verifying password:', error);\n        return false;\n    }\n}\n/**\n * Generate a JWT token for admin authentication\n */ function generateAdminToken(adminId = 'admin') {\n    try {\n        const payload = {\n            id: adminId,\n            isAdmin: true,\n            iat: Math.floor(Date.now() / 1000)\n        };\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_SECRET, {\n            expiresIn: JWT_EXPIRES_IN\n        });\n    } catch (error) {\n        console.error('Error generating token:', error);\n        throw new Error('Failed to generate authentication token');\n    }\n}\n/**\n * Verify and decode a JWT token\n */ function verifyAdminToken(token) {\n    try {\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, JWT_SECRET);\n        if (decoded.isAdmin) {\n            return {\n                id: decoded.id,\n                isAdmin: decoded.isAdmin\n            };\n        }\n        return null;\n    } catch (error) {\n        // Token is invalid or expired\n        return null;\n    }\n}\n/**\n * Create a new admin session\n */ function createAdminSession(adminId = 'admin') {\n    const sessionId = generateSessionId();\n    const now = Date.now();\n    const session = {\n        id: adminId,\n        isAdmin: true,\n        createdAt: now,\n        expiresAt: now + SESSION_TIMEOUT,\n        lastActivity: now\n    };\n    activeSessions.set(sessionId, session);\n    // Clean up expired sessions\n    cleanupExpiredSessions();\n    return sessionId;\n}\n/**\n * Validate an admin session\n */ function validateAdminSession(sessionId) {\n    const session = activeSessions.get(sessionId);\n    if (!session) {\n        return null;\n    }\n    const now = Date.now();\n    // Check if session has expired\n    if (now > session.expiresAt) {\n        activeSessions.delete(sessionId);\n        return null;\n    }\n    // Update last activity\n    session.lastActivity = now;\n    activeSessions.set(sessionId, session);\n    return session;\n}\n/**\n * Destroy an admin session\n */ function destroyAdminSession(sessionId) {\n    return activeSessions.delete(sessionId);\n}\n/**\n * Generate a secure session ID\n */ function generateSessionId() {\n    return `admin_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;\n}\n/**\n * Clean up expired sessions\n */ function cleanupExpiredSessions() {\n    const now = Date.now();\n    for (const [sessionId, session] of activeSessions.entries()){\n        if (now > session.expiresAt) {\n            activeSessions.delete(sessionId);\n        }\n    }\n}\n/**\n * Get admin password hash from environment\n * In production, this should be stored in a secure database\n */ function getAdminPasswordHash() {\n    // For backward compatibility, check if password is already hashed\n    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';\n    // If it starts with $2a$, $2b$, or $2y$, it's already a bcrypt hash\n    if (adminPassword.startsWith('$2a$') || adminPassword.startsWith('$2b$') || adminPassword.startsWith('$2y$')) {\n        return adminPassword;\n    }\n    // For development/migration: return the plain password (will be handled in auth route)\n    return adminPassword;\n}\n/**\n * Secure admin authentication\n */ async function authenticateAdmin(password) {\n    try {\n        const adminPasswordHash = getAdminPasswordHash();\n        let isValid = false;\n        // Check if stored password is hashed or plain text\n        if (adminPasswordHash.startsWith('$2a$') || adminPasswordHash.startsWith('$2b$') || adminPasswordHash.startsWith('$2y$')) {\n            // Password is hashed, use bcrypt comparison\n            isValid = await verifyPassword(password, adminPasswordHash);\n        } else {\n            // Password is plain text (development/migration), use direct comparison\n            isValid = password === adminPasswordHash;\n        }\n        if (isValid) {\n            const token = generateAdminToken();\n            const sessionId = createAdminSession();\n            return {\n                success: true,\n                token,\n                sessionId,\n                message: 'Authentication successful'\n            };\n        } else {\n            return {\n                success: false,\n                message: 'Invalid credentials'\n            };\n        }\n    } catch (error) {\n        console.error('Authentication error:', error);\n        return {\n            success: false,\n            message: 'Authentication failed'\n        };\n    }\n}\n/**\n * Middleware to verify admin authentication\n */ function verifyAdminAuth(authHeader, sessionId) {\n    // Check JWT token\n    if (authHeader && authHeader.startsWith('Bearer ')) {\n        const token = authHeader.substring(7);\n        const decoded = verifyAdminToken(token);\n        if (decoded) {\n            return {\n                isValid: true,\n                adminId: decoded.id,\n                message: 'Token authentication successful'\n            };\n        }\n    }\n    // Check session ID\n    if (sessionId) {\n        const session = validateAdminSession(sessionId);\n        if (session) {\n            return {\n                isValid: true,\n                adminId: session.id,\n                message: 'Session authentication successful'\n            };\n        }\n    }\n    return {\n        isValid: false,\n        message: 'Authentication required'\n    };\n}\n/**\n * Rate limiting for authentication attempts\n */ const authAttempts = new Map();\nconst MAX_AUTH_ATTEMPTS = 5;\nconst AUTH_LOCKOUT_TIME = 15 * 60 * 1000; // 15 minutes\nfunction checkAuthRateLimit(ip) {\n    const now = Date.now();\n    const attempts = authAttempts.get(ip);\n    if (!attempts) {\n        authAttempts.set(ip, {\n            count: 1,\n            lastAttempt: now\n        });\n        return {\n            allowed: true,\n            remainingAttempts: MAX_AUTH_ATTEMPTS - 1\n        };\n    }\n    // Reset if lockout time has passed\n    if (now - attempts.lastAttempt > AUTH_LOCKOUT_TIME) {\n        authAttempts.set(ip, {\n            count: 1,\n            lastAttempt: now\n        });\n        return {\n            allowed: true,\n            remainingAttempts: MAX_AUTH_ATTEMPTS - 1\n        };\n    }\n    // Check if max attempts exceeded\n    if (attempts.count >= MAX_AUTH_ATTEMPTS) {\n        const lockoutTime = AUTH_LOCKOUT_TIME - (now - attempts.lastAttempt);\n        return {\n            allowed: false,\n            remainingAttempts: 0,\n            lockoutTime\n        };\n    }\n    // Increment attempt count\n    attempts.count++;\n    attempts.lastAttempt = now;\n    authAttempts.set(ip, attempts);\n    return {\n        allowed: true,\n        remainingAttempts: MAX_AUTH_ATTEMPTS - attempts.count\n    };\n}\nfunction resetAuthRateLimit(ip) {\n    authAttempts.delete(ip);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/crmStorage.ts":
/*!*******************************!*\
  !*** ./src/lib/crmStorage.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCustomerActivity: () => (/* binding */ createCustomerActivity),\n/* harmony export */   createFollowUp: () => (/* binding */ createFollowUp),\n/* harmony export */   createInteraction: () => (/* binding */ createInteraction),\n/* harmony export */   createLead: () => (/* binding */ createLead),\n/* harmony export */   deleteLead: () => (/* binding */ deleteLead),\n/* harmony export */   getActivitiesByCustomerId: () => (/* binding */ getActivitiesByCustomerId),\n/* harmony export */   getAllCustomers: () => (/* binding */ getAllCustomers),\n/* harmony export */   getAllFollowUps: () => (/* binding */ getAllFollowUps),\n/* harmony export */   getAllInteractions: () => (/* binding */ getAllInteractions),\n/* harmony export */   getAllLeads: () => (/* binding */ getAllLeads),\n/* harmony export */   getCustomerByEmail: () => (/* binding */ getCustomerByEmail),\n/* harmony export */   getCustomerById: () => (/* binding */ getCustomerById),\n/* harmony export */   getCustomerOverview: () => (/* binding */ getCustomerOverview),\n/* harmony export */   getFollowUpById: () => (/* binding */ getFollowUpById),\n/* harmony export */   getFollowUpsByCustomerId: () => (/* binding */ getFollowUpsByCustomerId),\n/* harmony export */   getFollowUpsByStatus: () => (/* binding */ getFollowUpsByStatus),\n/* harmony export */   getInteractionsByCustomerId: () => (/* binding */ getInteractionsByCustomerId),\n/* harmony export */   getInteractionsByLeadId: () => (/* binding */ getInteractionsByLeadId),\n/* harmony export */   getLeadById: () => (/* binding */ getLeadById),\n/* harmony export */   getLeadsBySource: () => (/* binding */ getLeadsBySource),\n/* harmony export */   getLeadsByStatus: () => (/* binding */ getLeadsByStatus),\n/* harmony export */   getPendingFollowUps: () => (/* binding */ getPendingFollowUps),\n/* harmony export */   getRecentActivities: () => (/* binding */ getRecentActivities),\n/* harmony export */   updateCustomer: () => (/* binding */ updateCustomer),\n/* harmony export */   updateFollowUp: () => (/* binding */ updateFollowUp),\n/* harmony export */   updateLead: () => (/* binding */ updateLead),\n/* harmony export */   upsertCustomer: () => (/* binding */ upsertCustomer)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/esm/v4.js\");\n\n\n\n// File paths\nconst DATA_DIR = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'data');\nconst LEADS_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DATA_DIR, 'leads.json');\nconst CUSTOMERS_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DATA_DIR, 'customers.json');\nconst INTERACTIONS_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DATA_DIR, 'interactions.json');\nconst FOLLOWUPS_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DATA_DIR, 'followups.json');\nconst ACTIVITIES_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DATA_DIR, 'activities.json');\n// Check if running in serverless environment\nconst isServerless = process.env.VERCEL || process.env.NETLIFY || process.env.AWS_LAMBDA_FUNCTION_NAME;\n// In-memory storage for serverless environments\nlet leadsMemoryStore = [];\nlet customersMemoryStore = [];\nlet interactionsMemoryStore = [];\nlet followupsMemoryStore = [];\nlet activitiesMemoryStore = [];\n/**\n * Ensure data directory exists\n */ async function ensureDataDirectory() {\n    if (isServerless) return;\n    try {\n        await fs__WEBPACK_IMPORTED_MODULE_0__.promises.access(DATA_DIR);\n    } catch  {\n        await fs__WEBPACK_IMPORTED_MODULE_0__.promises.mkdir(DATA_DIR, {\n            recursive: true\n        });\n    }\n}\n// LEADS MANAGEMENT\n/**\n * Load leads from storage\n */ async function loadLeads() {\n    if (isServerless) {\n        return leadsMemoryStore;\n    }\n    try {\n        await ensureDataDirectory();\n        const data = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.readFile(LEADS_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        return [];\n    }\n}\n/**\n * Save leads to storage\n */ async function saveLeads(leads) {\n    if (isServerless) {\n        leadsMemoryStore = leads;\n        return;\n    }\n    await ensureDataDirectory();\n    await fs__WEBPACK_IMPORTED_MODULE_0__.promises.writeFile(LEADS_FILE, JSON.stringify(leads, null, 2));\n}\n/**\n * Create a new lead\n */ async function createLead(leadData) {\n    const leads = await loadLeads();\n    const newLead = {\n        ...leadData,\n        id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    };\n    leads.push(newLead);\n    await saveLeads(leads);\n    return newLead;\n}\n/**\n * Get all leads\n */ async function getAllLeads() {\n    return await loadLeads();\n}\n/**\n * Get lead by ID\n */ async function getLeadById(leadId) {\n    const leads = await loadLeads();\n    return leads.find((lead)=>lead.id === leadId) || null;\n}\n/**\n * Update lead\n */ async function updateLead(leadId, updates) {\n    const leads = await loadLeads();\n    const leadIndex = leads.findIndex((lead)=>lead.id === leadId);\n    if (leadIndex === -1) return false;\n    leads[leadIndex] = {\n        ...leads[leadIndex],\n        ...updates,\n        updatedAt: new Date().toISOString()\n    };\n    await saveLeads(leads);\n    return true;\n}\n/**\n * Delete lead\n */ async function deleteLead(leadId) {\n    const leads = await loadLeads();\n    const filteredLeads = leads.filter((lead)=>lead.id !== leadId);\n    if (filteredLeads.length === leads.length) return false;\n    await saveLeads(filteredLeads);\n    return true;\n}\n/**\n * Get leads by status\n */ async function getLeadsByStatus(status) {\n    const leads = await loadLeads();\n    return leads.filter((lead)=>lead.status === status);\n}\n/**\n * Get leads by source\n */ async function getLeadsBySource(source) {\n    const leads = await loadLeads();\n    return leads.filter((lead)=>lead.source === source);\n}\n// CUSTOMERS MANAGEMENT\n/**\n * Load customers from storage\n */ async function loadCustomers() {\n    if (isServerless) {\n        return customersMemoryStore;\n    }\n    try {\n        await ensureDataDirectory();\n        const data = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.readFile(CUSTOMERS_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        return [];\n    }\n}\n/**\n * Save customers to storage\n */ async function saveCustomers(customers) {\n    if (isServerless) {\n        customersMemoryStore = customers;\n        return;\n    }\n    await ensureDataDirectory();\n    await fs__WEBPACK_IMPORTED_MODULE_0__.promises.writeFile(CUSTOMERS_FILE, JSON.stringify(customers, null, 2));\n}\n/**\n * Create or update customer\n */ async function upsertCustomer(customerData) {\n    const customers = await loadCustomers();\n    // Check if customer exists by email\n    const existingCustomerIndex = customers.findIndex((c)=>c.personalInfo.email === customerData.personalInfo.email);\n    if (existingCustomerIndex !== -1) {\n        // Update existing customer\n        customers[existingCustomerIndex] = {\n            ...customers[existingCustomerIndex],\n            ...customerData,\n            updatedAt: new Date().toISOString()\n        };\n        await saveCustomers(customers);\n        return customers[existingCustomerIndex];\n    } else {\n        // Create new customer\n        const newCustomer = {\n            ...customerData,\n            id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        customers.push(newCustomer);\n        await saveCustomers(customers);\n        return newCustomer;\n    }\n}\n/**\n * Get all customers\n */ async function getAllCustomers() {\n    return await loadCustomers();\n}\n/**\n * Get customer by ID\n */ async function getCustomerById(customerId) {\n    const customers = await loadCustomers();\n    return customers.find((customer)=>customer.id === customerId) || null;\n}\n/**\n * Get customer by email\n */ async function getCustomerByEmail(email) {\n    const customers = await loadCustomers();\n    return customers.find((customer)=>customer.personalInfo.email === email) || null;\n}\n/**\n * Update customer\n */ async function updateCustomer(customerId, updates) {\n    const customers = await loadCustomers();\n    const customerIndex = customers.findIndex((customer)=>customer.id === customerId);\n    if (customerIndex === -1) return false;\n    customers[customerIndex] = {\n        ...customers[customerIndex],\n        ...updates,\n        updatedAt: new Date().toISOString()\n    };\n    await saveCustomers(customers);\n    return true;\n}\n// INTERACTIONS MANAGEMENT\n/**\n * Load interactions from storage\n */ async function loadInteractions() {\n    if (isServerless) {\n        return interactionsMemoryStore;\n    }\n    try {\n        await ensureDataDirectory();\n        const data = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.readFile(INTERACTIONS_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        return [];\n    }\n}\n/**\n * Save interactions to storage\n */ async function saveInteractions(interactions) {\n    if (isServerless) {\n        interactionsMemoryStore = interactions;\n        return;\n    }\n    await ensureDataDirectory();\n    await fs__WEBPACK_IMPORTED_MODULE_0__.promises.writeFile(INTERACTIONS_FILE, JSON.stringify(interactions, null, 2));\n}\n/**\n * Create a new interaction\n */ async function createInteraction(interactionData) {\n    const interactions = await loadInteractions();\n    const newInteraction = {\n        ...interactionData,\n        id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n        createdAt: new Date().toISOString()\n    };\n    interactions.push(newInteraction);\n    await saveInteractions(interactions);\n    return newInteraction;\n}\n/**\n * Get all interactions\n */ async function getAllInteractions() {\n    return await loadInteractions();\n}\n/**\n * Get interactions by customer ID\n */ async function getInteractionsByCustomerId(customerId) {\n    const interactions = await loadInteractions();\n    return interactions.filter((interaction)=>interaction.customerId === customerId);\n}\n/**\n * Get interactions by lead ID\n */ async function getInteractionsByLeadId(leadId) {\n    const interactions = await loadInteractions();\n    return interactions.filter((interaction)=>interaction.leadId === leadId);\n}\n// FOLLOW-UPS MANAGEMENT\n/**\n * Load follow-ups from storage\n */ async function loadFollowUps() {\n    if (isServerless) {\n        return followupsMemoryStore;\n    }\n    try {\n        await ensureDataDirectory();\n        const data = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.readFile(FOLLOWUPS_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        return [];\n    }\n}\n/**\n * Save follow-ups to storage\n */ async function saveFollowUps(followups) {\n    if (isServerless) {\n        followupsMemoryStore = followups;\n        return;\n    }\n    await ensureDataDirectory();\n    await fs__WEBPACK_IMPORTED_MODULE_0__.promises.writeFile(FOLLOWUPS_FILE, JSON.stringify(followups, null, 2));\n}\n/**\n * Create a new follow-up\n */ async function createFollowUp(followupData) {\n    const followups = await loadFollowUps();\n    const newFollowUp = {\n        ...followupData,\n        id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    };\n    followups.push(newFollowUp);\n    await saveFollowUps(followups);\n    return newFollowUp;\n}\n/**\n * Get all follow-ups\n */ async function getAllFollowUps() {\n    return await loadFollowUps();\n}\n/**\n * Get follow-up by ID\n */ async function getFollowUpById(id) {\n    const followups = await loadFollowUps();\n    return followups.find((f)=>f.id === id) || null;\n}\n/**\n * Get follow-ups by status\n */ async function getFollowUpsByStatus(status) {\n    const followups = await loadFollowUps();\n    return followups.filter((followup)=>followup.status === status);\n}\n/**\n * Get pending follow-ups (due now or overdue)\n */ async function getPendingFollowUps() {\n    const followups = await loadFollowUps();\n    const now = new Date().toISOString();\n    return followups.filter((followup)=>followup.status === 'pending' && followup.scheduledDate <= now);\n}\n/**\n * Update follow-up\n */ async function updateFollowUp(followupId, updates) {\n    const followups = await loadFollowUps();\n    const followupIndex = followups.findIndex((followup)=>followup.id === followupId);\n    if (followupIndex === -1) return false;\n    followups[followupIndex] = {\n        ...followups[followupIndex],\n        ...updates,\n        updatedAt: new Date().toISOString()\n    };\n    await saveFollowUps(followups);\n    return true;\n}\n/**\n * Get follow-ups by customer ID\n */ async function getFollowUpsByCustomerId(customerId) {\n    const followups = await loadFollowUps();\n    return followups.filter((followup)=>followup.customerId === customerId);\n}\n// CUSTOMER ACTIVITIES MANAGEMENT\n/**\n * Load activities from storage\n */ async function loadActivities() {\n    if (isServerless) {\n        return activitiesMemoryStore;\n    }\n    try {\n        await ensureDataDirectory();\n        const data = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.readFile(ACTIVITIES_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        return [];\n    }\n}\n/**\n * Save activities to storage\n */ async function saveActivities(activities) {\n    if (isServerless) {\n        activitiesMemoryStore = activities;\n        return;\n    }\n    await ensureDataDirectory();\n    await fs__WEBPACK_IMPORTED_MODULE_0__.promises.writeFile(ACTIVITIES_FILE, JSON.stringify(activities, null, 2));\n}\n/**\n * Create a new customer activity\n */ async function createCustomerActivity(activityData) {\n    const activities = await loadActivities();\n    const newActivity = {\n        ...activityData,\n        id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n        timestamp: new Date().toISOString()\n    };\n    activities.push(newActivity);\n    await saveActivities(activities);\n    return newActivity;\n}\n/**\n * Get activities by customer ID\n */ async function getActivitiesByCustomerId(customerId) {\n    const activities = await loadActivities();\n    return activities.filter((activity)=>activity.customerId === customerId);\n}\n/**\n * Get recent activities (last 30 days)\n */ async function getRecentActivities(days = 30) {\n    const activities = await loadActivities();\n    const cutoffDate = new Date();\n    cutoffDate.setDate(cutoffDate.getDate() - days);\n    return activities.filter((activity)=>new Date(activity.timestamp) >= cutoffDate).sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());\n}\n// UTILITY FUNCTIONS\n/**\n * Get customer overview with stats\n */ async function getCustomerOverview(customerId) {\n    const customer = await getCustomerById(customerId);\n    if (!customer) return null;\n    const interactions = await getInteractionsByCustomerId(customerId);\n    const followups = await getFollowUpsByCustomerId(customerId);\n    const activities = await getActivitiesByCustomerId(customerId);\n    return {\n        customer,\n        stats: {\n            totalInteractions: interactions.length,\n            pendingFollowUps: followups.filter((f)=>f.status === 'pending').length,\n            recentActivities: activities.filter((a)=>new Date(a.timestamp) >= new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)).length,\n            lastInteraction: interactions.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())[0]?.createdAt\n        },\n        recentInteractions: interactions.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()).slice(0, 5),\n        upcomingFollowUps: followups.filter((f)=>f.status === 'pending').sort((a, b)=>new Date(a.scheduledDate).getTime() - new Date(b.scheduledDate).getTime()).slice(0, 3)\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/crmStorage.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/uuid","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/jws","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/bcryptjs","vendor-chunks/safe-buffer","vendor-chunks/ms","vendor-chunks/lodash.once","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isinteger","vendor-chunks/lodash.isboolean","vendor-chunks/lodash.includes","vendor-chunks/jwa","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcrm%2Fstats%2Froute&page=%2Fapi%2Fadmin%2Fcrm%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcrm%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();