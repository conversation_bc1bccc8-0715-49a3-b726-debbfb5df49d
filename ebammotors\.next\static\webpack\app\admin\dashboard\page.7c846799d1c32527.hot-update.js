"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/admin/CRMManagement.tsx":
/*!************************************************!*\
  !*** ./src/components/admin/CRMManagement.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CRMManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,Clock,DollarSign,Edit,Eye,Mail,MapPin,MessageSquare,Phone,RefreshCw,Search,Star,Target,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,Clock,DollarSign,Edit,Eye,Mail,MapPin,MessageSquare,Phone,RefreshCw,Search,Star,Target,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,Clock,DollarSign,Edit,Eye,Mail,MapPin,MessageSquare,Phone,RefreshCw,Search,Star,Target,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,Clock,DollarSign,Edit,Eye,Mail,MapPin,MessageSquare,Phone,RefreshCw,Search,Star,Target,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,Clock,DollarSign,Edit,Eye,Mail,MapPin,MessageSquare,Phone,RefreshCw,Search,Star,Target,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,Clock,DollarSign,Edit,Eye,Mail,MapPin,MessageSquare,Phone,RefreshCw,Search,Star,Target,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,Clock,DollarSign,Edit,Eye,Mail,MapPin,MessageSquare,Phone,RefreshCw,Search,Star,Target,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,Clock,DollarSign,Edit,Eye,Mail,MapPin,MessageSquare,Phone,RefreshCw,Search,Star,Target,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,Clock,DollarSign,Edit,Eye,Mail,MapPin,MessageSquare,Phone,RefreshCw,Search,Star,Target,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,Clock,DollarSign,Edit,Eye,Mail,MapPin,MessageSquare,Phone,RefreshCw,Search,Star,Target,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,Clock,DollarSign,Edit,Eye,Mail,MapPin,MessageSquare,Phone,RefreshCw,Search,Star,Target,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,Clock,DollarSign,Edit,Eye,Mail,MapPin,MessageSquare,Phone,RefreshCw,Search,Star,Target,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,Clock,DollarSign,Edit,Eye,Mail,MapPin,MessageSquare,Phone,RefreshCw,Search,Star,Target,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,Clock,DollarSign,Edit,Eye,Mail,MapPin,MessageSquare,Phone,RefreshCw,Search,Star,Target,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,Clock,DollarSign,Edit,Eye,Mail,MapPin,MessageSquare,Phone,RefreshCw,Search,Star,Target,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,Clock,DollarSign,Edit,Eye,Mail,MapPin,MessageSquare,Phone,RefreshCw,Search,Star,Target,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction CRMManagement() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    const [customers, setCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [leads, setLeads] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        customers: {\n            total: 0,\n            active: 0,\n            new: 0,\n            vip: 0\n        },\n        leads: {\n            total: 0,\n            new: 0,\n            qualified: 0,\n            converted: 0\n        },\n        revenue: {\n            total: 0,\n            thisMonth: 0,\n            averageOrderValue: 0\n        },\n        interactions: {\n            total: 0,\n            thisWeek: 0,\n            responseRate: 0\n        }\n    });\n    const [showAddCustomer, setShowAddCustomer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCreateLead, setShowCreateLead] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showScheduleFollowup, setShowScheduleFollowup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CRMManagement.useEffect\": ()=>{\n            fetchCRMData();\n        }\n    }[\"CRMManagement.useEffect\"], []);\n    const fetchCRMData = async ()=>{\n        setLoading(true);\n        try {\n            const token = localStorage.getItem('admin_token');\n            // Fetch customers\n            const customersResponse = await fetch('/api/customers', {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (customersResponse.ok) {\n                const customersData = await customersResponse.json();\n                setCustomers(customersData.customers || []);\n            }\n            // Fetch leads\n            const leadsResponse = await fetch('/api/leads', {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (leadsResponse.ok) {\n                const leadsData = await leadsResponse.json();\n                setLeads(leadsData.leads || []);\n            }\n            // Fetch CRM stats\n            const statsResponse = await fetch('/api/admin/crm/stats', {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (statsResponse.ok) {\n                const statsData = await statsResponse.json();\n                setStats(statsData);\n            }\n        } catch (error) {\n            console.error('Error fetching CRM data:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status.toLowerCase()){\n            case 'active':\n                return 'text-green-600 bg-green-100';\n            case 'new':\n                return 'text-blue-600 bg-blue-100';\n            case 'qualified':\n                return 'text-purple-600 bg-purple-100';\n            case 'converted':\n                return 'text-green-600 bg-green-100';\n            case 'lost':\n                return 'text-red-600 bg-red-100';\n            case 'nurturing':\n                return 'text-yellow-600 bg-yellow-100';\n            default:\n                return 'text-gray-600 bg-gray-100';\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority.toLowerCase()){\n            case 'urgent':\n                return 'text-red-600 bg-red-100';\n            case 'high':\n                return 'text-orange-600 bg-orange-100';\n            case 'medium':\n                return 'text-yellow-600 bg-yellow-100';\n            case 'low':\n                return 'text-green-600 bg-green-100';\n            default:\n                return 'text-gray-600 bg-gray-100';\n        }\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat('ja-JP', {\n            style: 'currency',\n            currency: 'JPY',\n            minimumFractionDigits: 0\n        }).format(amount);\n    };\n    const filteredCustomers = customers.filter((customer)=>{\n        const matchesSearch = !searchQuery || customer.personalInfo.name.toLowerCase().includes(searchQuery.toLowerCase()) || customer.personalInfo.email.toLowerCase().includes(searchQuery.toLowerCase());\n        const matchesStatus = statusFilter === 'all' || customer.status === statusFilter;\n        return matchesSearch && matchesStatus;\n    });\n    const filteredLeads = leads.filter((lead)=>{\n        const matchesSearch = !searchQuery || lead.customerInfo.name.toLowerCase().includes(searchQuery.toLowerCase()) || lead.customerInfo.email && lead.customerInfo.email.toLowerCase().includes(searchQuery.toLowerCase());\n        const matchesStatus = statusFilter === 'all' || lead.status === statusFilter;\n        return matchesSearch && matchesStatus;\n    });\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-6 bg-gray-200 rounded w-1/4 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded w-3/4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded w-1/2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                lineNumber: 210,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n            lineNumber: 209,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"CRM Management\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Manage customers, leads, and relationships\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: fetchCRMData,\n                                        className: \"flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Refresh\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Add Customer\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-1 bg-gray-100 rounded-lg p-1\",\n                        children: [\n                            {\n                                id: 'overview',\n                                label: 'Overview',\n                                icon: _barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n                            },\n                            {\n                                id: 'customers',\n                                label: 'Customers',\n                                icon: _barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n                            },\n                            {\n                                id: 'leads',\n                                label: 'Leads',\n                                icon: _barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n                            },\n                            {\n                                id: 'interactions',\n                                label: 'Interactions',\n                                icon: _barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                            }\n                        ].map((tab)=>{\n                            const Icon = tab.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(tab.id),\n                                className: \"flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors \".concat(activeTab === tab.id ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 17\n                                    }, this),\n                                    tab.label\n                                ]\n                            }, tab.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, this),\n            activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Total Customers\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-gray-900\",\n                                                    children: stats.customers.total\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-green-600\",\n                                                    children: [\n                                                        \"+\",\n                                                        stats.customers.new,\n                                                        \" new this month\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 bg-blue-100 rounded-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Active Leads\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-gray-900\",\n                                                    children: stats.leads.total\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-purple-600\",\n                                                    children: [\n                                                        stats.leads.qualified,\n                                                        \" qualified\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 bg-purple-100 rounded-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-6 h-6 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Total Revenue\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-gray-900\",\n                                                    children: formatCurrency(stats.revenue.total)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-green-600\",\n                                                    children: [\n                                                        \"Avg: \",\n                                                        formatCurrency(stats.revenue.averageOrderValue)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 bg-green-100 rounded-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-6 h-6 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Interactions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-gray-900\",\n                                                    children: stats.interactions.total\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-600\",\n                                                    children: [\n                                                        stats.interactions.thisWeek,\n                                                        \" this week\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 bg-orange-100 rounded-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-6 h-6 text-orange-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                children: \"Quick Actions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-5 h-5 text-blue-600 mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: \"Add New Customer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Create a new customer profile\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-5 h-5 text-purple-600 mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: \"Create Lead\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Add a new sales lead\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-5 h-5 text-green-600 mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: \"Schedule Follow-up\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Set reminder for customer contact\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                lineNumber: 276,\n                columnNumber: 9\n            }, this),\n            activeTab === 'customers' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Search customers...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: statusFilter,\n                                    onChange: (e)=>setStatusFilter(e.target.value),\n                                    className: \"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"all\",\n                                            children: \"All Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"active\",\n                                            children: \"Active\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"inactive\",\n                                            children: \"Inactive\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"new\",\n                                            children: \"New\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"vip\",\n                                            children: \"VIP\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                            lineNumber: 369,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow\",\n                        children: filteredCustomers.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-12 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                    children: \"No customers found\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Try adjusting your search or filters\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                            lineNumber: 397,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        className: \"bg-gray-50 border-b border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Customer\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Orders\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Total Spent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Tier\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                            lineNumber: 406,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        className: \"bg-white divide-y divide-gray-200\",\n                                        children: filteredCustomers.map((customer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"hover:bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                    children: customer.personalInfo.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                                    lineNumber: 420,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: customer.personalInfo.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                                    lineNumber: 421,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                customer.address.city && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-500 flex items-center mt-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"w-3 h-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                                            lineNumber: 424,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        customer.address.city,\n                                                                        \", \",\n                                                                        customer.address.country\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                                    lineNumber: 423,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                            lineNumber: 419,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 text-xs font-medium rounded-full \".concat(getStatusColor(customer.status)),\n                                                            children: customer.status\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 text-sm text-gray-900\",\n                                                        children: customer.totalOrders\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 text-sm text-gray-900\",\n                                                        children: formatCurrency(customer.totalSpent)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-yellow-400 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                                    lineNumber: 443,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-900\",\n                                                                    children: customer.membershipTier\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                                    lineNumber: 444,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                            lineNumber: 442,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-gray-400 hover:text-blue-600 transition-colors\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                                        lineNumber: 450,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                                    lineNumber: 449,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-gray-400 hover:text-green-600 transition-colors\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                                        lineNumber: 453,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                                    lineNumber: 452,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-gray-400 hover:text-purple-600 transition-colors\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                                        lineNumber: 456,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                                    lineNumber: 455,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, customer.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                            lineNumber: 403,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                        lineNumber: 395,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                lineNumber: 366,\n                columnNumber: 9\n            }, this),\n            activeTab === 'leads' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Search leads...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                    lineNumber: 476,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: statusFilter,\n                                    onChange: (e)=>setStatusFilter(e.target.value),\n                                    className: \"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"all\",\n                                            children: \"All Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"new\",\n                                            children: \"New\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"contacted\",\n                                            children: \"Contacted\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                            lineNumber: 493,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"qualified\",\n                                            children: \"Qualified\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"converted\",\n                                            children: \"Converted\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"lost\",\n                                            children: \"Lost\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                    lineNumber: 486,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                            lineNumber: 475,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                        lineNumber: 474,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow\",\n                        children: filteredLeads.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-12 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                    children: \"No leads found\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Try adjusting your search or filters\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                            lineNumber: 504,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"divide-y divide-gray-200\",\n                            children: filteredLeads.map((lead)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 hover:bg-gray-50 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-gray-900\",\n                                                                children: lead.customerInfo.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 text-xs font-medium rounded-full \".concat(getStatusColor(lead.status)),\n                                                                children: lead.status\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                                lineNumber: 517,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 text-xs font-medium rounded-full \".concat(getPriorityColor(lead.priority)),\n                                                                children: lead.priority\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                                lineNumber: 520,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4 text-sm text-gray-600 mb-3\",\n                                                        children: [\n                                                            lead.customerInfo.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                                        lineNumber: 528,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    lead.customerInfo.email\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                                lineNumber: 527,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            lead.customerInfo.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                                        lineNumber: 534,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    lead.customerInfo.phone\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                                lineNumber: 533,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            lead.customerInfo.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                                        lineNumber: 540,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    lead.customerInfo.location\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                                lineNumber: 539,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                        lineNumber: 525,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-700 mb-3\",\n                                                        children: lead.inquiry.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                        lineNumber: 546,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    lead.inquiry.productInterest && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-blue-600 mb-2\",\n                                                        children: [\n                                                            \"Interest: \",\n                                                            lead.inquiry.productInterest\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center text-sm text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                                lineNumber: 555,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Created \",\n                                                            new Date(lead.createdAt).toLocaleDateString(),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"mx-2\",\n                                                                children: \"•\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                                lineNumber: 557,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Source: \",\n                                                            lead.source\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                        lineNumber: 554,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                lineNumber: 514,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 ml-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-gray-400 hover:text-blue-600 transition-colors\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                            lineNumber: 564,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-gray-400 hover:text-green-600 transition-colors\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                        lineNumber: 566,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-gray-400 hover:text-purple-600 transition-colors\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                            lineNumber: 570,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                        lineNumber: 569,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                                lineNumber: 562,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 21\n                                    }, this)\n                                }, lead.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 19\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                        lineNumber: 502,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                lineNumber: 472,\n                columnNumber: 9\n            }, this),\n            activeTab === 'interactions' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Clock_DollarSign_Edit_Eye_Mail_MapPin_MessageSquare_Phone_RefreshCw_Search_Star_Target_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                            lineNumber: 586,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                            children: \"Interactions Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                            lineNumber: 587,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Interaction tracking and communication history coming soon\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                            lineNumber: 588,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                    lineNumber: 585,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n                lineNumber: 584,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\CRMManagement.tsx\",\n        lineNumber: 224,\n        columnNumber: 5\n    }, this);\n}\n_s(CRMManagement, \"AMb7rvwQo7L0IH9I4J4HaYsStAw=\");\n_c = CRMManagement;\nvar _c;\n$RefreshReg$(_c, \"CRMManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/CRMManagement.tsx\n"));

/***/ })

});