"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/canvg";
exports.ids = ["vendor-chunks/canvg"];
exports.modules = {

/***/ "(rsc)/./node_modules/canvg/lib/index.es.js":
/*!********************************************!*\
  !*** ./node_modules/canvg/lib/index.es.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AElement: () => (/* binding */ AElement),\n/* harmony export */   AnimateColorElement: () => (/* binding */ AnimateColorElement),\n/* harmony export */   AnimateElement: () => (/* binding */ AnimateElement),\n/* harmony export */   AnimateTransformElement: () => (/* binding */ AnimateTransformElement),\n/* harmony export */   BoundingBox: () => (/* binding */ BoundingBox),\n/* harmony export */   CB1: () => (/* binding */ CB1),\n/* harmony export */   CB2: () => (/* binding */ CB2),\n/* harmony export */   CB3: () => (/* binding */ CB3),\n/* harmony export */   CB4: () => (/* binding */ CB4),\n/* harmony export */   Canvg: () => (/* binding */ Canvg),\n/* harmony export */   CircleElement: () => (/* binding */ CircleElement),\n/* harmony export */   ClipPathElement: () => (/* binding */ ClipPathElement),\n/* harmony export */   DefsElement: () => (/* binding */ DefsElement),\n/* harmony export */   DescElement: () => (/* binding */ DescElement),\n/* harmony export */   Document: () => (/* binding */ Document),\n/* harmony export */   Element: () => (/* binding */ Element),\n/* harmony export */   EllipseElement: () => (/* binding */ EllipseElement),\n/* harmony export */   FeColorMatrixElement: () => (/* binding */ FeColorMatrixElement),\n/* harmony export */   FeCompositeElement: () => (/* binding */ FeCompositeElement),\n/* harmony export */   FeDropShadowElement: () => (/* binding */ FeDropShadowElement),\n/* harmony export */   FeGaussianBlurElement: () => (/* binding */ FeGaussianBlurElement),\n/* harmony export */   FeMorphologyElement: () => (/* binding */ FeMorphologyElement),\n/* harmony export */   FilterElement: () => (/* binding */ FilterElement),\n/* harmony export */   Font: () => (/* binding */ Font),\n/* harmony export */   FontElement: () => (/* binding */ FontElement),\n/* harmony export */   FontFaceElement: () => (/* binding */ FontFaceElement),\n/* harmony export */   GElement: () => (/* binding */ GElement),\n/* harmony export */   GlyphElement: () => (/* binding */ GlyphElement),\n/* harmony export */   GradientElement: () => (/* binding */ GradientElement),\n/* harmony export */   ImageElement: () => (/* binding */ ImageElement),\n/* harmony export */   LineElement: () => (/* binding */ LineElement),\n/* harmony export */   LinearGradientElement: () => (/* binding */ LinearGradientElement),\n/* harmony export */   MarkerElement: () => (/* binding */ MarkerElement),\n/* harmony export */   MaskElement: () => (/* binding */ MaskElement),\n/* harmony export */   Matrix: () => (/* binding */ Matrix),\n/* harmony export */   MissingGlyphElement: () => (/* binding */ MissingGlyphElement),\n/* harmony export */   Mouse: () => (/* binding */ Mouse),\n/* harmony export */   PSEUDO_ZERO: () => (/* binding */ PSEUDO_ZERO),\n/* harmony export */   Parser: () => (/* binding */ Parser),\n/* harmony export */   PathElement: () => (/* binding */ PathElement),\n/* harmony export */   PathParser: () => (/* binding */ PathParser),\n/* harmony export */   PatternElement: () => (/* binding */ PatternElement),\n/* harmony export */   Point: () => (/* binding */ Point),\n/* harmony export */   PolygonElement: () => (/* binding */ PolygonElement),\n/* harmony export */   PolylineElement: () => (/* binding */ PolylineElement),\n/* harmony export */   Property: () => (/* binding */ Property),\n/* harmony export */   QB1: () => (/* binding */ QB1),\n/* harmony export */   QB2: () => (/* binding */ QB2),\n/* harmony export */   QB3: () => (/* binding */ QB3),\n/* harmony export */   RadialGradientElement: () => (/* binding */ RadialGradientElement),\n/* harmony export */   RectElement: () => (/* binding */ RectElement),\n/* harmony export */   RenderedElement: () => (/* binding */ RenderedElement),\n/* harmony export */   Rotate: () => (/* binding */ Rotate),\n/* harmony export */   SVGElement: () => (/* binding */ SVGElement),\n/* harmony export */   SVGFontLoader: () => (/* binding */ SVGFontLoader),\n/* harmony export */   Scale: () => (/* binding */ Scale),\n/* harmony export */   Screen: () => (/* binding */ Screen),\n/* harmony export */   Skew: () => (/* binding */ Skew),\n/* harmony export */   SkewX: () => (/* binding */ SkewX),\n/* harmony export */   SkewY: () => (/* binding */ SkewY),\n/* harmony export */   StopElement: () => (/* binding */ StopElement),\n/* harmony export */   StyleElement: () => (/* binding */ StyleElement),\n/* harmony export */   SymbolElement: () => (/* binding */ SymbolElement),\n/* harmony export */   TRefElement: () => (/* binding */ TRefElement),\n/* harmony export */   TSpanElement: () => (/* binding */ TSpanElement),\n/* harmony export */   TextElement: () => (/* binding */ TextElement),\n/* harmony export */   TextPathElement: () => (/* binding */ TextPathElement),\n/* harmony export */   TitleElement: () => (/* binding */ TitleElement),\n/* harmony export */   Transform: () => (/* binding */ Transform),\n/* harmony export */   Translate: () => (/* binding */ Translate),\n/* harmony export */   UnknownElement: () => (/* binding */ UnknownElement),\n/* harmony export */   UseElement: () => (/* binding */ UseElement),\n/* harmony export */   ViewPort: () => (/* binding */ ViewPort),\n/* harmony export */   compressSpaces: () => (/* binding */ compressSpaces),\n/* harmony export */   \"default\": () => (/* binding */ Canvg),\n/* harmony export */   getSelectorSpecificity: () => (/* binding */ getSelectorSpecificity),\n/* harmony export */   normalizeAttributeName: () => (/* binding */ normalizeAttributeName),\n/* harmony export */   normalizeColor: () => (/* binding */ normalizeColor),\n/* harmony export */   parseExternalUrl: () => (/* binding */ parseExternalUrl),\n/* harmony export */   presets: () => (/* binding */ index),\n/* harmony export */   toNumbers: () => (/* binding */ toNumbers),\n/* harmony export */   trimLeft: () => (/* binding */ trimLeft),\n/* harmony export */   trimRight: () => (/* binding */ trimRight),\n/* harmony export */   vectorMagnitude: () => (/* binding */ vectorMagnitude),\n/* harmony export */   vectorsAngle: () => (/* binding */ vectorsAngle),\n/* harmony export */   vectorsRatio: () => (/* binding */ vectorsRatio)\n/* harmony export */ });\n/* harmony import */ var core_js_modules_es_promise_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.promise.js */ \"(rsc)/./node_modules/core-js/modules/es.promise.js\");\n/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ \"(rsc)/./node_modules/@babel/runtime/helpers/asyncToGenerator.js\");\n/* harmony import */ var core_js_modules_es_string_match_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.string.match.js */ \"(rsc)/./node_modules/core-js/modules/es.string.match.js\");\n/* harmony import */ var core_js_modules_es_string_replace_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.string.replace.js */ \"(rsc)/./node_modules/core-js/modules/es.string.replace.js\");\n/* harmony import */ var core_js_modules_es_string_starts_with_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.string.starts-with.js */ \"(rsc)/./node_modules/core-js/modules/es.string.starts-with.js\");\n/* harmony import */ var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/es.array.iterator.js */ \"(rsc)/./node_modules/core-js/modules/es.array.iterator.js\");\n/* harmony import */ var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! core-js/modules/web.dom-collections.iterator.js */ \"(rsc)/./node_modules/core-js/modules/web.dom-collections.iterator.js\");\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"(rsc)/./node_modules/@babel/runtime/helpers/defineProperty.js\");\n/* harmony import */ var core_js_modules_es_array_reduce_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! core-js/modules/es.array.reduce.js */ \"(rsc)/./node_modules/core-js/modules/es.array.reduce.js\");\n/* harmony import */ var core_js_modules_es_string_ends_with_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! core-js/modules/es.string.ends-with.js */ \"(rsc)/./node_modules/core-js/modules/es.string.ends-with.js\");\n/* harmony import */ var core_js_modules_es_string_split_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! core-js/modules/es.string.split.js */ \"(rsc)/./node_modules/core-js/modules/es.string.split.js\");\n/* harmony import */ var raf__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! raf */ \"(rsc)/./node_modules/raf/index.js\");\n/* harmony import */ var core_js_modules_es_string_trim_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! core-js/modules/es.string.trim.js */ \"(rsc)/./node_modules/core-js/modules/es.string.trim.js\");\n/* harmony import */ var rgbcolor__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! rgbcolor */ \"(rsc)/./node_modules/rgbcolor/index.js\");\n/* harmony import */ var core_js_modules_es_array_index_of_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! core-js/modules/es.array.index-of.js */ \"(rsc)/./node_modules/core-js/modules/es.array.index-of.js\");\n/* harmony import */ var core_js_modules_es_string_includes_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! core-js/modules/es.string.includes.js */ \"(rsc)/./node_modules/core-js/modules/es.string.includes.js\");\n/* harmony import */ var core_js_modules_es_array_reverse_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! core-js/modules/es.array.reverse.js */ \"(rsc)/./node_modules/core-js/modules/es.array.reverse.js\");\n/* harmony import */ var svg_pathdata__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! svg-pathdata */ \"(rsc)/./node_modules/svg-pathdata/lib/SVGPathData.module.js\");\n/* harmony import */ var core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! core-js/modules/es.regexp.to-string.js */ \"(rsc)/./node_modules/core-js/modules/es.regexp.to-string.js\");\n/* harmony import */ var stackblur_canvas__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! stackblur-canvas */ \"(rsc)/./node_modules/stackblur-canvas/dist/stackblur-es.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\r\n * Options preset for `OffscreenCanvas`.\r\n * @param config - Preset requirements.\r\n * @param config.DOMParser - XML/HTML parser from string into DOM Document.\r\n * @returns Preset object.\r\n */\nfunction offscreen() {\n  var {\n    DOMParser: DOMParserFallback\n  } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var preset = {\n    window: null,\n    ignoreAnimation: true,\n    ignoreMouse: true,\n    DOMParser: DOMParserFallback,\n\n    createCanvas(width, height) {\n      return new OffscreenCanvas(width, height);\n    },\n\n    createImage(url) {\n      return _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__(function* () {\n        var response = yield fetch(url);\n        var blob = yield response.blob();\n        var img = yield createImageBitmap(blob);\n        return img;\n      })();\n    }\n\n  };\n\n  if (typeof DOMParser !== 'undefined' || typeof DOMParserFallback === 'undefined') {\n    Reflect.deleteProperty(preset, 'DOMParser');\n  }\n\n  return preset;\n}\n\n/**\r\n * Options preset for `node-canvas`.\r\n * @param config - Preset requirements.\r\n * @param config.DOMParser - XML/HTML parser from string into DOM Document.\r\n * @param config.canvas - `node-canvas` exports.\r\n * @param config.fetch - WHATWG-compatible `fetch` function.\r\n * @returns Preset object.\r\n */\nfunction node(_ref) {\n  var {\n    DOMParser,\n    canvas,\n    fetch\n  } = _ref;\n  return {\n    window: null,\n    ignoreAnimation: true,\n    ignoreMouse: true,\n    DOMParser,\n    fetch,\n    createCanvas: canvas.createCanvas,\n    createImage: canvas.loadImage\n  };\n}\n\nvar index = /*#__PURE__*/Object.freeze({\n\t__proto__: null,\n\toffscreen: offscreen,\n\tnode: node\n});\n\n/**\r\n * HTML-safe compress white-spaces.\r\n * @param str - String to compress.\r\n * @returns String.\r\n */\nfunction compressSpaces(str) {\n  return str.replace(/(?!\\u3000)\\s+/gm, ' ');\n}\n/**\r\n * HTML-safe left trim.\r\n * @param str - String to trim.\r\n * @returns String.\r\n */\n\nfunction trimLeft(str) {\n  return str.replace(/^[\\n \\t]+/, '');\n}\n/**\r\n * HTML-safe right trim.\r\n * @param str - String to trim.\r\n * @returns String.\r\n */\n\nfunction trimRight(str) {\n  return str.replace(/[\\n \\t]+$/, '');\n}\n/**\r\n * String to numbers array.\r\n * @param str - Numbers string.\r\n * @returns Numbers array.\r\n */\n\nfunction toNumbers(str) {\n  var matches = (str || '').match(/-?(\\d+(?:\\.\\d*(?:[eE][+-]?\\d+)?)?|\\.\\d+)(?=\\D|$)/gm) || [];\n  return matches.map(parseFloat);\n} // Microsoft Edge fix\n\nvar allUppercase = /^[A-Z-]+$/;\n/**\r\n * Normalize attribute name.\r\n * @param name - Attribute name.\r\n * @returns Normalized attribute name.\r\n */\n\nfunction normalizeAttributeName(name) {\n  if (allUppercase.test(name)) {\n    return name.toLowerCase();\n  }\n\n  return name;\n}\n/**\r\n * Parse external URL.\r\n * @param url - CSS url string.\r\n * @returns Parsed URL.\r\n */\n\nfunction parseExternalUrl(url) {\n  //                      single quotes [2]\n  //                      v         double quotes [3]\n  //                      v         v         no quotes [4]\n  //                      v         v         v\n  var urlMatch = /url\\(('([^']+)'|\"([^\"]+)\"|([^'\")]+))\\)/.exec(url) || [];\n  return urlMatch[2] || urlMatch[3] || urlMatch[4];\n}\n/**\r\n * Transform floats to integers in rgb colors.\r\n * @param color - Color to normalize.\r\n * @returns Normalized color.\r\n */\n\nfunction normalizeColor(color) {\n  if (!color.startsWith('rgb')) {\n    return color;\n  }\n\n  var rgbParts = 3;\n  var normalizedColor = color.replace(/\\d+(\\.\\d+)?/g, (num, isFloat) => rgbParts-- && isFloat ? String(Math.round(parseFloat(num))) : num);\n  return normalizedColor;\n}\n\n// slightly modified version of https://github.com/keeganstreet/specificity/blob/master/specificity.js\nvar attributeRegex = /(\\[[^\\]]+\\])/g;\nvar idRegex = /(#[^\\s+>~.[:]+)/g;\nvar classRegex = /(\\.[^\\s+>~.[:]+)/g;\nvar pseudoElementRegex = /(::[^\\s+>~.[:]+|:first-line|:first-letter|:before|:after)/gi;\nvar pseudoClassWithBracketsRegex = /(:[\\w-]+\\([^)]*\\))/gi;\nvar pseudoClassRegex = /(:[^\\s+>~.[:]+)/g;\nvar elementRegex = /([^\\s+>~.[:]+)/g;\n\nfunction findSelectorMatch(selector, regex) {\n  var matches = regex.exec(selector);\n\n  if (!matches) {\n    return [selector, 0];\n  }\n\n  return [selector.replace(regex, ' '), matches.length];\n}\n/**\r\n * Measure selector specificity.\r\n * @param selector - Selector to measure.\r\n * @returns Specificity.\r\n */\n\n\nfunction getSelectorSpecificity(selector) {\n  var specificity = [0, 0, 0];\n  var currentSelector = selector.replace(/:not\\(([^)]*)\\)/g, '     $1 ').replace(/{[\\s\\S]*/gm, ' ');\n  var delta = 0;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, attributeRegex);\n  specificity[1] += delta;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, idRegex);\n  specificity[0] += delta;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, classRegex);\n  specificity[1] += delta;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, pseudoElementRegex);\n  specificity[2] += delta;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, pseudoClassWithBracketsRegex);\n  specificity[1] += delta;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, pseudoClassRegex);\n  specificity[1] += delta;\n  currentSelector = currentSelector.replace(/[*\\s+>~]/g, ' ').replace(/[#.]/g, ' ');\n  [currentSelector, delta] = findSelectorMatch(currentSelector, elementRegex); // lgtm [js/useless-assignment-to-local]\n\n  specificity[2] += delta;\n  return specificity.join('');\n}\n\nvar PSEUDO_ZERO = .00000001;\n/**\r\n * Vector magnitude.\r\n * @param v\r\n * @returns Number result.\r\n */\n\nfunction vectorMagnitude(v) {\n  return Math.sqrt(Math.pow(v[0], 2) + Math.pow(v[1], 2));\n}\n/**\r\n * Ratio between two vectors.\r\n * @param u\r\n * @param v\r\n * @returns Number result.\r\n */\n\nfunction vectorsRatio(u, v) {\n  return (u[0] * v[0] + u[1] * v[1]) / (vectorMagnitude(u) * vectorMagnitude(v));\n}\n/**\r\n * Angle between two vectors.\r\n * @param u\r\n * @param v\r\n * @returns Number result.\r\n */\n\nfunction vectorsAngle(u, v) {\n  return (u[0] * v[1] < u[1] * v[0] ? -1 : 1) * Math.acos(vectorsRatio(u, v));\n}\nfunction CB1(t) {\n  return t * t * t;\n}\nfunction CB2(t) {\n  return 3 * t * t * (1 - t);\n}\nfunction CB3(t) {\n  return 3 * t * (1 - t) * (1 - t);\n}\nfunction CB4(t) {\n  return (1 - t) * (1 - t) * (1 - t);\n}\nfunction QB1(t) {\n  return t * t;\n}\nfunction QB2(t) {\n  return 2 * t * (1 - t);\n}\nfunction QB3(t) {\n  return (1 - t) * (1 - t);\n}\n\nclass Property {\n  constructor(document, name, value) {\n    this.document = document;\n    this.name = name;\n    this.value = value;\n    this.isNormalizedColor = false;\n  }\n\n  static empty(document) {\n    return new Property(document, 'EMPTY', '');\n  }\n\n  split() {\n    var separator = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : ' ';\n    var {\n      document,\n      name\n    } = this;\n    return compressSpaces(this.getString()).trim().split(separator).map(value => new Property(document, name, value));\n  }\n\n  hasValue(zeroIsValue) {\n    var {\n      value\n    } = this;\n    return value !== null && value !== '' && (zeroIsValue || value !== 0) && typeof value !== 'undefined';\n  }\n\n  isString(regexp) {\n    var {\n      value\n    } = this;\n    var result = typeof value === 'string';\n\n    if (!result || !regexp) {\n      return result;\n    }\n\n    return regexp.test(value);\n  }\n\n  isUrlDefinition() {\n    return this.isString(/^url\\(/);\n  }\n\n  isPixels() {\n    if (!this.hasValue()) {\n      return false;\n    }\n\n    var asString = this.getString();\n\n    switch (true) {\n      case asString.endsWith('px'):\n      case /^[0-9]+$/.test(asString):\n        return true;\n\n      default:\n        return false;\n    }\n  }\n\n  setValue(value) {\n    this.value = value;\n    return this;\n  }\n\n  getValue(def) {\n    if (typeof def === 'undefined' || this.hasValue()) {\n      return this.value;\n    }\n\n    return def;\n  }\n\n  getNumber(def) {\n    if (!this.hasValue()) {\n      if (typeof def === 'undefined') {\n        return 0;\n      }\n\n      return parseFloat(def);\n    }\n\n    var {\n      value\n    } = this;\n    var n = parseFloat(value);\n\n    if (this.isString(/%$/)) {\n      n /= 100.0;\n    }\n\n    return n;\n  }\n\n  getString(def) {\n    if (typeof def === 'undefined' || this.hasValue()) {\n      return typeof this.value === 'undefined' ? '' : String(this.value);\n    }\n\n    return String(def);\n  }\n\n  getColor(def) {\n    var color = this.getString(def);\n\n    if (this.isNormalizedColor) {\n      return color;\n    }\n\n    this.isNormalizedColor = true;\n    color = normalizeColor(color);\n    this.value = color;\n    return color;\n  }\n\n  getDpi() {\n    return 96.0; // TODO: compute?\n  }\n\n  getRem() {\n    return this.document.rootEmSize;\n  }\n\n  getEm() {\n    return this.document.emSize;\n  }\n\n  getUnits() {\n    return this.getString().replace(/[0-9.-]/g, '');\n  }\n\n  getPixels(axisOrIsFontSize) {\n    var processPercent = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n    if (!this.hasValue()) {\n      return 0;\n    }\n\n    var [axis, isFontSize] = typeof axisOrIsFontSize === 'boolean' ? [undefined, axisOrIsFontSize] : [axisOrIsFontSize];\n    var {\n      viewPort\n    } = this.document.screen;\n\n    switch (true) {\n      case this.isString(/vmin$/):\n        return this.getNumber() / 100.0 * Math.min(viewPort.computeSize('x'), viewPort.computeSize('y'));\n\n      case this.isString(/vmax$/):\n        return this.getNumber() / 100.0 * Math.max(viewPort.computeSize('x'), viewPort.computeSize('y'));\n\n      case this.isString(/vw$/):\n        return this.getNumber() / 100.0 * viewPort.computeSize('x');\n\n      case this.isString(/vh$/):\n        return this.getNumber() / 100.0 * viewPort.computeSize('y');\n\n      case this.isString(/rem$/):\n        return this.getNumber() * this.getRem();\n\n      case this.isString(/em$/):\n        return this.getNumber() * this.getEm();\n\n      case this.isString(/ex$/):\n        return this.getNumber() * this.getEm() / 2.0;\n\n      case this.isString(/px$/):\n        return this.getNumber();\n\n      case this.isString(/pt$/):\n        return this.getNumber() * this.getDpi() * (1.0 / 72.0);\n\n      case this.isString(/pc$/):\n        return this.getNumber() * 15;\n\n      case this.isString(/cm$/):\n        return this.getNumber() * this.getDpi() / 2.54;\n\n      case this.isString(/mm$/):\n        return this.getNumber() * this.getDpi() / 25.4;\n\n      case this.isString(/in$/):\n        return this.getNumber() * this.getDpi();\n\n      case this.isString(/%$/) && isFontSize:\n        return this.getNumber() * this.getEm();\n\n      case this.isString(/%$/):\n        return this.getNumber() * viewPort.computeSize(axis);\n\n      default:\n        {\n          var n = this.getNumber();\n\n          if (processPercent && n < 1.0) {\n            return n * viewPort.computeSize(axis);\n          }\n\n          return n;\n        }\n    }\n  }\n\n  getMilliseconds() {\n    if (!this.hasValue()) {\n      return 0;\n    }\n\n    if (this.isString(/ms$/)) {\n      return this.getNumber();\n    }\n\n    return this.getNumber() * 1000;\n  }\n\n  getRadians() {\n    if (!this.hasValue()) {\n      return 0;\n    }\n\n    switch (true) {\n      case this.isString(/deg$/):\n        return this.getNumber() * (Math.PI / 180.0);\n\n      case this.isString(/grad$/):\n        return this.getNumber() * (Math.PI / 200.0);\n\n      case this.isString(/rad$/):\n        return this.getNumber();\n\n      default:\n        return this.getNumber() * (Math.PI / 180.0);\n    }\n  }\n\n  getDefinition() {\n    var asString = this.getString();\n    var name = /#([^)'\"]+)/.exec(asString);\n\n    if (name) {\n      name = name[1];\n    }\n\n    if (!name) {\n      name = asString;\n    }\n\n    return this.document.definitions[name];\n  }\n\n  getFillStyleDefinition(element, opacity) {\n    var def = this.getDefinition();\n\n    if (!def) {\n      return null;\n    } // gradient\n\n\n    if (typeof def.createGradient === 'function') {\n      return def.createGradient(this.document.ctx, element, opacity);\n    } // pattern\n\n\n    if (typeof def.createPattern === 'function') {\n      if (def.getHrefAttribute().hasValue()) {\n        var patternTransform = def.getAttribute('patternTransform');\n        def = def.getHrefAttribute().getDefinition();\n\n        if (patternTransform.hasValue()) {\n          def.getAttribute('patternTransform', true).setValue(patternTransform.value);\n        }\n      }\n\n      return def.createPattern(this.document.ctx, element, opacity);\n    }\n\n    return null;\n  }\n\n  getTextBaseline() {\n    if (!this.hasValue()) {\n      return null;\n    }\n\n    return Property.textBaselineMapping[this.getString()];\n  }\n\n  addOpacity(opacity) {\n    var value = this.getColor();\n    var len = value.length;\n    var commas = 0; // Simulate old RGBColor version, which can't parse rgba.\n\n    for (var i = 0; i < len; i++) {\n      if (value[i] === ',') {\n        commas++;\n      }\n\n      if (commas === 3) {\n        break;\n      }\n    }\n\n    if (opacity.hasValue() && this.isString() && commas !== 3) {\n      var color = new rgbcolor__WEBPACK_IMPORTED_MODULE_13__(value);\n\n      if (color.ok) {\n        color.alpha = opacity.getNumber();\n        value = color.toRGBA();\n      }\n    }\n\n    return new Property(this.document, this.name, value);\n  }\n\n}\nProperty.textBaselineMapping = {\n  'baseline': 'alphabetic',\n  'before-edge': 'top',\n  'text-before-edge': 'top',\n  'middle': 'middle',\n  'central': 'middle',\n  'after-edge': 'bottom',\n  'text-after-edge': 'bottom',\n  'ideographic': 'ideographic',\n  'alphabetic': 'alphabetic',\n  'hanging': 'hanging',\n  'mathematical': 'alphabetic'\n};\n\nclass ViewPort {\n  constructor() {\n    this.viewPorts = [];\n  }\n\n  clear() {\n    this.viewPorts = [];\n  }\n\n  setCurrent(width, height) {\n    this.viewPorts.push({\n      width,\n      height\n    });\n  }\n\n  removeCurrent() {\n    this.viewPorts.pop();\n  }\n\n  getCurrent() {\n    var {\n      viewPorts\n    } = this;\n    return viewPorts[viewPorts.length - 1];\n  }\n\n  get width() {\n    return this.getCurrent().width;\n  }\n\n  get height() {\n    return this.getCurrent().height;\n  }\n\n  computeSize(d) {\n    if (typeof d === 'number') {\n      return d;\n    }\n\n    if (d === 'x') {\n      return this.width;\n    }\n\n    if (d === 'y') {\n      return this.height;\n    }\n\n    return Math.sqrt(Math.pow(this.width, 2) + Math.pow(this.height, 2)) / Math.sqrt(2);\n  }\n\n}\n\nclass Point {\n  constructor(x, y) {\n    this.x = x;\n    this.y = y;\n  }\n\n  static parse(point) {\n    var defaultValue = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n    var [x = defaultValue, y = defaultValue] = toNumbers(point);\n    return new Point(x, y);\n  }\n\n  static parseScale(scale) {\n    var defaultValue = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n    var [x = defaultValue, y = x] = toNumbers(scale);\n    return new Point(x, y);\n  }\n\n  static parsePath(path) {\n    var points = toNumbers(path);\n    var len = points.length;\n    var pathPoints = [];\n\n    for (var i = 0; i < len; i += 2) {\n      pathPoints.push(new Point(points[i], points[i + 1]));\n    }\n\n    return pathPoints;\n  }\n\n  angleTo(point) {\n    return Math.atan2(point.y - this.y, point.x - this.x);\n  }\n\n  applyTransform(transform) {\n    var {\n      x,\n      y\n    } = this;\n    var xp = x * transform[0] + y * transform[2] + transform[4];\n    var yp = x * transform[1] + y * transform[3] + transform[5];\n    this.x = xp;\n    this.y = yp;\n  }\n\n}\n\nclass Mouse {\n  constructor(screen) {\n    this.screen = screen;\n    this.working = false;\n    this.events = [];\n    this.eventElements = []; // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n\n    this.onClick = this.onClick.bind(this); // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n\n    this.onMouseMove = this.onMouseMove.bind(this);\n  }\n\n  isWorking() {\n    return this.working;\n  }\n\n  start() {\n    if (this.working) {\n      return;\n    }\n\n    var {\n      screen,\n      onClick,\n      onMouseMove\n    } = this;\n    var canvas = screen.ctx.canvas;\n    canvas.onclick = onClick;\n    canvas.onmousemove = onMouseMove;\n    this.working = true;\n  }\n\n  stop() {\n    if (!this.working) {\n      return;\n    }\n\n    var canvas = this.screen.ctx.canvas;\n    this.working = false;\n    canvas.onclick = null;\n    canvas.onmousemove = null;\n  }\n\n  hasEvents() {\n    return this.working && this.events.length > 0;\n  }\n\n  runEvents() {\n    if (!this.working) {\n      return;\n    }\n\n    var {\n      screen: document,\n      events,\n      eventElements\n    } = this;\n    var {\n      style\n    } = document.ctx.canvas;\n\n    if (style) {\n      style.cursor = '';\n    }\n\n    events.forEach((_ref, i) => {\n      var {\n        run\n      } = _ref;\n      var element = eventElements[i];\n\n      while (element) {\n        run(element);\n        element = element.parent;\n      }\n    }); // done running, clear\n\n    this.events = [];\n    this.eventElements = [];\n  }\n\n  checkPath(element, ctx) {\n    if (!this.working || !ctx) {\n      return;\n    }\n\n    var {\n      events,\n      eventElements\n    } = this;\n    events.forEach((_ref2, i) => {\n      var {\n        x,\n        y\n      } = _ref2;\n\n      if (!eventElements[i] && ctx.isPointInPath && ctx.isPointInPath(x, y)) {\n        eventElements[i] = element;\n      }\n    });\n  }\n\n  checkBoundingBox(element, boundingBox) {\n    if (!this.working || !boundingBox) {\n      return;\n    }\n\n    var {\n      events,\n      eventElements\n    } = this;\n    events.forEach((_ref3, i) => {\n      var {\n        x,\n        y\n      } = _ref3;\n\n      if (!eventElements[i] && boundingBox.isPointInBox(x, y)) {\n        eventElements[i] = element;\n      }\n    });\n  }\n\n  mapXY(x, y) {\n    var {\n      window,\n      ctx\n    } = this.screen;\n    var point = new Point(x, y);\n    var element = ctx.canvas;\n\n    while (element) {\n      point.x -= element.offsetLeft;\n      point.y -= element.offsetTop;\n      element = element.offsetParent;\n    }\n\n    if (window.scrollX) {\n      point.x += window.scrollX;\n    }\n\n    if (window.scrollY) {\n      point.y += window.scrollY;\n    }\n\n    return point;\n  }\n\n  onClick(event) {\n    var {\n      x,\n      y\n    } = this.mapXY(event.clientX, event.clientY);\n    this.events.push({\n      type: 'onclick',\n      x,\n      y,\n\n      run(eventTarget) {\n        if (eventTarget.onClick) {\n          eventTarget.onClick();\n        }\n      }\n\n    });\n  }\n\n  onMouseMove(event) {\n    var {\n      x,\n      y\n    } = this.mapXY(event.clientX, event.clientY);\n    this.events.push({\n      type: 'onmousemove',\n      x,\n      y,\n\n      run(eventTarget) {\n        if (eventTarget.onMouseMove) {\n          eventTarget.onMouseMove();\n        }\n      }\n\n    });\n  }\n\n}\n\nvar defaultWindow = typeof window !== 'undefined' ? window : null;\nvar defaultFetch$1 = typeof fetch !== 'undefined' ? fetch.bind(undefined) // `fetch` depends on context: `someObject.fetch(...)` will throw error.\n: null;\nclass Screen {\n  constructor(ctx) {\n    var {\n      fetch = defaultFetch$1,\n      window = defaultWindow\n    } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.ctx = ctx;\n    this.FRAMERATE = 30;\n    this.MAX_VIRTUAL_PIXELS = 30000;\n    this.CLIENT_WIDTH = 800;\n    this.CLIENT_HEIGHT = 600;\n    this.viewPort = new ViewPort();\n    this.mouse = new Mouse(this);\n    this.animations = [];\n    this.waits = [];\n    this.frameDuration = 0;\n    this.isReadyLock = false;\n    this.isFirstRender = true;\n    this.intervalId = null;\n    this.window = window;\n    this.fetch = fetch;\n  }\n\n  wait(checker) {\n    this.waits.push(checker);\n  }\n\n  ready() {\n    // eslint-disable-next-line @typescript-eslint/no-misused-promises\n    if (!this.readyPromise) {\n      return Promise.resolve();\n    }\n\n    return this.readyPromise;\n  }\n\n  isReady() {\n    if (this.isReadyLock) {\n      return true;\n    }\n\n    var isReadyLock = this.waits.every(_ => _());\n\n    if (isReadyLock) {\n      this.waits = [];\n\n      if (this.resolveReady) {\n        this.resolveReady();\n      }\n    }\n\n    this.isReadyLock = isReadyLock;\n    return isReadyLock;\n  }\n\n  setDefaults(ctx) {\n    // initial values and defaults\n    ctx.strokeStyle = 'rgba(0,0,0,0)';\n    ctx.lineCap = 'butt';\n    ctx.lineJoin = 'miter';\n    ctx.miterLimit = 4;\n  }\n\n  setViewBox(_ref) {\n    var {\n      document,\n      ctx,\n      aspectRatio,\n      width,\n      desiredWidth,\n      height,\n      desiredHeight,\n      minX = 0,\n      minY = 0,\n      refX,\n      refY,\n      clip = false,\n      clipX = 0,\n      clipY = 0\n    } = _ref;\n    // aspect ratio - http://www.w3.org/TR/SVG/coords.html#PreserveAspectRatioAttribute\n    var cleanAspectRatio = compressSpaces(aspectRatio).replace(/^defer\\s/, ''); // ignore defer\n\n    var [aspectRatioAlign, aspectRatioMeetOrSlice] = cleanAspectRatio.split(' ');\n    var align = aspectRatioAlign || 'xMidYMid';\n    var meetOrSlice = aspectRatioMeetOrSlice || 'meet'; // calculate scale\n\n    var scaleX = width / desiredWidth;\n    var scaleY = height / desiredHeight;\n    var scaleMin = Math.min(scaleX, scaleY);\n    var scaleMax = Math.max(scaleX, scaleY);\n    var finalDesiredWidth = desiredWidth;\n    var finalDesiredHeight = desiredHeight;\n\n    if (meetOrSlice === 'meet') {\n      finalDesiredWidth *= scaleMin;\n      finalDesiredHeight *= scaleMin;\n    }\n\n    if (meetOrSlice === 'slice') {\n      finalDesiredWidth *= scaleMax;\n      finalDesiredHeight *= scaleMax;\n    }\n\n    var refXProp = new Property(document, 'refX', refX);\n    var refYProp = new Property(document, 'refY', refY);\n    var hasRefs = refXProp.hasValue() && refYProp.hasValue();\n\n    if (hasRefs) {\n      ctx.translate(-scaleMin * refXProp.getPixels('x'), -scaleMin * refYProp.getPixels('y'));\n    }\n\n    if (clip) {\n      var scaledClipX = scaleMin * clipX;\n      var scaledClipY = scaleMin * clipY;\n      ctx.beginPath();\n      ctx.moveTo(scaledClipX, scaledClipY);\n      ctx.lineTo(width, scaledClipY);\n      ctx.lineTo(width, height);\n      ctx.lineTo(scaledClipX, height);\n      ctx.closePath();\n      ctx.clip();\n    }\n\n    if (!hasRefs) {\n      var isMeetMinY = meetOrSlice === 'meet' && scaleMin === scaleY;\n      var isSliceMaxY = meetOrSlice === 'slice' && scaleMax === scaleY;\n      var isMeetMinX = meetOrSlice === 'meet' && scaleMin === scaleX;\n      var isSliceMaxX = meetOrSlice === 'slice' && scaleMax === scaleX;\n\n      if (align.startsWith('xMid') && (isMeetMinY || isSliceMaxY)) {\n        ctx.translate(width / 2.0 - finalDesiredWidth / 2.0, 0);\n      }\n\n      if (align.endsWith('YMid') && (isMeetMinX || isSliceMaxX)) {\n        ctx.translate(0, height / 2.0 - finalDesiredHeight / 2.0);\n      }\n\n      if (align.startsWith('xMax') && (isMeetMinY || isSliceMaxY)) {\n        ctx.translate(width - finalDesiredWidth, 0);\n      }\n\n      if (align.endsWith('YMax') && (isMeetMinX || isSliceMaxX)) {\n        ctx.translate(0, height - finalDesiredHeight);\n      }\n    } // scale\n\n\n    switch (true) {\n      case align === 'none':\n        ctx.scale(scaleX, scaleY);\n        break;\n\n      case meetOrSlice === 'meet':\n        ctx.scale(scaleMin, scaleMin);\n        break;\n\n      case meetOrSlice === 'slice':\n        ctx.scale(scaleMax, scaleMax);\n        break;\n    } // translate\n\n\n    ctx.translate(-minX, -minY);\n  }\n\n  start(element) {\n    var {\n      enableRedraw = false,\n      ignoreMouse = false,\n      ignoreAnimation = false,\n      ignoreDimensions = false,\n      ignoreClear = false,\n      forceRedraw,\n      scaleWidth,\n      scaleHeight,\n      offsetX,\n      offsetY\n    } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var {\n      FRAMERATE,\n      mouse\n    } = this;\n    var frameDuration = 1000 / FRAMERATE;\n    this.frameDuration = frameDuration;\n    this.readyPromise = new Promise(resolve => {\n      this.resolveReady = resolve;\n    });\n\n    if (this.isReady()) {\n      this.render(element, ignoreDimensions, ignoreClear, scaleWidth, scaleHeight, offsetX, offsetY);\n    }\n\n    if (!enableRedraw) {\n      return;\n    }\n\n    var now = Date.now();\n    var then = now;\n    var delta = 0;\n\n    var tick = () => {\n      now = Date.now();\n      delta = now - then;\n\n      if (delta >= frameDuration) {\n        then = now - delta % frameDuration;\n\n        if (this.shouldUpdate(ignoreAnimation, forceRedraw)) {\n          this.render(element, ignoreDimensions, ignoreClear, scaleWidth, scaleHeight, offsetX, offsetY);\n          mouse.runEvents();\n        }\n      }\n\n      this.intervalId = raf__WEBPACK_IMPORTED_MODULE_11__(tick);\n    };\n\n    if (!ignoreMouse) {\n      mouse.start();\n    }\n\n    this.intervalId = raf__WEBPACK_IMPORTED_MODULE_11__(tick);\n  }\n\n  stop() {\n    if (this.intervalId) {\n      raf__WEBPACK_IMPORTED_MODULE_11__.cancel(this.intervalId);\n      this.intervalId = null;\n    }\n\n    this.mouse.stop();\n  }\n\n  shouldUpdate(ignoreAnimation, forceRedraw) {\n    // need update from animations?\n    if (!ignoreAnimation) {\n      var {\n        frameDuration\n      } = this;\n      var shouldUpdate = this.animations.reduce((shouldUpdate, animation) => animation.update(frameDuration) || shouldUpdate, false);\n\n      if (shouldUpdate) {\n        return true;\n      }\n    } // need update from redraw?\n\n\n    if (typeof forceRedraw === 'function' && forceRedraw()) {\n      return true;\n    }\n\n    if (!this.isReadyLock && this.isReady()) {\n      return true;\n    } // need update from mouse events?\n\n\n    if (this.mouse.hasEvents()) {\n      return true;\n    }\n\n    return false;\n  }\n\n  render(element, ignoreDimensions, ignoreClear, scaleWidth, scaleHeight, offsetX, offsetY) {\n    var {\n      CLIENT_WIDTH,\n      CLIENT_HEIGHT,\n      viewPort,\n      ctx,\n      isFirstRender\n    } = this;\n    var canvas = ctx.canvas;\n    viewPort.clear();\n\n    if (canvas.width && canvas.height) {\n      viewPort.setCurrent(canvas.width, canvas.height);\n    } else {\n      viewPort.setCurrent(CLIENT_WIDTH, CLIENT_HEIGHT);\n    }\n\n    var widthStyle = element.getStyle('width');\n    var heightStyle = element.getStyle('height');\n\n    if (!ignoreDimensions && (isFirstRender || typeof scaleWidth !== 'number' && typeof scaleHeight !== 'number')) {\n      // set canvas size\n      if (widthStyle.hasValue()) {\n        canvas.width = widthStyle.getPixels('x');\n\n        if (canvas.style) {\n          canvas.style.width = \"\".concat(canvas.width, \"px\");\n        }\n      }\n\n      if (heightStyle.hasValue()) {\n        canvas.height = heightStyle.getPixels('y');\n\n        if (canvas.style) {\n          canvas.style.height = \"\".concat(canvas.height, \"px\");\n        }\n      }\n    }\n\n    var cWidth = canvas.clientWidth || canvas.width;\n    var cHeight = canvas.clientHeight || canvas.height;\n\n    if (ignoreDimensions && widthStyle.hasValue() && heightStyle.hasValue()) {\n      cWidth = widthStyle.getPixels('x');\n      cHeight = heightStyle.getPixels('y');\n    }\n\n    viewPort.setCurrent(cWidth, cHeight);\n\n    if (typeof offsetX === 'number') {\n      element.getAttribute('x', true).setValue(offsetX);\n    }\n\n    if (typeof offsetY === 'number') {\n      element.getAttribute('y', true).setValue(offsetY);\n    }\n\n    if (typeof scaleWidth === 'number' || typeof scaleHeight === 'number') {\n      var viewBox = toNumbers(element.getAttribute('viewBox').getString());\n      var xRatio = 0;\n      var yRatio = 0;\n\n      if (typeof scaleWidth === 'number') {\n        var _widthStyle = element.getStyle('width');\n\n        if (_widthStyle.hasValue()) {\n          xRatio = _widthStyle.getPixels('x') / scaleWidth;\n        } else if (!isNaN(viewBox[2])) {\n          xRatio = viewBox[2] / scaleWidth;\n        }\n      }\n\n      if (typeof scaleHeight === 'number') {\n        var _heightStyle = element.getStyle('height');\n\n        if (_heightStyle.hasValue()) {\n          yRatio = _heightStyle.getPixels('y') / scaleHeight;\n        } else if (!isNaN(viewBox[3])) {\n          yRatio = viewBox[3] / scaleHeight;\n        }\n      }\n\n      if (!xRatio) {\n        xRatio = yRatio;\n      }\n\n      if (!yRatio) {\n        yRatio = xRatio;\n      }\n\n      element.getAttribute('width', true).setValue(scaleWidth);\n      element.getAttribute('height', true).setValue(scaleHeight);\n      var transformStyle = element.getStyle('transform', true, true);\n      transformStyle.setValue(\"\".concat(transformStyle.getString(), \" scale(\").concat(1.0 / xRatio, \", \").concat(1.0 / yRatio, \")\"));\n    } // clear and render\n\n\n    if (!ignoreClear) {\n      ctx.clearRect(0, 0, cWidth, cHeight);\n    }\n\n    element.render(ctx);\n\n    if (isFirstRender) {\n      this.isFirstRender = false;\n    }\n  }\n\n}\nScreen.defaultWindow = defaultWindow;\nScreen.defaultFetch = defaultFetch$1;\n\nvar {\n  defaultFetch\n} = Screen;\nvar DefaultDOMParser = typeof DOMParser !== 'undefined' ? DOMParser : null;\nclass Parser {\n  constructor() {\n    var {\n      fetch = defaultFetch,\n      DOMParser = DefaultDOMParser\n    } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    this.fetch = fetch;\n    this.DOMParser = DOMParser;\n  }\n\n  parse(resource) {\n    var _this = this;\n\n    return _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__(function* () {\n      if (resource.startsWith('<')) {\n        return _this.parseFromString(resource);\n      }\n\n      return _this.load(resource);\n    })();\n  }\n\n  parseFromString(xml) {\n    var parser = new this.DOMParser();\n\n    try {\n      return this.checkDocument(parser.parseFromString(xml, 'image/svg+xml'));\n    } catch (err) {\n      return this.checkDocument(parser.parseFromString(xml, 'text/xml'));\n    }\n  }\n\n  checkDocument(document) {\n    var parserError = document.getElementsByTagName('parsererror')[0];\n\n    if (parserError) {\n      throw new Error(parserError.textContent);\n    }\n\n    return document;\n  }\n\n  load(url) {\n    var _this2 = this;\n\n    return _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__(function* () {\n      var response = yield _this2.fetch(url);\n      var xml = yield response.text();\n      return _this2.parseFromString(xml);\n    })();\n  }\n\n}\n\nclass Translate {\n  constructor(_, point) {\n    this.type = 'translate';\n    this.point = null;\n    this.point = Point.parse(point);\n  }\n\n  apply(ctx) {\n    var {\n      x,\n      y\n    } = this.point;\n    ctx.translate(x || 0.0, y || 0.0);\n  }\n\n  unapply(ctx) {\n    var {\n      x,\n      y\n    } = this.point;\n    ctx.translate(-1.0 * x || 0.0, -1.0 * y || 0.0);\n  }\n\n  applyToPoint(point) {\n    var {\n      x,\n      y\n    } = this.point;\n    point.applyTransform([1, 0, 0, 1, x || 0.0, y || 0.0]);\n  }\n\n}\n\nclass Rotate {\n  constructor(document, rotate, transformOrigin) {\n    this.type = 'rotate';\n    this.angle = null;\n    this.originX = null;\n    this.originY = null;\n    this.cx = 0;\n    this.cy = 0;\n    var numbers = toNumbers(rotate);\n    this.angle = new Property(document, 'angle', numbers[0]);\n    this.originX = transformOrigin[0];\n    this.originY = transformOrigin[1];\n    this.cx = numbers[1] || 0;\n    this.cy = numbers[2] || 0;\n  }\n\n  apply(ctx) {\n    var {\n      cx,\n      cy,\n      originX,\n      originY,\n      angle\n    } = this;\n    var tx = cx + originX.getPixels('x');\n    var ty = cy + originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.rotate(angle.getRadians());\n    ctx.translate(-tx, -ty);\n  }\n\n  unapply(ctx) {\n    var {\n      cx,\n      cy,\n      originX,\n      originY,\n      angle\n    } = this;\n    var tx = cx + originX.getPixels('x');\n    var ty = cy + originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.rotate(-1.0 * angle.getRadians());\n    ctx.translate(-tx, -ty);\n  }\n\n  applyToPoint(point) {\n    var {\n      cx,\n      cy,\n      angle\n    } = this;\n    var rad = angle.getRadians();\n    point.applyTransform([1, 0, 0, 1, cx || 0.0, cy || 0.0 // this.p.y\n    ]);\n    point.applyTransform([Math.cos(rad), Math.sin(rad), -Math.sin(rad), Math.cos(rad), 0, 0]);\n    point.applyTransform([1, 0, 0, 1, -cx || 0.0, -cy || 0.0 // -this.p.y\n    ]);\n  }\n\n}\n\nclass Scale {\n  constructor(_, scale, transformOrigin) {\n    this.type = 'scale';\n    this.scale = null;\n    this.originX = null;\n    this.originY = null;\n    var scaleSize = Point.parseScale(scale); // Workaround for node-canvas\n\n    if (scaleSize.x === 0 || scaleSize.y === 0) {\n      scaleSize.x = PSEUDO_ZERO;\n      scaleSize.y = PSEUDO_ZERO;\n    }\n\n    this.scale = scaleSize;\n    this.originX = transformOrigin[0];\n    this.originY = transformOrigin[1];\n  }\n\n  apply(ctx) {\n    var {\n      scale: {\n        x,\n        y\n      },\n      originX,\n      originY\n    } = this;\n    var tx = originX.getPixels('x');\n    var ty = originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.scale(x, y || x);\n    ctx.translate(-tx, -ty);\n  }\n\n  unapply(ctx) {\n    var {\n      scale: {\n        x,\n        y\n      },\n      originX,\n      originY\n    } = this;\n    var tx = originX.getPixels('x');\n    var ty = originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.scale(1.0 / x, 1.0 / y || x);\n    ctx.translate(-tx, -ty);\n  }\n\n  applyToPoint(point) {\n    var {\n      x,\n      y\n    } = this.scale;\n    point.applyTransform([x || 0.0, 0, 0, y || 0.0, 0, 0]);\n  }\n\n}\n\nclass Matrix {\n  constructor(_, matrix, transformOrigin) {\n    this.type = 'matrix';\n    this.matrix = [];\n    this.originX = null;\n    this.originY = null;\n    this.matrix = toNumbers(matrix);\n    this.originX = transformOrigin[0];\n    this.originY = transformOrigin[1];\n  }\n\n  apply(ctx) {\n    var {\n      originX,\n      originY,\n      matrix\n    } = this;\n    var tx = originX.getPixels('x');\n    var ty = originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.transform(matrix[0], matrix[1], matrix[2], matrix[3], matrix[4], matrix[5]);\n    ctx.translate(-tx, -ty);\n  }\n\n  unapply(ctx) {\n    var {\n      originX,\n      originY,\n      matrix\n    } = this;\n    var a = matrix[0];\n    var b = matrix[2];\n    var c = matrix[4];\n    var d = matrix[1];\n    var e = matrix[3];\n    var f = matrix[5];\n    var g = 0.0;\n    var h = 0.0;\n    var i = 1.0;\n    var det = 1 / (a * (e * i - f * h) - b * (d * i - f * g) + c * (d * h - e * g));\n    var tx = originX.getPixels('x');\n    var ty = originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.transform(det * (e * i - f * h), det * (f * g - d * i), det * (c * h - b * i), det * (a * i - c * g), det * (b * f - c * e), det * (c * d - a * f));\n    ctx.translate(-tx, -ty);\n  }\n\n  applyToPoint(point) {\n    point.applyTransform(this.matrix);\n  }\n\n}\n\nclass Skew extends Matrix {\n  constructor(document, skew, transformOrigin) {\n    super(document, skew, transformOrigin);\n    this.type = 'skew';\n    this.angle = null;\n    this.angle = new Property(document, 'angle', skew);\n  }\n\n}\n\nclass SkewX extends Skew {\n  constructor(document, skew, transformOrigin) {\n    super(document, skew, transformOrigin);\n    this.type = 'skewX';\n    this.matrix = [1, 0, Math.tan(this.angle.getRadians()), 1, 0, 0];\n  }\n\n}\n\nclass SkewY extends Skew {\n  constructor(document, skew, transformOrigin) {\n    super(document, skew, transformOrigin);\n    this.type = 'skewY';\n    this.matrix = [1, Math.tan(this.angle.getRadians()), 0, 1, 0, 0];\n  }\n\n}\n\nfunction parseTransforms(transform) {\n  return compressSpaces(transform).trim().replace(/\\)([a-zA-Z])/g, ') $1').replace(/\\)(\\s?,\\s?)/g, ') ').split(/\\s(?=[a-z])/);\n}\n\nfunction parseTransform(transform) {\n  var [type, value] = transform.split('(');\n  return [type.trim(), value.trim().replace(')', '')];\n}\n\nclass Transform {\n  constructor(document, transform, transformOrigin) {\n    this.document = document;\n    this.transforms = [];\n    var data = parseTransforms(transform);\n    data.forEach(transform => {\n      if (transform === 'none') {\n        return;\n      }\n\n      var [type, value] = parseTransform(transform);\n      var TransformType = Transform.transformTypes[type];\n\n      if (typeof TransformType !== 'undefined') {\n        this.transforms.push(new TransformType(this.document, value, transformOrigin));\n      }\n    });\n  }\n\n  static fromElement(document, element) {\n    var transformStyle = element.getStyle('transform', false, true);\n    var [transformOriginXProperty, transformOriginYProperty = transformOriginXProperty] = element.getStyle('transform-origin', false, true).split();\n    var transformOrigin = [transformOriginXProperty, transformOriginYProperty];\n\n    if (transformStyle.hasValue()) {\n      return new Transform(document, transformStyle.getString(), transformOrigin);\n    }\n\n    return null;\n  }\n\n  apply(ctx) {\n    var {\n      transforms\n    } = this;\n    var len = transforms.length;\n\n    for (var i = 0; i < len; i++) {\n      transforms[i].apply(ctx);\n    }\n  }\n\n  unapply(ctx) {\n    var {\n      transforms\n    } = this;\n    var len = transforms.length;\n\n    for (var i = len - 1; i >= 0; i--) {\n      transforms[i].unapply(ctx);\n    }\n  } // TODO: applyToPoint unused ... remove?\n\n\n  applyToPoint(point) {\n    var {\n      transforms\n    } = this;\n    var len = transforms.length;\n\n    for (var i = 0; i < len; i++) {\n      transforms[i].applyToPoint(point);\n    }\n  }\n\n}\nTransform.transformTypes = {\n  translate: Translate,\n  rotate: Rotate,\n  scale: Scale,\n  matrix: Matrix,\n  skewX: SkewX,\n  skewY: SkewY\n};\n\nclass Element {\n  constructor(document, node) {\n    var captureTextNodes = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    this.document = document;\n    this.node = node;\n    this.captureTextNodes = captureTextNodes;\n    this.attributes = Object.create(null);\n    this.styles = Object.create(null);\n    this.stylesSpecificity = Object.create(null);\n    this.animationFrozen = false;\n    this.animationFrozenValue = '';\n    this.parent = null;\n    this.children = [];\n\n    if (!node || node.nodeType !== 1) {\n      // ELEMENT_NODE\n      return;\n    } // add attributes\n\n\n    Array.from(node.attributes).forEach(attribute => {\n      var nodeName = normalizeAttributeName(attribute.nodeName);\n      this.attributes[nodeName] = new Property(document, nodeName, attribute.value);\n    });\n    this.addStylesFromStyleDefinition(); // add inline styles\n\n    if (this.getAttribute('style').hasValue()) {\n      var styles = this.getAttribute('style').getString().split(';').map(_ => _.trim());\n      styles.forEach(style => {\n        if (!style) {\n          return;\n        }\n\n        var [name, value] = style.split(':').map(_ => _.trim());\n        this.styles[name] = new Property(document, name, value);\n      });\n    }\n\n    var {\n      definitions\n    } = document;\n    var id = this.getAttribute('id'); // add id\n\n    if (id.hasValue()) {\n      if (!definitions[id.getString()]) {\n        definitions[id.getString()] = this;\n      }\n    }\n\n    Array.from(node.childNodes).forEach(childNode => {\n      if (childNode.nodeType === 1) {\n        this.addChild(childNode); // ELEMENT_NODE\n      } else if (captureTextNodes && (childNode.nodeType === 3 || childNode.nodeType === 4)) {\n        var textNode = document.createTextNode(childNode);\n\n        if (textNode.getText().length > 0) {\n          this.addChild(textNode); // TEXT_NODE\n        }\n      }\n    });\n  }\n\n  getAttribute(name) {\n    var createIfNotExists = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var attr = this.attributes[name];\n\n    if (!attr && createIfNotExists) {\n      var _attr = new Property(this.document, name, '');\n\n      this.attributes[name] = _attr;\n      return _attr;\n    }\n\n    return attr || Property.empty(this.document);\n  }\n\n  getHrefAttribute() {\n    for (var key in this.attributes) {\n      if (key === 'href' || key.endsWith(':href')) {\n        return this.attributes[key];\n      }\n    }\n\n    return Property.empty(this.document);\n  }\n\n  getStyle(name) {\n    var createIfNotExists = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var skipAncestors = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    var style = this.styles[name];\n\n    if (style) {\n      return style;\n    }\n\n    var attr = this.getAttribute(name);\n\n    if (attr !== null && attr !== void 0 && attr.hasValue()) {\n      this.styles[name] = attr; // move up to me to cache\n\n      return attr;\n    }\n\n    if (!skipAncestors) {\n      var {\n        parent\n      } = this;\n\n      if (parent) {\n        var parentStyle = parent.getStyle(name);\n\n        if (parentStyle !== null && parentStyle !== void 0 && parentStyle.hasValue()) {\n          return parentStyle;\n        }\n      }\n    }\n\n    if (createIfNotExists) {\n      var _style = new Property(this.document, name, '');\n\n      this.styles[name] = _style;\n      return _style;\n    }\n\n    return style || Property.empty(this.document);\n  }\n\n  render(ctx) {\n    // don't render display=none\n    // don't render visibility=hidden\n    if (this.getStyle('display').getString() === 'none' || this.getStyle('visibility').getString() === 'hidden') {\n      return;\n    }\n\n    ctx.save();\n\n    if (this.getStyle('mask').hasValue()) {\n      // mask\n      var mask = this.getStyle('mask').getDefinition();\n\n      if (mask) {\n        this.applyEffects(ctx);\n        mask.apply(ctx, this);\n      }\n    } else if (this.getStyle('filter').getValue('none') !== 'none') {\n      // filter\n      var filter = this.getStyle('filter').getDefinition();\n\n      if (filter) {\n        this.applyEffects(ctx);\n        filter.apply(ctx, this);\n      }\n    } else {\n      this.setContext(ctx);\n      this.renderChildren(ctx);\n      this.clearContext(ctx);\n    }\n\n    ctx.restore();\n  }\n\n  setContext(_) {// NO RENDER\n  }\n\n  applyEffects(ctx) {\n    // transform\n    var transform = Transform.fromElement(this.document, this);\n\n    if (transform) {\n      transform.apply(ctx);\n    } // clip\n\n\n    var clipPathStyleProp = this.getStyle('clip-path', false, true);\n\n    if (clipPathStyleProp.hasValue()) {\n      var clip = clipPathStyleProp.getDefinition();\n\n      if (clip) {\n        clip.apply(ctx);\n      }\n    }\n  }\n\n  clearContext(_) {// NO RENDER\n  }\n\n  renderChildren(ctx) {\n    this.children.forEach(child => {\n      child.render(ctx);\n    });\n  }\n\n  addChild(childNode) {\n    var child = childNode instanceof Element ? childNode : this.document.createElement(childNode);\n    child.parent = this;\n\n    if (!Element.ignoreChildTypes.includes(child.type)) {\n      this.children.push(child);\n    }\n  }\n\n  matchesSelector(selector) {\n    var _node$getAttribute;\n\n    var {\n      node\n    } = this;\n\n    if (typeof node.matches === 'function') {\n      return node.matches(selector);\n    }\n\n    var styleClasses = (_node$getAttribute = node.getAttribute) === null || _node$getAttribute === void 0 ? void 0 : _node$getAttribute.call(node, 'class');\n\n    if (!styleClasses || styleClasses === '') {\n      return false;\n    }\n\n    return styleClasses.split(' ').some(styleClass => \".\".concat(styleClass) === selector);\n  }\n\n  addStylesFromStyleDefinition() {\n    var {\n      styles,\n      stylesSpecificity\n    } = this.document;\n\n    for (var selector in styles) {\n      if (!selector.startsWith('@') && this.matchesSelector(selector)) {\n        var style = styles[selector];\n        var specificity = stylesSpecificity[selector];\n\n        if (style) {\n          for (var name in style) {\n            var existingSpecificity = this.stylesSpecificity[name];\n\n            if (typeof existingSpecificity === 'undefined') {\n              existingSpecificity = '000';\n            }\n\n            if (specificity >= existingSpecificity) {\n              this.styles[name] = style[name];\n              this.stylesSpecificity[name] = specificity;\n            }\n          }\n        }\n      }\n    }\n  }\n\n  removeStyles(element, ignoreStyles) {\n    var toRestore = ignoreStyles.reduce((toRestore, name) => {\n      var styleProp = element.getStyle(name);\n\n      if (!styleProp.hasValue()) {\n        return toRestore;\n      }\n\n      var value = styleProp.getString();\n      styleProp.setValue('');\n      return [...toRestore, [name, value]];\n    }, []);\n    return toRestore;\n  }\n\n  restoreStyles(element, styles) {\n    styles.forEach(_ref => {\n      var [name, value] = _ref;\n      element.getStyle(name, true).setValue(value);\n    });\n  }\n\n  isFirstChild() {\n    var _this$parent;\n\n    return ((_this$parent = this.parent) === null || _this$parent === void 0 ? void 0 : _this$parent.children.indexOf(this)) === 0;\n  }\n\n}\nElement.ignoreChildTypes = ['title'];\n\nclass UnknownElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n  }\n\n}\n\nfunction wrapFontFamily(fontFamily) {\n  var trimmed = fontFamily.trim();\n  return /^('|\")/.test(trimmed) ? trimmed : \"\\\"\".concat(trimmed, \"\\\"\");\n}\n\nfunction prepareFontFamily(fontFamily) {\n  return typeof process === 'undefined' ? fontFamily : fontFamily.trim().split(',').map(wrapFontFamily).join(',');\n}\n/**\r\n * https://developer.mozilla.org/en-US/docs/Web/CSS/font-style\r\n * @param fontStyle\r\n * @returns CSS font style.\r\n */\n\n\nfunction prepareFontStyle(fontStyle) {\n  if (!fontStyle) {\n    return '';\n  }\n\n  var targetFontStyle = fontStyle.trim().toLowerCase();\n\n  switch (targetFontStyle) {\n    case 'normal':\n    case 'italic':\n    case 'oblique':\n    case 'inherit':\n    case 'initial':\n    case 'unset':\n      return targetFontStyle;\n\n    default:\n      if (/^oblique\\s+(-|)\\d+deg$/.test(targetFontStyle)) {\n        return targetFontStyle;\n      }\n\n      return '';\n  }\n}\n/**\r\n * https://developer.mozilla.org/en-US/docs/Web/CSS/font-weight\r\n * @param fontWeight\r\n * @returns CSS font weight.\r\n */\n\n\nfunction prepareFontWeight(fontWeight) {\n  if (!fontWeight) {\n    return '';\n  }\n\n  var targetFontWeight = fontWeight.trim().toLowerCase();\n\n  switch (targetFontWeight) {\n    case 'normal':\n    case 'bold':\n    case 'lighter':\n    case 'bolder':\n    case 'inherit':\n    case 'initial':\n    case 'unset':\n      return targetFontWeight;\n\n    default:\n      if (/^[\\d.]+$/.test(targetFontWeight)) {\n        return targetFontWeight;\n      }\n\n      return '';\n  }\n}\n\nclass Font {\n  constructor(fontStyle, fontVariant, fontWeight, fontSize, fontFamily, inherit) {\n    var inheritFont = inherit ? typeof inherit === 'string' ? Font.parse(inherit) : inherit : {};\n    this.fontFamily = fontFamily || inheritFont.fontFamily;\n    this.fontSize = fontSize || inheritFont.fontSize;\n    this.fontStyle = fontStyle || inheritFont.fontStyle;\n    this.fontWeight = fontWeight || inheritFont.fontWeight;\n    this.fontVariant = fontVariant || inheritFont.fontVariant;\n  }\n\n  static parse() {\n    var font = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n    var inherit = arguments.length > 1 ? arguments[1] : undefined;\n    var fontStyle = '';\n    var fontVariant = '';\n    var fontWeight = '';\n    var fontSize = '';\n    var fontFamily = '';\n    var parts = compressSpaces(font).trim().split(' ');\n    var set = {\n      fontSize: false,\n      fontStyle: false,\n      fontWeight: false,\n      fontVariant: false\n    };\n    parts.forEach(part => {\n      switch (true) {\n        case !set.fontStyle && Font.styles.includes(part):\n          if (part !== 'inherit') {\n            fontStyle = part;\n          }\n\n          set.fontStyle = true;\n          break;\n\n        case !set.fontVariant && Font.variants.includes(part):\n          if (part !== 'inherit') {\n            fontVariant = part;\n          }\n\n          set.fontStyle = true;\n          set.fontVariant = true;\n          break;\n\n        case !set.fontWeight && Font.weights.includes(part):\n          if (part !== 'inherit') {\n            fontWeight = part;\n          }\n\n          set.fontStyle = true;\n          set.fontVariant = true;\n          set.fontWeight = true;\n          break;\n\n        case !set.fontSize:\n          if (part !== 'inherit') {\n            [fontSize] = part.split('/');\n          }\n\n          set.fontStyle = true;\n          set.fontVariant = true;\n          set.fontWeight = true;\n          set.fontSize = true;\n          break;\n\n        default:\n          if (part !== 'inherit') {\n            fontFamily += part;\n          }\n\n      }\n    });\n    return new Font(fontStyle, fontVariant, fontWeight, fontSize, fontFamily, inherit);\n  }\n\n  toString() {\n    return [prepareFontStyle(this.fontStyle), this.fontVariant, prepareFontWeight(this.fontWeight), this.fontSize, // Wrap fontFamily only on nodejs and only for canvas.ctx\n    prepareFontFamily(this.fontFamily)].join(' ').trim();\n  }\n\n}\nFont.styles = 'normal|italic|oblique|inherit';\nFont.variants = 'normal|small-caps|inherit';\nFont.weights = 'normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900|inherit';\n\nclass BoundingBox {\n  constructor() {\n    var x1 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : Number.NaN;\n    var y1 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : Number.NaN;\n    var x2 = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : Number.NaN;\n    var y2 = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : Number.NaN;\n    this.x1 = x1;\n    this.y1 = y1;\n    this.x2 = x2;\n    this.y2 = y2;\n    this.addPoint(x1, y1);\n    this.addPoint(x2, y2);\n  }\n\n  get x() {\n    return this.x1;\n  }\n\n  get y() {\n    return this.y1;\n  }\n\n  get width() {\n    return this.x2 - this.x1;\n  }\n\n  get height() {\n    return this.y2 - this.y1;\n  }\n\n  addPoint(x, y) {\n    if (typeof x !== 'undefined') {\n      if (isNaN(this.x1) || isNaN(this.x2)) {\n        this.x1 = x;\n        this.x2 = x;\n      }\n\n      if (x < this.x1) {\n        this.x1 = x;\n      }\n\n      if (x > this.x2) {\n        this.x2 = x;\n      }\n    }\n\n    if (typeof y !== 'undefined') {\n      if (isNaN(this.y1) || isNaN(this.y2)) {\n        this.y1 = y;\n        this.y2 = y;\n      }\n\n      if (y < this.y1) {\n        this.y1 = y;\n      }\n\n      if (y > this.y2) {\n        this.y2 = y;\n      }\n    }\n  }\n\n  addX(x) {\n    this.addPoint(x, null);\n  }\n\n  addY(y) {\n    this.addPoint(null, y);\n  }\n\n  addBoundingBox(boundingBox) {\n    if (!boundingBox) {\n      return;\n    }\n\n    var {\n      x1,\n      y1,\n      x2,\n      y2\n    } = boundingBox;\n    this.addPoint(x1, y1);\n    this.addPoint(x2, y2);\n  }\n\n  sumCubic(t, p0, p1, p2, p3) {\n    return Math.pow(1 - t, 3) * p0 + 3 * Math.pow(1 - t, 2) * t * p1 + 3 * (1 - t) * Math.pow(t, 2) * p2 + Math.pow(t, 3) * p3;\n  }\n\n  bezierCurveAdd(forX, p0, p1, p2, p3) {\n    var b = 6 * p0 - 12 * p1 + 6 * p2;\n    var a = -3 * p0 + 9 * p1 - 9 * p2 + 3 * p3;\n    var c = 3 * p1 - 3 * p0;\n\n    if (a === 0) {\n      if (b === 0) {\n        return;\n      }\n\n      var t = -c / b;\n\n      if (0 < t && t < 1) {\n        if (forX) {\n          this.addX(this.sumCubic(t, p0, p1, p2, p3));\n        } else {\n          this.addY(this.sumCubic(t, p0, p1, p2, p3));\n        }\n      }\n\n      return;\n    }\n\n    var b2ac = Math.pow(b, 2) - 4 * c * a;\n\n    if (b2ac < 0) {\n      return;\n    }\n\n    var t1 = (-b + Math.sqrt(b2ac)) / (2 * a);\n\n    if (0 < t1 && t1 < 1) {\n      if (forX) {\n        this.addX(this.sumCubic(t1, p0, p1, p2, p3));\n      } else {\n        this.addY(this.sumCubic(t1, p0, p1, p2, p3));\n      }\n    }\n\n    var t2 = (-b - Math.sqrt(b2ac)) / (2 * a);\n\n    if (0 < t2 && t2 < 1) {\n      if (forX) {\n        this.addX(this.sumCubic(t2, p0, p1, p2, p3));\n      } else {\n        this.addY(this.sumCubic(t2, p0, p1, p2, p3));\n      }\n    }\n  } // from http://blog.hackers-cafe.net/2009/06/how-to-calculate-bezier-curves-bounding.html\n\n\n  addBezierCurve(p0x, p0y, p1x, p1y, p2x, p2y, p3x, p3y) {\n    this.addPoint(p0x, p0y);\n    this.addPoint(p3x, p3y);\n    this.bezierCurveAdd(true, p0x, p1x, p2x, p3x);\n    this.bezierCurveAdd(false, p0y, p1y, p2y, p3y);\n  }\n\n  addQuadraticCurve(p0x, p0y, p1x, p1y, p2x, p2y) {\n    var cp1x = p0x + 2 / 3 * (p1x - p0x); // CP1 = QP0 + 2/3 *(QP1-QP0)\n\n    var cp1y = p0y + 2 / 3 * (p1y - p0y); // CP1 = QP0 + 2/3 *(QP1-QP0)\n\n    var cp2x = cp1x + 1 / 3 * (p2x - p0x); // CP2 = CP1 + 1/3 *(QP2-QP0)\n\n    var cp2y = cp1y + 1 / 3 * (p2y - p0y); // CP2 = CP1 + 1/3 *(QP2-QP0)\n\n    this.addBezierCurve(p0x, p0y, cp1x, cp2x, cp1y, cp2y, p2x, p2y);\n  }\n\n  isPointInBox(x, y) {\n    var {\n      x1,\n      y1,\n      x2,\n      y2\n    } = this;\n    return x1 <= x && x <= x2 && y1 <= y && y <= y2;\n  }\n\n}\n\nclass PathParser extends svg_pathdata__WEBPACK_IMPORTED_MODULE_17__.SVGPathData {\n  constructor(path) {\n    super(path // Fix spaces after signs.\n    .replace(/([+\\-.])\\s+/gm, '$1') // Remove invalid part.\n    .replace(/[^MmZzLlHhVvCcSsQqTtAae\\d\\s.,+-].*/g, ''));\n    this.control = null;\n    this.start = null;\n    this.current = null;\n    this.command = null;\n    this.commands = this.commands;\n    this.i = -1;\n    this.previousCommand = null;\n    this.points = [];\n    this.angles = [];\n  }\n\n  reset() {\n    this.i = -1;\n    this.command = null;\n    this.previousCommand = null;\n    this.start = new Point(0, 0);\n    this.control = new Point(0, 0);\n    this.current = new Point(0, 0);\n    this.points = [];\n    this.angles = [];\n  }\n\n  isEnd() {\n    var {\n      i,\n      commands\n    } = this;\n    return i >= commands.length - 1;\n  }\n\n  next() {\n    var command = this.commands[++this.i];\n    this.previousCommand = this.command;\n    this.command = command;\n    return command;\n  }\n\n  getPoint() {\n    var xProp = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'x';\n    var yProp = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'y';\n    var point = new Point(this.command[xProp], this.command[yProp]);\n    return this.makeAbsolute(point);\n  }\n\n  getAsControlPoint(xProp, yProp) {\n    var point = this.getPoint(xProp, yProp);\n    this.control = point;\n    return point;\n  }\n\n  getAsCurrentPoint(xProp, yProp) {\n    var point = this.getPoint(xProp, yProp);\n    this.current = point;\n    return point;\n  }\n\n  getReflectedControlPoint() {\n    var previousCommand = this.previousCommand.type;\n\n    if (previousCommand !== svg_pathdata__WEBPACK_IMPORTED_MODULE_17__.SVGPathData.CURVE_TO && previousCommand !== svg_pathdata__WEBPACK_IMPORTED_MODULE_17__.SVGPathData.SMOOTH_CURVE_TO && previousCommand !== svg_pathdata__WEBPACK_IMPORTED_MODULE_17__.SVGPathData.QUAD_TO && previousCommand !== svg_pathdata__WEBPACK_IMPORTED_MODULE_17__.SVGPathData.SMOOTH_QUAD_TO) {\n      return this.current;\n    } // reflect point\n\n\n    var {\n      current: {\n        x: cx,\n        y: cy\n      },\n      control: {\n        x: ox,\n        y: oy\n      }\n    } = this;\n    var point = new Point(2 * cx - ox, 2 * cy - oy);\n    return point;\n  }\n\n  makeAbsolute(point) {\n    if (this.command.relative) {\n      var {\n        x,\n        y\n      } = this.current;\n      point.x += x;\n      point.y += y;\n    }\n\n    return point;\n  }\n\n  addMarker(point, from, priorTo) {\n    var {\n      points,\n      angles\n    } = this; // if the last angle isn't filled in because we didn't have this point yet ...\n\n    if (priorTo && angles.length > 0 && !angles[angles.length - 1]) {\n      angles[angles.length - 1] = points[points.length - 1].angleTo(priorTo);\n    }\n\n    this.addMarkerAngle(point, from ? from.angleTo(point) : null);\n  }\n\n  addMarkerAngle(point, angle) {\n    this.points.push(point);\n    this.angles.push(angle);\n  }\n\n  getMarkerPoints() {\n    return this.points;\n  }\n\n  getMarkerAngles() {\n    var {\n      angles\n    } = this;\n    var len = angles.length;\n\n    for (var i = 0; i < len; i++) {\n      if (!angles[i]) {\n        for (var j = i + 1; j < len; j++) {\n          if (angles[j]) {\n            angles[i] = angles[j];\n            break;\n          }\n        }\n      }\n    }\n\n    return angles;\n  }\n\n}\n\nclass RenderedElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.modifiedEmSizeStack = false;\n  }\n\n  calculateOpacity() {\n    var opacity = 1.0; // eslint-disable-next-line @typescript-eslint/no-this-alias, consistent-this\n\n    var element = this;\n\n    while (element) {\n      var opacityStyle = element.getStyle('opacity', false, true); // no ancestors on style call\n\n      if (opacityStyle.hasValue(true)) {\n        opacity *= opacityStyle.getNumber();\n      }\n\n      element = element.parent;\n    }\n\n    return opacity;\n  }\n\n  setContext(ctx) {\n    var fromMeasure = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n    if (!fromMeasure) {\n      // causes stack overflow when measuring text with gradients\n      // fill\n      var fillStyleProp = this.getStyle('fill');\n      var fillOpacityStyleProp = this.getStyle('fill-opacity');\n      var strokeStyleProp = this.getStyle('stroke');\n      var strokeOpacityProp = this.getStyle('stroke-opacity');\n\n      if (fillStyleProp.isUrlDefinition()) {\n        var fillStyle = fillStyleProp.getFillStyleDefinition(this, fillOpacityStyleProp);\n\n        if (fillStyle) {\n          ctx.fillStyle = fillStyle;\n        }\n      } else if (fillStyleProp.hasValue()) {\n        if (fillStyleProp.getString() === 'currentColor') {\n          fillStyleProp.setValue(this.getStyle('color').getColor());\n        }\n\n        var _fillStyle = fillStyleProp.getColor();\n\n        if (_fillStyle !== 'inherit') {\n          ctx.fillStyle = _fillStyle === 'none' ? 'rgba(0,0,0,0)' : _fillStyle;\n        }\n      }\n\n      if (fillOpacityStyleProp.hasValue()) {\n        var _fillStyle2 = new Property(this.document, 'fill', ctx.fillStyle).addOpacity(fillOpacityStyleProp).getColor();\n\n        ctx.fillStyle = _fillStyle2;\n      } // stroke\n\n\n      if (strokeStyleProp.isUrlDefinition()) {\n        var strokeStyle = strokeStyleProp.getFillStyleDefinition(this, strokeOpacityProp);\n\n        if (strokeStyle) {\n          ctx.strokeStyle = strokeStyle;\n        }\n      } else if (strokeStyleProp.hasValue()) {\n        if (strokeStyleProp.getString() === 'currentColor') {\n          strokeStyleProp.setValue(this.getStyle('color').getColor());\n        }\n\n        var _strokeStyle = strokeStyleProp.getString();\n\n        if (_strokeStyle !== 'inherit') {\n          ctx.strokeStyle = _strokeStyle === 'none' ? 'rgba(0,0,0,0)' : _strokeStyle;\n        }\n      }\n\n      if (strokeOpacityProp.hasValue()) {\n        var _strokeStyle2 = new Property(this.document, 'stroke', ctx.strokeStyle).addOpacity(strokeOpacityProp).getString();\n\n        ctx.strokeStyle = _strokeStyle2;\n      }\n\n      var strokeWidthStyleProp = this.getStyle('stroke-width');\n\n      if (strokeWidthStyleProp.hasValue()) {\n        var newLineWidth = strokeWidthStyleProp.getPixels();\n        ctx.lineWidth = !newLineWidth ? PSEUDO_ZERO // browsers don't respect 0 (or node-canvas? :-)\n        : newLineWidth;\n      }\n\n      var strokeLinecapStyleProp = this.getStyle('stroke-linecap');\n      var strokeLinejoinStyleProp = this.getStyle('stroke-linejoin');\n      var strokeMiterlimitProp = this.getStyle('stroke-miterlimit'); // NEED TEST\n      // const pointOrderStyleProp = this.getStyle('paint-order');\n\n      var strokeDasharrayStyleProp = this.getStyle('stroke-dasharray');\n      var strokeDashoffsetProp = this.getStyle('stroke-dashoffset');\n\n      if (strokeLinecapStyleProp.hasValue()) {\n        ctx.lineCap = strokeLinecapStyleProp.getString();\n      }\n\n      if (strokeLinejoinStyleProp.hasValue()) {\n        ctx.lineJoin = strokeLinejoinStyleProp.getString();\n      }\n\n      if (strokeMiterlimitProp.hasValue()) {\n        ctx.miterLimit = strokeMiterlimitProp.getNumber();\n      } // NEED TEST\n      // if (pointOrderStyleProp.hasValue()) {\n      // \t// ?\n      // \tctx.paintOrder = pointOrderStyleProp.getValue();\n      // }\n\n\n      if (strokeDasharrayStyleProp.hasValue() && strokeDasharrayStyleProp.getString() !== 'none') {\n        var gaps = toNumbers(strokeDasharrayStyleProp.getString());\n\n        if (typeof ctx.setLineDash !== 'undefined') {\n          ctx.setLineDash(gaps);\n        } else // @ts-expect-error Handle browser prefix.\n          if (typeof ctx.webkitLineDash !== 'undefined') {\n            // @ts-expect-error Handle browser prefix.\n            ctx.webkitLineDash = gaps;\n          } else // @ts-expect-error Handle browser prefix.\n            if (typeof ctx.mozDash !== 'undefined' && !(gaps.length === 1 && gaps[0] === 0)) {\n              // @ts-expect-error Handle browser prefix.\n              ctx.mozDash = gaps;\n            }\n\n        var offset = strokeDashoffsetProp.getPixels();\n\n        if (typeof ctx.lineDashOffset !== 'undefined') {\n          ctx.lineDashOffset = offset;\n        } else // @ts-expect-error Handle browser prefix.\n          if (typeof ctx.webkitLineDashOffset !== 'undefined') {\n            // @ts-expect-error Handle browser prefix.\n            ctx.webkitLineDashOffset = offset;\n          } else // @ts-expect-error Handle browser prefix.\n            if (typeof ctx.mozDashOffset !== 'undefined') {\n              // @ts-expect-error Handle browser prefix.\n              ctx.mozDashOffset = offset;\n            }\n      }\n    } // font\n\n\n    this.modifiedEmSizeStack = false;\n\n    if (typeof ctx.font !== 'undefined') {\n      var fontStyleProp = this.getStyle('font');\n      var fontStyleStyleProp = this.getStyle('font-style');\n      var fontVariantStyleProp = this.getStyle('font-variant');\n      var fontWeightStyleProp = this.getStyle('font-weight');\n      var fontSizeStyleProp = this.getStyle('font-size');\n      var fontFamilyStyleProp = this.getStyle('font-family');\n      var font = new Font(fontStyleStyleProp.getString(), fontVariantStyleProp.getString(), fontWeightStyleProp.getString(), fontSizeStyleProp.hasValue() ? \"\".concat(fontSizeStyleProp.getPixels(true), \"px\") : '', fontFamilyStyleProp.getString(), Font.parse(fontStyleProp.getString(), ctx.font));\n      fontStyleStyleProp.setValue(font.fontStyle);\n      fontVariantStyleProp.setValue(font.fontVariant);\n      fontWeightStyleProp.setValue(font.fontWeight);\n      fontSizeStyleProp.setValue(font.fontSize);\n      fontFamilyStyleProp.setValue(font.fontFamily);\n      ctx.font = font.toString();\n\n      if (fontSizeStyleProp.isPixels()) {\n        this.document.emSize = fontSizeStyleProp.getPixels();\n        this.modifiedEmSizeStack = true;\n      }\n    }\n\n    if (!fromMeasure) {\n      // effects\n      this.applyEffects(ctx); // opacity\n\n      ctx.globalAlpha = this.calculateOpacity();\n    }\n  }\n\n  clearContext(ctx) {\n    super.clearContext(ctx);\n\n    if (this.modifiedEmSizeStack) {\n      this.document.popEmSize();\n    }\n  }\n\n}\n\nclass PathElement extends RenderedElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'path';\n    this.pathParser = null;\n    this.pathParser = new PathParser(this.getAttribute('d').getString());\n  }\n\n  path(ctx) {\n    var {\n      pathParser\n    } = this;\n    var boundingBox = new BoundingBox();\n    pathParser.reset();\n\n    if (ctx) {\n      ctx.beginPath();\n    }\n\n    while (!pathParser.isEnd()) {\n      switch (pathParser.next().type) {\n        case PathParser.MOVE_TO:\n          this.pathM(ctx, boundingBox);\n          break;\n\n        case PathParser.LINE_TO:\n          this.pathL(ctx, boundingBox);\n          break;\n\n        case PathParser.HORIZ_LINE_TO:\n          this.pathH(ctx, boundingBox);\n          break;\n\n        case PathParser.VERT_LINE_TO:\n          this.pathV(ctx, boundingBox);\n          break;\n\n        case PathParser.CURVE_TO:\n          this.pathC(ctx, boundingBox);\n          break;\n\n        case PathParser.SMOOTH_CURVE_TO:\n          this.pathS(ctx, boundingBox);\n          break;\n\n        case PathParser.QUAD_TO:\n          this.pathQ(ctx, boundingBox);\n          break;\n\n        case PathParser.SMOOTH_QUAD_TO:\n          this.pathT(ctx, boundingBox);\n          break;\n\n        case PathParser.ARC:\n          this.pathA(ctx, boundingBox);\n          break;\n\n        case PathParser.CLOSE_PATH:\n          this.pathZ(ctx, boundingBox);\n          break;\n      }\n    }\n\n    return boundingBox;\n  }\n\n  getBoundingBox(_) {\n    return this.path();\n  }\n\n  getMarkers() {\n    var {\n      pathParser\n    } = this;\n    var points = pathParser.getMarkerPoints();\n    var angles = pathParser.getMarkerAngles();\n    var markers = points.map((point, i) => [point, angles[i]]);\n    return markers;\n  }\n\n  renderChildren(ctx) {\n    this.path(ctx);\n    this.document.screen.mouse.checkPath(this, ctx);\n    var fillRuleStyleProp = this.getStyle('fill-rule');\n\n    if (ctx.fillStyle !== '') {\n      if (fillRuleStyleProp.getString('inherit') !== 'inherit') {\n        ctx.fill(fillRuleStyleProp.getString());\n      } else {\n        ctx.fill();\n      }\n    }\n\n    if (ctx.strokeStyle !== '') {\n      if (this.getAttribute('vector-effect').getString() === 'non-scaling-stroke') {\n        ctx.save();\n        ctx.setTransform(1, 0, 0, 1, 0, 0);\n        ctx.stroke();\n        ctx.restore();\n      } else {\n        ctx.stroke();\n      }\n    }\n\n    var markers = this.getMarkers();\n\n    if (markers) {\n      var markersLastIndex = markers.length - 1;\n      var markerStartStyleProp = this.getStyle('marker-start');\n      var markerMidStyleProp = this.getStyle('marker-mid');\n      var markerEndStyleProp = this.getStyle('marker-end');\n\n      if (markerStartStyleProp.isUrlDefinition()) {\n        var marker = markerStartStyleProp.getDefinition();\n        var [point, angle] = markers[0];\n        marker.render(ctx, point, angle);\n      }\n\n      if (markerMidStyleProp.isUrlDefinition()) {\n        var _marker = markerMidStyleProp.getDefinition();\n\n        for (var i = 1; i < markersLastIndex; i++) {\n          var [_point, _angle] = markers[i];\n\n          _marker.render(ctx, _point, _angle);\n        }\n      }\n\n      if (markerEndStyleProp.isUrlDefinition()) {\n        var _marker2 = markerEndStyleProp.getDefinition();\n\n        var [_point2, _angle2] = markers[markersLastIndex];\n\n        _marker2.render(ctx, _point2, _angle2);\n      }\n    }\n  }\n\n  static pathM(pathParser) {\n    var point = pathParser.getAsCurrentPoint();\n    pathParser.start = pathParser.current;\n    return {\n      point\n    };\n  }\n\n  pathM(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      point\n    } = PathElement.pathM(pathParser);\n    var {\n      x,\n      y\n    } = point;\n    pathParser.addMarker(point);\n    boundingBox.addPoint(x, y);\n\n    if (ctx) {\n      ctx.moveTo(x, y);\n    }\n  }\n\n  static pathL(pathParser) {\n    var {\n      current\n    } = pathParser;\n    var point = pathParser.getAsCurrentPoint();\n    return {\n      current,\n      point\n    };\n  }\n\n  pathL(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      point\n    } = PathElement.pathL(pathParser);\n    var {\n      x,\n      y\n    } = point;\n    pathParser.addMarker(point, current);\n    boundingBox.addPoint(x, y);\n\n    if (ctx) {\n      ctx.lineTo(x, y);\n    }\n  }\n\n  static pathH(pathParser) {\n    var {\n      current,\n      command\n    } = pathParser;\n    var point = new Point((command.relative ? current.x : 0) + command.x, current.y);\n    pathParser.current = point;\n    return {\n      current,\n      point\n    };\n  }\n\n  pathH(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      point\n    } = PathElement.pathH(pathParser);\n    var {\n      x,\n      y\n    } = point;\n    pathParser.addMarker(point, current);\n    boundingBox.addPoint(x, y);\n\n    if (ctx) {\n      ctx.lineTo(x, y);\n    }\n  }\n\n  static pathV(pathParser) {\n    var {\n      current,\n      command\n    } = pathParser;\n    var point = new Point(current.x, (command.relative ? current.y : 0) + command.y);\n    pathParser.current = point;\n    return {\n      current,\n      point\n    };\n  }\n\n  pathV(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      point\n    } = PathElement.pathV(pathParser);\n    var {\n      x,\n      y\n    } = point;\n    pathParser.addMarker(point, current);\n    boundingBox.addPoint(x, y);\n\n    if (ctx) {\n      ctx.lineTo(x, y);\n    }\n  }\n\n  static pathC(pathParser) {\n    var {\n      current\n    } = pathParser;\n    var point = pathParser.getPoint('x1', 'y1');\n    var controlPoint = pathParser.getAsControlPoint('x2', 'y2');\n    var currentPoint = pathParser.getAsCurrentPoint();\n    return {\n      current,\n      point,\n      controlPoint,\n      currentPoint\n    };\n  }\n\n  pathC(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      point,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathC(pathParser);\n    pathParser.addMarker(currentPoint, controlPoint, point);\n    boundingBox.addBezierCurve(current.x, current.y, point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n\n    if (ctx) {\n      ctx.bezierCurveTo(point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    }\n  }\n\n  static pathS(pathParser) {\n    var {\n      current\n    } = pathParser;\n    var point = pathParser.getReflectedControlPoint();\n    var controlPoint = pathParser.getAsControlPoint('x2', 'y2');\n    var currentPoint = pathParser.getAsCurrentPoint();\n    return {\n      current,\n      point,\n      controlPoint,\n      currentPoint\n    };\n  }\n\n  pathS(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      point,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathS(pathParser);\n    pathParser.addMarker(currentPoint, controlPoint, point);\n    boundingBox.addBezierCurve(current.x, current.y, point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n\n    if (ctx) {\n      ctx.bezierCurveTo(point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    }\n  }\n\n  static pathQ(pathParser) {\n    var {\n      current\n    } = pathParser;\n    var controlPoint = pathParser.getAsControlPoint('x1', 'y1');\n    var currentPoint = pathParser.getAsCurrentPoint();\n    return {\n      current,\n      controlPoint,\n      currentPoint\n    };\n  }\n\n  pathQ(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathQ(pathParser);\n    pathParser.addMarker(currentPoint, controlPoint, controlPoint);\n    boundingBox.addQuadraticCurve(current.x, current.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n\n    if (ctx) {\n      ctx.quadraticCurveTo(controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    }\n  }\n\n  static pathT(pathParser) {\n    var {\n      current\n    } = pathParser;\n    var controlPoint = pathParser.getReflectedControlPoint();\n    pathParser.control = controlPoint;\n    var currentPoint = pathParser.getAsCurrentPoint();\n    return {\n      current,\n      controlPoint,\n      currentPoint\n    };\n  }\n\n  pathT(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathT(pathParser);\n    pathParser.addMarker(currentPoint, controlPoint, controlPoint);\n    boundingBox.addQuadraticCurve(current.x, current.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n\n    if (ctx) {\n      ctx.quadraticCurveTo(controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    }\n  }\n\n  static pathA(pathParser) {\n    var {\n      current,\n      command\n    } = pathParser;\n    var {\n      rX,\n      rY,\n      xRot,\n      lArcFlag,\n      sweepFlag\n    } = command;\n    var xAxisRotation = xRot * (Math.PI / 180.0);\n    var currentPoint = pathParser.getAsCurrentPoint(); // Conversion from endpoint to center parameterization\n    // http://www.w3.org/TR/SVG11/implnote.html#ArcImplementationNotes\n    // x1', y1'\n\n    var currp = new Point(Math.cos(xAxisRotation) * (current.x - currentPoint.x) / 2.0 + Math.sin(xAxisRotation) * (current.y - currentPoint.y) / 2.0, -Math.sin(xAxisRotation) * (current.x - currentPoint.x) / 2.0 + Math.cos(xAxisRotation) * (current.y - currentPoint.y) / 2.0); // adjust radii\n\n    var l = Math.pow(currp.x, 2) / Math.pow(rX, 2) + Math.pow(currp.y, 2) / Math.pow(rY, 2);\n\n    if (l > 1) {\n      rX *= Math.sqrt(l);\n      rY *= Math.sqrt(l);\n    } // cx', cy'\n\n\n    var s = (lArcFlag === sweepFlag ? -1 : 1) * Math.sqrt((Math.pow(rX, 2) * Math.pow(rY, 2) - Math.pow(rX, 2) * Math.pow(currp.y, 2) - Math.pow(rY, 2) * Math.pow(currp.x, 2)) / (Math.pow(rX, 2) * Math.pow(currp.y, 2) + Math.pow(rY, 2) * Math.pow(currp.x, 2)));\n\n    if (isNaN(s)) {\n      s = 0;\n    }\n\n    var cpp = new Point(s * rX * currp.y / rY, s * -rY * currp.x / rX); // cx, cy\n\n    var centp = new Point((current.x + currentPoint.x) / 2.0 + Math.cos(xAxisRotation) * cpp.x - Math.sin(xAxisRotation) * cpp.y, (current.y + currentPoint.y) / 2.0 + Math.sin(xAxisRotation) * cpp.x + Math.cos(xAxisRotation) * cpp.y); // initial angle\n\n    var a1 = vectorsAngle([1, 0], [(currp.x - cpp.x) / rX, (currp.y - cpp.y) / rY]); // θ1\n    // angle delta\n\n    var u = [(currp.x - cpp.x) / rX, (currp.y - cpp.y) / rY];\n    var v = [(-currp.x - cpp.x) / rX, (-currp.y - cpp.y) / rY];\n    var ad = vectorsAngle(u, v); // Δθ\n\n    if (vectorsRatio(u, v) <= -1) {\n      ad = Math.PI;\n    }\n\n    if (vectorsRatio(u, v) >= 1) {\n      ad = 0;\n    }\n\n    return {\n      currentPoint,\n      rX,\n      rY,\n      sweepFlag,\n      xAxisRotation,\n      centp,\n      a1,\n      ad\n    };\n  }\n\n  pathA(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      currentPoint,\n      rX,\n      rY,\n      sweepFlag,\n      xAxisRotation,\n      centp,\n      a1,\n      ad\n    } = PathElement.pathA(pathParser); // for markers\n\n    var dir = 1 - sweepFlag ? 1.0 : -1.0;\n    var ah = a1 + dir * (ad / 2.0);\n    var halfWay = new Point(centp.x + rX * Math.cos(ah), centp.y + rY * Math.sin(ah));\n    pathParser.addMarkerAngle(halfWay, ah - dir * Math.PI / 2);\n    pathParser.addMarkerAngle(currentPoint, ah - dir * Math.PI);\n    boundingBox.addPoint(currentPoint.x, currentPoint.y); // TODO: this is too naive, make it better\n\n    if (ctx && !isNaN(a1) && !isNaN(ad)) {\n      var r = rX > rY ? rX : rY;\n      var sx = rX > rY ? 1 : rX / rY;\n      var sy = rX > rY ? rY / rX : 1;\n      ctx.translate(centp.x, centp.y);\n      ctx.rotate(xAxisRotation);\n      ctx.scale(sx, sy);\n      ctx.arc(0, 0, r, a1, a1 + ad, Boolean(1 - sweepFlag));\n      ctx.scale(1 / sx, 1 / sy);\n      ctx.rotate(-xAxisRotation);\n      ctx.translate(-centp.x, -centp.y);\n    }\n  }\n\n  static pathZ(pathParser) {\n    pathParser.current = pathParser.start;\n  }\n\n  pathZ(ctx, boundingBox) {\n    PathElement.pathZ(this.pathParser);\n\n    if (ctx) {\n      // only close path if it is not a straight line\n      if (boundingBox.x1 !== boundingBox.x2 && boundingBox.y1 !== boundingBox.y2) {\n        ctx.closePath();\n      }\n    }\n  }\n\n}\n\nclass GlyphElement extends PathElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'glyph';\n    this.horizAdvX = this.getAttribute('horiz-adv-x').getNumber();\n    this.unicode = this.getAttribute('unicode').getString();\n    this.arabicForm = this.getAttribute('arabic-form').getString();\n  }\n\n}\n\nclass TextElement extends RenderedElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, new.target === TextElement ? true : captureTextNodes);\n    this.type = 'text';\n    this.x = 0;\n    this.y = 0;\n    this.measureCache = -1;\n  }\n\n  setContext(ctx) {\n    var fromMeasure = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    super.setContext(ctx, fromMeasure);\n    var textBaseline = this.getStyle('dominant-baseline').getTextBaseline() || this.getStyle('alignment-baseline').getTextBaseline();\n\n    if (textBaseline) {\n      ctx.textBaseline = textBaseline;\n    }\n  }\n\n  initializeCoordinates() {\n    this.x = 0;\n    this.y = 0;\n    this.leafTexts = [];\n    this.textChunkStart = 0;\n    this.minX = Number.POSITIVE_INFINITY;\n    this.maxX = Number.NEGATIVE_INFINITY;\n  }\n\n  getBoundingBox(ctx) {\n    if (this.type !== 'text') {\n      return this.getTElementBoundingBox(ctx);\n    } // first, calculate child positions\n\n\n    this.initializeCoordinates();\n    this.adjustChildCoordinatesRecursive(ctx);\n    var boundingBox = null; // then calculate bounding box\n\n    this.children.forEach((_, i) => {\n      var childBoundingBox = this.getChildBoundingBox(ctx, this, this, i);\n\n      if (!boundingBox) {\n        boundingBox = childBoundingBox;\n      } else {\n        boundingBox.addBoundingBox(childBoundingBox);\n      }\n    });\n    return boundingBox;\n  }\n\n  getFontSize() {\n    var {\n      document,\n      parent\n    } = this;\n    var inheritFontSize = Font.parse(document.ctx.font).fontSize;\n    var fontSize = parent.getStyle('font-size').getNumber(inheritFontSize);\n    return fontSize;\n  }\n\n  getTElementBoundingBox(ctx) {\n    var fontSize = this.getFontSize();\n    return new BoundingBox(this.x, this.y - fontSize, this.x + this.measureText(ctx), this.y);\n  }\n\n  getGlyph(font, text, i) {\n    var char = text[i];\n    var glyph = null;\n\n    if (font.isArabic) {\n      var len = text.length;\n      var prevChar = text[i - 1];\n      var nextChar = text[i + 1];\n      var arabicForm = 'isolated';\n\n      if ((i === 0 || prevChar === ' ') && i < len - 1 && nextChar !== ' ') {\n        arabicForm = 'terminal';\n      }\n\n      if (i > 0 && prevChar !== ' ' && i < len - 1 && nextChar !== ' ') {\n        arabicForm = 'medial';\n      }\n\n      if (i > 0 && prevChar !== ' ' && (i === len - 1 || nextChar === ' ')) {\n        arabicForm = 'initial';\n      }\n\n      if (typeof font.glyphs[char] !== 'undefined') {\n        // NEED TEST\n        var maybeGlyph = font.glyphs[char];\n        glyph = maybeGlyph instanceof GlyphElement ? maybeGlyph : maybeGlyph[arabicForm];\n      }\n    } else {\n      glyph = font.glyphs[char];\n    }\n\n    if (!glyph) {\n      glyph = font.missingGlyph;\n    }\n\n    return glyph;\n  }\n\n  getText() {\n    return '';\n  }\n\n  getTextFromNode(node) {\n    var textNode = node || this.node;\n    var childNodes = Array.from(textNode.parentNode.childNodes);\n    var index = childNodes.indexOf(textNode);\n    var lastIndex = childNodes.length - 1;\n    var text = compressSpaces( // textNode.value\n    // || textNode.text\n    textNode.textContent || '');\n\n    if (index === 0) {\n      text = trimLeft(text);\n    }\n\n    if (index === lastIndex) {\n      text = trimRight(text);\n    }\n\n    return text;\n  }\n\n  renderChildren(ctx) {\n    if (this.type !== 'text') {\n      this.renderTElementChildren(ctx);\n      return;\n    } // first, calculate child positions\n\n\n    this.initializeCoordinates();\n    this.adjustChildCoordinatesRecursive(ctx); // then render\n\n    this.children.forEach((_, i) => {\n      this.renderChild(ctx, this, this, i);\n    });\n    var {\n      mouse\n    } = this.document.screen; // Do not calc bounding box if mouse is not working.\n\n    if (mouse.isWorking()) {\n      mouse.checkBoundingBox(this, this.getBoundingBox(ctx));\n    }\n  }\n\n  renderTElementChildren(ctx) {\n    var {\n      document,\n      parent\n    } = this;\n    var renderText = this.getText();\n    var customFont = parent.getStyle('font-family').getDefinition();\n\n    if (customFont) {\n      var {\n        unitsPerEm\n      } = customFont.fontFace;\n      var ctxFont = Font.parse(document.ctx.font);\n      var fontSize = parent.getStyle('font-size').getNumber(ctxFont.fontSize);\n      var fontStyle = parent.getStyle('font-style').getString(ctxFont.fontStyle);\n      var scale = fontSize / unitsPerEm;\n      var text = customFont.isRTL ? renderText.split('').reverse().join('') : renderText;\n      var dx = toNumbers(parent.getAttribute('dx').getString());\n      var len = text.length;\n\n      for (var i = 0; i < len; i++) {\n        var glyph = this.getGlyph(customFont, text, i);\n        ctx.translate(this.x, this.y);\n        ctx.scale(scale, -scale);\n        var lw = ctx.lineWidth;\n        ctx.lineWidth = ctx.lineWidth * unitsPerEm / fontSize;\n\n        if (fontStyle === 'italic') {\n          ctx.transform(1, 0, .4, 1, 0, 0);\n        }\n\n        glyph.render(ctx);\n\n        if (fontStyle === 'italic') {\n          ctx.transform(1, 0, -.4, 1, 0, 0);\n        }\n\n        ctx.lineWidth = lw;\n        ctx.scale(1 / scale, -1 / scale);\n        ctx.translate(-this.x, -this.y);\n        this.x += fontSize * (glyph.horizAdvX || customFont.horizAdvX) / unitsPerEm;\n\n        if (typeof dx[i] !== 'undefined' && !isNaN(dx[i])) {\n          this.x += dx[i];\n        }\n      }\n\n      return;\n    }\n\n    var {\n      x,\n      y\n    } = this; // NEED TEST\n    // if (ctx.paintOrder === 'stroke') {\n    // \tif (ctx.strokeStyle) {\n    // \t\tctx.strokeText(renderText, x, y);\n    // \t}\n    // \tif (ctx.fillStyle) {\n    // \t\tctx.fillText(renderText, x, y);\n    // \t}\n    // } else {\n\n    if (ctx.fillStyle) {\n      ctx.fillText(renderText, x, y);\n    }\n\n    if (ctx.strokeStyle) {\n      ctx.strokeText(renderText, x, y);\n    } // }\n\n  }\n\n  applyAnchoring() {\n    if (this.textChunkStart >= this.leafTexts.length) {\n      return;\n    } // This is basically the \"Apply anchoring\" part of https://www.w3.org/TR/SVG2/text.html#TextLayoutAlgorithm.\n    // The difference is that we apply the anchoring as soon as a chunk is finished. This saves some extra looping.\n    // Vertical text is not supported.\n\n\n    var firstElement = this.leafTexts[this.textChunkStart];\n    var textAnchor = firstElement.getStyle('text-anchor').getString('start');\n    var isRTL = false; // we treat RTL like LTR\n\n    var shift = 0;\n\n    if (textAnchor === 'start' && !isRTL || textAnchor === 'end' && isRTL) {\n      shift = firstElement.x - this.minX;\n    } else if (textAnchor === 'end' && !isRTL || textAnchor === 'start' && isRTL) {\n      shift = firstElement.x - this.maxX;\n    } else {\n      shift = firstElement.x - (this.minX + this.maxX) / 2;\n    }\n\n    for (var i = this.textChunkStart; i < this.leafTexts.length; i++) {\n      this.leafTexts[i].x += shift;\n    } // start new chunk\n\n\n    this.minX = Number.POSITIVE_INFINITY;\n    this.maxX = Number.NEGATIVE_INFINITY;\n    this.textChunkStart = this.leafTexts.length;\n  }\n\n  adjustChildCoordinatesRecursive(ctx) {\n    this.children.forEach((_, i) => {\n      this.adjustChildCoordinatesRecursiveCore(ctx, this, this, i);\n    });\n    this.applyAnchoring();\n  }\n\n  adjustChildCoordinatesRecursiveCore(ctx, textParent, parent, i) {\n    var child = parent.children[i];\n\n    if (child.children.length > 0) {\n      child.children.forEach((_, i) => {\n        textParent.adjustChildCoordinatesRecursiveCore(ctx, textParent, child, i);\n      });\n    } else {\n      // only leafs are relevant\n      this.adjustChildCoordinates(ctx, textParent, parent, i);\n    }\n  }\n\n  adjustChildCoordinates(ctx, textParent, parent, i) {\n    var child = parent.children[i];\n\n    if (typeof child.measureText !== 'function') {\n      return child;\n    }\n\n    ctx.save();\n    child.setContext(ctx, true);\n    var xAttr = child.getAttribute('x');\n    var yAttr = child.getAttribute('y');\n    var dxAttr = child.getAttribute('dx');\n    var dyAttr = child.getAttribute('dy');\n    var customFont = child.getStyle('font-family').getDefinition();\n    var isRTL = Boolean(customFont) && customFont.isRTL;\n\n    if (i === 0) {\n      // First children inherit attributes from parent(s). Positional attributes\n      // are only inherited from a parent to it's first child.\n      if (!xAttr.hasValue()) {\n        xAttr.setValue(child.getInheritedAttribute('x'));\n      }\n\n      if (!yAttr.hasValue()) {\n        yAttr.setValue(child.getInheritedAttribute('y'));\n      }\n\n      if (!dxAttr.hasValue()) {\n        dxAttr.setValue(child.getInheritedAttribute('dx'));\n      }\n\n      if (!dyAttr.hasValue()) {\n        dyAttr.setValue(child.getInheritedAttribute('dy'));\n      }\n    }\n\n    var width = child.measureText(ctx);\n\n    if (isRTL) {\n      textParent.x -= width;\n    }\n\n    if (xAttr.hasValue()) {\n      // an \"x\" attribute marks the start of a new chunk\n      textParent.applyAnchoring();\n      child.x = xAttr.getPixels('x');\n\n      if (dxAttr.hasValue()) {\n        child.x += dxAttr.getPixels('x');\n      }\n    } else {\n      if (dxAttr.hasValue()) {\n        textParent.x += dxAttr.getPixels('x');\n      }\n\n      child.x = textParent.x;\n    }\n\n    textParent.x = child.x;\n\n    if (!isRTL) {\n      textParent.x += width;\n    }\n\n    if (yAttr.hasValue()) {\n      child.y = yAttr.getPixels('y');\n\n      if (dyAttr.hasValue()) {\n        child.y += dyAttr.getPixels('y');\n      }\n    } else {\n      if (dyAttr.hasValue()) {\n        textParent.y += dyAttr.getPixels('y');\n      }\n\n      child.y = textParent.y;\n    }\n\n    textParent.y = child.y; // update the current chunk and it's bounds\n\n    textParent.leafTexts.push(child);\n    textParent.minX = Math.min(textParent.minX, child.x, child.x + width);\n    textParent.maxX = Math.max(textParent.maxX, child.x, child.x + width);\n    child.clearContext(ctx);\n    ctx.restore();\n    return child;\n  }\n\n  getChildBoundingBox(ctx, textParent, parent, i) {\n    var child = parent.children[i]; // not a text node?\n\n    if (typeof child.getBoundingBox !== 'function') {\n      return null;\n    }\n\n    var boundingBox = child.getBoundingBox(ctx);\n\n    if (!boundingBox) {\n      return null;\n    }\n\n    child.children.forEach((_, i) => {\n      var childBoundingBox = textParent.getChildBoundingBox(ctx, textParent, child, i);\n      boundingBox.addBoundingBox(childBoundingBox);\n    });\n    return boundingBox;\n  }\n\n  renderChild(ctx, textParent, parent, i) {\n    var child = parent.children[i];\n    child.render(ctx);\n    child.children.forEach((_, i) => {\n      textParent.renderChild(ctx, textParent, child, i);\n    });\n  }\n\n  measureText(ctx) {\n    var {\n      measureCache\n    } = this;\n\n    if (~measureCache) {\n      return measureCache;\n    }\n\n    var renderText = this.getText();\n    var measure = this.measureTargetText(ctx, renderText);\n    this.measureCache = measure;\n    return measure;\n  }\n\n  measureTargetText(ctx, targetText) {\n    if (!targetText.length) {\n      return 0;\n    }\n\n    var {\n      parent\n    } = this;\n    var customFont = parent.getStyle('font-family').getDefinition();\n\n    if (customFont) {\n      var fontSize = this.getFontSize();\n      var text = customFont.isRTL ? targetText.split('').reverse().join('') : targetText;\n      var dx = toNumbers(parent.getAttribute('dx').getString());\n      var len = text.length;\n      var _measure = 0;\n\n      for (var i = 0; i < len; i++) {\n        var glyph = this.getGlyph(customFont, text, i);\n        _measure += (glyph.horizAdvX || customFont.horizAdvX) * fontSize / customFont.fontFace.unitsPerEm;\n\n        if (typeof dx[i] !== 'undefined' && !isNaN(dx[i])) {\n          _measure += dx[i];\n        }\n      }\n\n      return _measure;\n    }\n\n    if (!ctx.measureText) {\n      return targetText.length * 10;\n    }\n\n    ctx.save();\n    this.setContext(ctx, true);\n    var {\n      width: measure\n    } = ctx.measureText(targetText);\n    this.clearContext(ctx);\n    ctx.restore();\n    return measure;\n  }\n  /**\r\n   * Inherits positional attributes from {@link TextElement} parent(s). Attributes\r\n   * are only inherited from a parent to its first child.\r\n   * @param name - The attribute name.\r\n   * @returns The attribute value or null.\r\n   */\n\n\n  getInheritedAttribute(name) {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias,consistent-this\n    var current = this;\n\n    while (current instanceof TextElement && current.isFirstChild()) {\n      var parentAttr = current.parent.getAttribute(name);\n\n      if (parentAttr.hasValue(true)) {\n        return parentAttr.getValue('0');\n      }\n\n      current = current.parent;\n    }\n\n    return null;\n  }\n\n}\n\nclass TSpanElement extends TextElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, new.target === TSpanElement ? true : captureTextNodes);\n    this.type = 'tspan'; // if this node has children, then they own the text\n\n    this.text = this.children.length > 0 ? '' : this.getTextFromNode();\n  }\n\n  getText() {\n    return this.text;\n  }\n\n}\n\nclass TextNode extends TSpanElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'textNode';\n  }\n\n}\n\nclass SVGElement extends RenderedElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'svg';\n    this.root = false;\n  }\n\n  setContext(ctx) {\n    var _this$node$parentNode;\n\n    var {\n      document\n    } = this;\n    var {\n      screen,\n      window\n    } = document;\n    var canvas = ctx.canvas;\n    screen.setDefaults(ctx);\n\n    if (canvas.style && typeof ctx.font !== 'undefined' && window && typeof window.getComputedStyle !== 'undefined') {\n      ctx.font = window.getComputedStyle(canvas).getPropertyValue('font');\n      var fontSizeProp = new Property(document, 'fontSize', Font.parse(ctx.font).fontSize);\n\n      if (fontSizeProp.hasValue()) {\n        document.rootEmSize = fontSizeProp.getPixels('y');\n        document.emSize = document.rootEmSize;\n      }\n    } // create new view port\n\n\n    if (!this.getAttribute('x').hasValue()) {\n      this.getAttribute('x', true).setValue(0);\n    }\n\n    if (!this.getAttribute('y').hasValue()) {\n      this.getAttribute('y', true).setValue(0);\n    }\n\n    var {\n      width,\n      height\n    } = screen.viewPort;\n\n    if (!this.getStyle('width').hasValue()) {\n      this.getStyle('width', true).setValue('100%');\n    }\n\n    if (!this.getStyle('height').hasValue()) {\n      this.getStyle('height', true).setValue('100%');\n    }\n\n    if (!this.getStyle('color').hasValue()) {\n      this.getStyle('color', true).setValue('black');\n    }\n\n    var refXAttr = this.getAttribute('refX');\n    var refYAttr = this.getAttribute('refY');\n    var viewBoxAttr = this.getAttribute('viewBox');\n    var viewBox = viewBoxAttr.hasValue() ? toNumbers(viewBoxAttr.getString()) : null;\n    var clip = !this.root && this.getStyle('overflow').getValue('hidden') !== 'visible';\n    var minX = 0;\n    var minY = 0;\n    var clipX = 0;\n    var clipY = 0;\n\n    if (viewBox) {\n      minX = viewBox[0];\n      minY = viewBox[1];\n    }\n\n    if (!this.root) {\n      width = this.getStyle('width').getPixels('x');\n      height = this.getStyle('height').getPixels('y');\n\n      if (this.type === 'marker') {\n        clipX = minX;\n        clipY = minY;\n        minX = 0;\n        minY = 0;\n      }\n    }\n\n    screen.viewPort.setCurrent(width, height); // Default value of transform-origin is center only for root SVG elements\n    // https://developer.mozilla.org/en-US/docs/Web/SVG/Attribute/transform-origin\n\n    if (this.node // is not temporary SVGElement\n    && (!this.parent || ((_this$node$parentNode = this.node.parentNode) === null || _this$node$parentNode === void 0 ? void 0 : _this$node$parentNode.nodeName) === 'foreignObject') && this.getStyle('transform', false, true).hasValue() && !this.getStyle('transform-origin', false, true).hasValue()) {\n      this.getStyle('transform-origin', true, true).setValue('50% 50%');\n    }\n\n    super.setContext(ctx);\n    ctx.translate(this.getAttribute('x').getPixels('x'), this.getAttribute('y').getPixels('y'));\n\n    if (viewBox) {\n      width = viewBox[2];\n      height = viewBox[3];\n    }\n\n    document.setViewBox({\n      ctx,\n      aspectRatio: this.getAttribute('preserveAspectRatio').getString(),\n      width: screen.viewPort.width,\n      desiredWidth: width,\n      height: screen.viewPort.height,\n      desiredHeight: height,\n      minX,\n      minY,\n      refX: refXAttr.getValue(),\n      refY: refYAttr.getValue(),\n      clip,\n      clipX,\n      clipY\n    });\n\n    if (viewBox) {\n      screen.viewPort.removeCurrent();\n      screen.viewPort.setCurrent(width, height);\n    }\n  }\n\n  clearContext(ctx) {\n    super.clearContext(ctx);\n    this.document.screen.viewPort.removeCurrent();\n  }\n  /**\r\n   * Resize SVG to fit in given size.\r\n   * @param width\r\n   * @param height\r\n   * @param preserveAspectRatio\r\n   */\n\n\n  resize(width) {\n    var height = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : width;\n    var preserveAspectRatio = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    var widthAttr = this.getAttribute('width', true);\n    var heightAttr = this.getAttribute('height', true);\n    var viewBoxAttr = this.getAttribute('viewBox');\n    var styleAttr = this.getAttribute('style');\n    var originWidth = widthAttr.getNumber(0);\n    var originHeight = heightAttr.getNumber(0);\n\n    if (preserveAspectRatio) {\n      if (typeof preserveAspectRatio === 'string') {\n        this.getAttribute('preserveAspectRatio', true).setValue(preserveAspectRatio);\n      } else {\n        var preserveAspectRatioAttr = this.getAttribute('preserveAspectRatio');\n\n        if (preserveAspectRatioAttr.hasValue()) {\n          preserveAspectRatioAttr.setValue(preserveAspectRatioAttr.getString().replace(/^\\s*(\\S.*\\S)\\s*$/, '$1'));\n        }\n      }\n    }\n\n    widthAttr.setValue(width);\n    heightAttr.setValue(height);\n\n    if (!viewBoxAttr.hasValue()) {\n      viewBoxAttr.setValue(\"0 0 \".concat(originWidth || width, \" \").concat(originHeight || height));\n    }\n\n    if (styleAttr.hasValue()) {\n      var widthStyle = this.getStyle('width');\n      var heightStyle = this.getStyle('height');\n\n      if (widthStyle.hasValue()) {\n        widthStyle.setValue(\"\".concat(width, \"px\"));\n      }\n\n      if (heightStyle.hasValue()) {\n        heightStyle.setValue(\"\".concat(height, \"px\"));\n      }\n    }\n  }\n\n}\n\nclass RectElement extends PathElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'rect';\n  }\n\n  path(ctx) {\n    var x = this.getAttribute('x').getPixels('x');\n    var y = this.getAttribute('y').getPixels('y');\n    var width = this.getStyle('width', false, true).getPixels('x');\n    var height = this.getStyle('height', false, true).getPixels('y');\n    var rxAttr = this.getAttribute('rx');\n    var ryAttr = this.getAttribute('ry');\n    var rx = rxAttr.getPixels('x');\n    var ry = ryAttr.getPixels('y');\n\n    if (rxAttr.hasValue() && !ryAttr.hasValue()) {\n      ry = rx;\n    }\n\n    if (ryAttr.hasValue() && !rxAttr.hasValue()) {\n      rx = ry;\n    }\n\n    rx = Math.min(rx, width / 2.0);\n    ry = Math.min(ry, height / 2.0);\n\n    if (ctx) {\n      var KAPPA = 4 * ((Math.sqrt(2) - 1) / 3);\n      ctx.beginPath(); // always start the path so we don't fill prior paths\n\n      if (height > 0 && width > 0) {\n        ctx.moveTo(x + rx, y);\n        ctx.lineTo(x + width - rx, y);\n        ctx.bezierCurveTo(x + width - rx + KAPPA * rx, y, x + width, y + ry - KAPPA * ry, x + width, y + ry);\n        ctx.lineTo(x + width, y + height - ry);\n        ctx.bezierCurveTo(x + width, y + height - ry + KAPPA * ry, x + width - rx + KAPPA * rx, y + height, x + width - rx, y + height);\n        ctx.lineTo(x + rx, y + height);\n        ctx.bezierCurveTo(x + rx - KAPPA * rx, y + height, x, y + height - ry + KAPPA * ry, x, y + height - ry);\n        ctx.lineTo(x, y + ry);\n        ctx.bezierCurveTo(x, y + ry - KAPPA * ry, x + rx - KAPPA * rx, y, x + rx, y);\n        ctx.closePath();\n      }\n    }\n\n    return new BoundingBox(x, y, x + width, y + height);\n  }\n\n  getMarkers() {\n    return null;\n  }\n\n}\n\nclass CircleElement extends PathElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'circle';\n  }\n\n  path(ctx) {\n    var cx = this.getAttribute('cx').getPixels('x');\n    var cy = this.getAttribute('cy').getPixels('y');\n    var r = this.getAttribute('r').getPixels();\n\n    if (ctx && r > 0) {\n      ctx.beginPath();\n      ctx.arc(cx, cy, r, 0, Math.PI * 2, false);\n      ctx.closePath();\n    }\n\n    return new BoundingBox(cx - r, cy - r, cx + r, cy + r);\n  }\n\n  getMarkers() {\n    return null;\n  }\n\n}\n\nclass EllipseElement extends PathElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'ellipse';\n  }\n\n  path(ctx) {\n    var KAPPA = 4 * ((Math.sqrt(2) - 1) / 3);\n    var rx = this.getAttribute('rx').getPixels('x');\n    var ry = this.getAttribute('ry').getPixels('y');\n    var cx = this.getAttribute('cx').getPixels('x');\n    var cy = this.getAttribute('cy').getPixels('y');\n\n    if (ctx && rx > 0 && ry > 0) {\n      ctx.beginPath();\n      ctx.moveTo(cx + rx, cy);\n      ctx.bezierCurveTo(cx + rx, cy + KAPPA * ry, cx + KAPPA * rx, cy + ry, cx, cy + ry);\n      ctx.bezierCurveTo(cx - KAPPA * rx, cy + ry, cx - rx, cy + KAPPA * ry, cx - rx, cy);\n      ctx.bezierCurveTo(cx - rx, cy - KAPPA * ry, cx - KAPPA * rx, cy - ry, cx, cy - ry);\n      ctx.bezierCurveTo(cx + KAPPA * rx, cy - ry, cx + rx, cy - KAPPA * ry, cx + rx, cy);\n      ctx.closePath();\n    }\n\n    return new BoundingBox(cx - rx, cy - ry, cx + rx, cy + ry);\n  }\n\n  getMarkers() {\n    return null;\n  }\n\n}\n\nclass LineElement extends PathElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'line';\n  }\n\n  getPoints() {\n    return [new Point(this.getAttribute('x1').getPixels('x'), this.getAttribute('y1').getPixels('y')), new Point(this.getAttribute('x2').getPixels('x'), this.getAttribute('y2').getPixels('y'))];\n  }\n\n  path(ctx) {\n    var [{\n      x: x0,\n      y: y0\n    }, {\n      x: x1,\n      y: y1\n    }] = this.getPoints();\n\n    if (ctx) {\n      ctx.beginPath();\n      ctx.moveTo(x0, y0);\n      ctx.lineTo(x1, y1);\n    }\n\n    return new BoundingBox(x0, y0, x1, y1);\n  }\n\n  getMarkers() {\n    var [p0, p1] = this.getPoints();\n    var a = p0.angleTo(p1);\n    return [[p0, a], [p1, a]];\n  }\n\n}\n\nclass PolylineElement extends PathElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'polyline';\n    this.points = [];\n    this.points = Point.parsePath(this.getAttribute('points').getString());\n  }\n\n  path(ctx) {\n    var {\n      points\n    } = this;\n    var [{\n      x: x0,\n      y: y0\n    }] = points;\n    var boundingBox = new BoundingBox(x0, y0);\n\n    if (ctx) {\n      ctx.beginPath();\n      ctx.moveTo(x0, y0);\n    }\n\n    points.forEach(_ref => {\n      var {\n        x,\n        y\n      } = _ref;\n      boundingBox.addPoint(x, y);\n\n      if (ctx) {\n        ctx.lineTo(x, y);\n      }\n    });\n    return boundingBox;\n  }\n\n  getMarkers() {\n    var {\n      points\n    } = this;\n    var lastIndex = points.length - 1;\n    var markers = [];\n    points.forEach((point, i) => {\n      if (i === lastIndex) {\n        return;\n      }\n\n      markers.push([point, point.angleTo(points[i + 1])]);\n    });\n\n    if (markers.length > 0) {\n      markers.push([points[points.length - 1], markers[markers.length - 1][1]]);\n    }\n\n    return markers;\n  }\n\n}\n\nclass PolygonElement extends PolylineElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'polygon';\n  }\n\n  path(ctx) {\n    var boundingBox = super.path(ctx);\n    var [{\n      x,\n      y\n    }] = this.points;\n\n    if (ctx) {\n      ctx.lineTo(x, y);\n      ctx.closePath();\n    }\n\n    return boundingBox;\n  }\n\n}\n\nclass PatternElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'pattern';\n  }\n\n  createPattern(ctx, _, parentOpacityProp) {\n    var width = this.getStyle('width').getPixels('x', true);\n    var height = this.getStyle('height').getPixels('y', true); // render me using a temporary svg element\n\n    var patternSvg = new SVGElement(this.document, null);\n    patternSvg.attributes.viewBox = new Property(this.document, 'viewBox', this.getAttribute('viewBox').getValue());\n    patternSvg.attributes.width = new Property(this.document, 'width', \"\".concat(width, \"px\"));\n    patternSvg.attributes.height = new Property(this.document, 'height', \"\".concat(height, \"px\"));\n    patternSvg.attributes.transform = new Property(this.document, 'transform', this.getAttribute('patternTransform').getValue());\n    patternSvg.children = this.children;\n    var patternCanvas = this.document.createCanvas(width, height);\n    var patternCtx = patternCanvas.getContext('2d');\n    var xAttr = this.getAttribute('x');\n    var yAttr = this.getAttribute('y');\n\n    if (xAttr.hasValue() && yAttr.hasValue()) {\n      patternCtx.translate(xAttr.getPixels('x', true), yAttr.getPixels('y', true));\n    }\n\n    if (parentOpacityProp.hasValue()) {\n      this.styles['fill-opacity'] = parentOpacityProp;\n    } else {\n      Reflect.deleteProperty(this.styles, 'fill-opacity');\n    } // render 3x3 grid so when we transform there's no white space on edges\n\n\n    for (var x = -1; x <= 1; x++) {\n      for (var y = -1; y <= 1; y++) {\n        patternCtx.save();\n        patternSvg.attributes.x = new Property(this.document, 'x', x * patternCanvas.width);\n        patternSvg.attributes.y = new Property(this.document, 'y', y * patternCanvas.height);\n        patternSvg.render(patternCtx);\n        patternCtx.restore();\n      }\n    }\n\n    var pattern = ctx.createPattern(patternCanvas, 'repeat');\n    return pattern;\n  }\n\n}\n\nclass MarkerElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'marker';\n  }\n\n  render(ctx, point, angle) {\n    if (!point) {\n      return;\n    }\n\n    var {\n      x,\n      y\n    } = point;\n    var orient = this.getAttribute('orient').getString('auto');\n    var markerUnits = this.getAttribute('markerUnits').getString('strokeWidth');\n    ctx.translate(x, y);\n\n    if (orient === 'auto') {\n      ctx.rotate(angle);\n    }\n\n    if (markerUnits === 'strokeWidth') {\n      ctx.scale(ctx.lineWidth, ctx.lineWidth);\n    }\n\n    ctx.save(); // render me using a temporary svg element\n\n    var markerSvg = new SVGElement(this.document, null);\n    markerSvg.type = this.type;\n    markerSvg.attributes.viewBox = new Property(this.document, 'viewBox', this.getAttribute('viewBox').getValue());\n    markerSvg.attributes.refX = new Property(this.document, 'refX', this.getAttribute('refX').getValue());\n    markerSvg.attributes.refY = new Property(this.document, 'refY', this.getAttribute('refY').getValue());\n    markerSvg.attributes.width = new Property(this.document, 'width', this.getAttribute('markerWidth').getValue());\n    markerSvg.attributes.height = new Property(this.document, 'height', this.getAttribute('markerHeight').getValue());\n    markerSvg.attributes.overflow = new Property(this.document, 'overflow', this.getAttribute('overflow').getValue());\n    markerSvg.attributes.fill = new Property(this.document, 'fill', this.getAttribute('fill').getColor('black'));\n    markerSvg.attributes.stroke = new Property(this.document, 'stroke', this.getAttribute('stroke').getValue('none'));\n    markerSvg.children = this.children;\n    markerSvg.render(ctx);\n    ctx.restore();\n\n    if (markerUnits === 'strokeWidth') {\n      ctx.scale(1 / ctx.lineWidth, 1 / ctx.lineWidth);\n    }\n\n    if (orient === 'auto') {\n      ctx.rotate(-angle);\n    }\n\n    ctx.translate(-x, -y);\n  }\n\n}\n\nclass DefsElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'defs';\n  }\n\n  render() {// NOOP\n  }\n\n}\n\nclass GElement extends RenderedElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'g';\n  }\n\n  getBoundingBox(ctx) {\n    var boundingBox = new BoundingBox();\n    this.children.forEach(child => {\n      boundingBox.addBoundingBox(child.getBoundingBox(ctx));\n    });\n    return boundingBox;\n  }\n\n}\n\nclass GradientElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.attributesToInherit = ['gradientUnits'];\n    this.stops = [];\n    var {\n      stops,\n      children\n    } = this;\n    children.forEach(child => {\n      if (child.type === 'stop') {\n        stops.push(child);\n      }\n    });\n  }\n\n  getGradientUnits() {\n    return this.getAttribute('gradientUnits').getString('objectBoundingBox');\n  }\n\n  createGradient(ctx, element, parentOpacityProp) {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias, consistent-this\n    var stopsContainer = this;\n\n    if (this.getHrefAttribute().hasValue()) {\n      stopsContainer = this.getHrefAttribute().getDefinition();\n      this.inheritStopContainer(stopsContainer);\n    }\n\n    var {\n      stops\n    } = stopsContainer;\n    var gradient = this.getGradient(ctx, element);\n\n    if (!gradient) {\n      return this.addParentOpacity(parentOpacityProp, stops[stops.length - 1].color);\n    }\n\n    stops.forEach(stop => {\n      gradient.addColorStop(stop.offset, this.addParentOpacity(parentOpacityProp, stop.color));\n    });\n\n    if (this.getAttribute('gradientTransform').hasValue()) {\n      // render as transformed pattern on temporary canvas\n      var {\n        document\n      } = this;\n      var {\n        MAX_VIRTUAL_PIXELS,\n        viewPort\n      } = document.screen;\n      var [rootView] = viewPort.viewPorts;\n      var rect = new RectElement(document, null);\n      rect.attributes.x = new Property(document, 'x', -MAX_VIRTUAL_PIXELS / 3.0);\n      rect.attributes.y = new Property(document, 'y', -MAX_VIRTUAL_PIXELS / 3.0);\n      rect.attributes.width = new Property(document, 'width', MAX_VIRTUAL_PIXELS);\n      rect.attributes.height = new Property(document, 'height', MAX_VIRTUAL_PIXELS);\n      var group = new GElement(document, null);\n      group.attributes.transform = new Property(document, 'transform', this.getAttribute('gradientTransform').getValue());\n      group.children = [rect];\n      var patternSvg = new SVGElement(document, null);\n      patternSvg.attributes.x = new Property(document, 'x', 0);\n      patternSvg.attributes.y = new Property(document, 'y', 0);\n      patternSvg.attributes.width = new Property(document, 'width', rootView.width);\n      patternSvg.attributes.height = new Property(document, 'height', rootView.height);\n      patternSvg.children = [group];\n      var patternCanvas = document.createCanvas(rootView.width, rootView.height);\n      var patternCtx = patternCanvas.getContext('2d');\n      patternCtx.fillStyle = gradient;\n      patternSvg.render(patternCtx);\n      return patternCtx.createPattern(patternCanvas, 'no-repeat');\n    }\n\n    return gradient;\n  }\n\n  inheritStopContainer(stopsContainer) {\n    this.attributesToInherit.forEach(attributeToInherit => {\n      if (!this.getAttribute(attributeToInherit).hasValue() && stopsContainer.getAttribute(attributeToInherit).hasValue()) {\n        this.getAttribute(attributeToInherit, true).setValue(stopsContainer.getAttribute(attributeToInherit).getValue());\n      }\n    });\n  }\n\n  addParentOpacity(parentOpacityProp, color) {\n    if (parentOpacityProp.hasValue()) {\n      var colorProp = new Property(this.document, 'color', color);\n      return colorProp.addOpacity(parentOpacityProp).getColor();\n    }\n\n    return color;\n  }\n\n}\n\nclass LinearGradientElement extends GradientElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'linearGradient';\n    this.attributesToInherit.push('x1', 'y1', 'x2', 'y2');\n  }\n\n  getGradient(ctx, element) {\n    var isBoundingBoxUnits = this.getGradientUnits() === 'objectBoundingBox';\n    var boundingBox = isBoundingBoxUnits ? element.getBoundingBox(ctx) : null;\n\n    if (isBoundingBoxUnits && !boundingBox) {\n      return null;\n    }\n\n    if (!this.getAttribute('x1').hasValue() && !this.getAttribute('y1').hasValue() && !this.getAttribute('x2').hasValue() && !this.getAttribute('y2').hasValue()) {\n      this.getAttribute('x1', true).setValue(0);\n      this.getAttribute('y1', true).setValue(0);\n      this.getAttribute('x2', true).setValue(1);\n      this.getAttribute('y2', true).setValue(0);\n    }\n\n    var x1 = isBoundingBoxUnits ? boundingBox.x + boundingBox.width * this.getAttribute('x1').getNumber() : this.getAttribute('x1').getPixels('x');\n    var y1 = isBoundingBoxUnits ? boundingBox.y + boundingBox.height * this.getAttribute('y1').getNumber() : this.getAttribute('y1').getPixels('y');\n    var x2 = isBoundingBoxUnits ? boundingBox.x + boundingBox.width * this.getAttribute('x2').getNumber() : this.getAttribute('x2').getPixels('x');\n    var y2 = isBoundingBoxUnits ? boundingBox.y + boundingBox.height * this.getAttribute('y2').getNumber() : this.getAttribute('y2').getPixels('y');\n\n    if (x1 === x2 && y1 === y2) {\n      return null;\n    }\n\n    return ctx.createLinearGradient(x1, y1, x2, y2);\n  }\n\n}\n\nclass RadialGradientElement extends GradientElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'radialGradient';\n    this.attributesToInherit.push('cx', 'cy', 'r', 'fx', 'fy', 'fr');\n  }\n\n  getGradient(ctx, element) {\n    var isBoundingBoxUnits = this.getGradientUnits() === 'objectBoundingBox';\n    var boundingBox = element.getBoundingBox(ctx);\n\n    if (isBoundingBoxUnits && !boundingBox) {\n      return null;\n    }\n\n    if (!this.getAttribute('cx').hasValue()) {\n      this.getAttribute('cx', true).setValue('50%');\n    }\n\n    if (!this.getAttribute('cy').hasValue()) {\n      this.getAttribute('cy', true).setValue('50%');\n    }\n\n    if (!this.getAttribute('r').hasValue()) {\n      this.getAttribute('r', true).setValue('50%');\n    }\n\n    var cx = isBoundingBoxUnits ? boundingBox.x + boundingBox.width * this.getAttribute('cx').getNumber() : this.getAttribute('cx').getPixels('x');\n    var cy = isBoundingBoxUnits ? boundingBox.y + boundingBox.height * this.getAttribute('cy').getNumber() : this.getAttribute('cy').getPixels('y');\n    var fx = cx;\n    var fy = cy;\n\n    if (this.getAttribute('fx').hasValue()) {\n      fx = isBoundingBoxUnits ? boundingBox.x + boundingBox.width * this.getAttribute('fx').getNumber() : this.getAttribute('fx').getPixels('x');\n    }\n\n    if (this.getAttribute('fy').hasValue()) {\n      fy = isBoundingBoxUnits ? boundingBox.y + boundingBox.height * this.getAttribute('fy').getNumber() : this.getAttribute('fy').getPixels('y');\n    }\n\n    var r = isBoundingBoxUnits ? (boundingBox.width + boundingBox.height) / 2.0 * this.getAttribute('r').getNumber() : this.getAttribute('r').getPixels();\n    var fr = this.getAttribute('fr').getPixels();\n    return ctx.createRadialGradient(fx, fy, fr, cx, cy, r);\n  }\n\n}\n\nclass StopElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'stop';\n    var offset = Math.max(0, Math.min(1, this.getAttribute('offset').getNumber()));\n    var stopOpacity = this.getStyle('stop-opacity');\n    var stopColor = this.getStyle('stop-color', true);\n\n    if (stopColor.getString() === '') {\n      stopColor.setValue('#000');\n    }\n\n    if (stopOpacity.hasValue()) {\n      stopColor = stopColor.addOpacity(stopOpacity);\n    }\n\n    this.offset = offset;\n    this.color = stopColor.getColor();\n  }\n\n}\n\nclass AnimateElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'animate';\n    this.duration = 0;\n    this.initialValue = null;\n    this.initialUnits = '';\n    this.removed = false;\n    this.frozen = false;\n    document.screen.animations.push(this);\n    this.begin = this.getAttribute('begin').getMilliseconds();\n    this.maxDuration = this.begin + this.getAttribute('dur').getMilliseconds();\n    this.from = this.getAttribute('from');\n    this.to = this.getAttribute('to');\n    this.values = new Property(document, 'values', null);\n    var valuesAttr = this.getAttribute('values');\n\n    if (valuesAttr.hasValue()) {\n      this.values.setValue(valuesAttr.getString().split(';'));\n    }\n  }\n\n  getProperty() {\n    var attributeType = this.getAttribute('attributeType').getString();\n    var attributeName = this.getAttribute('attributeName').getString();\n\n    if (attributeType === 'CSS') {\n      return this.parent.getStyle(attributeName, true);\n    }\n\n    return this.parent.getAttribute(attributeName, true);\n  }\n\n  calcValue() {\n    var {\n      initialUnits\n    } = this;\n    var {\n      progress,\n      from,\n      to\n    } = this.getProgress(); // tween value linearly\n\n    var newValue = from.getNumber() + (to.getNumber() - from.getNumber()) * progress;\n\n    if (initialUnits === '%') {\n      newValue *= 100.0; // numValue() returns 0-1 whereas properties are 0-100\n    }\n\n    return \"\".concat(newValue).concat(initialUnits);\n  }\n\n  update(delta) {\n    var {\n      parent\n    } = this;\n    var prop = this.getProperty(); // set initial value\n\n    if (!this.initialValue) {\n      this.initialValue = prop.getString();\n      this.initialUnits = prop.getUnits();\n    } // if we're past the end time\n\n\n    if (this.duration > this.maxDuration) {\n      var fill = this.getAttribute('fill').getString('remove'); // loop for indefinitely repeating animations\n\n      if (this.getAttribute('repeatCount').getString() === 'indefinite' || this.getAttribute('repeatDur').getString() === 'indefinite') {\n        this.duration = 0;\n      } else if (fill === 'freeze' && !this.frozen) {\n        this.frozen = true;\n        parent.animationFrozen = true;\n        parent.animationFrozenValue = prop.getString();\n      } else if (fill === 'remove' && !this.removed) {\n        this.removed = true;\n        prop.setValue(parent.animationFrozen ? parent.animationFrozenValue : this.initialValue);\n        return true;\n      }\n\n      return false;\n    }\n\n    this.duration += delta; // if we're past the begin time\n\n    var updated = false;\n\n    if (this.begin < this.duration) {\n      var newValue = this.calcValue(); // tween\n\n      var typeAttr = this.getAttribute('type');\n\n      if (typeAttr.hasValue()) {\n        // for transform, etc.\n        var type = typeAttr.getString();\n        newValue = \"\".concat(type, \"(\").concat(newValue, \")\");\n      }\n\n      prop.setValue(newValue);\n      updated = true;\n    }\n\n    return updated;\n  }\n\n  getProgress() {\n    var {\n      document,\n      values\n    } = this;\n    var result = {\n      progress: (this.duration - this.begin) / (this.maxDuration - this.begin)\n    };\n\n    if (values.hasValue()) {\n      var p = result.progress * (values.getValue().length - 1);\n      var lb = Math.floor(p);\n      var ub = Math.ceil(p);\n      result.from = new Property(document, 'from', parseFloat(values.getValue()[lb]));\n      result.to = new Property(document, 'to', parseFloat(values.getValue()[ub]));\n      result.progress = (p - lb) / (ub - lb);\n    } else {\n      result.from = this.from;\n      result.to = this.to;\n    }\n\n    return result;\n  }\n\n}\n\nclass AnimateColorElement extends AnimateElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'animateColor';\n  }\n\n  calcValue() {\n    var {\n      progress,\n      from,\n      to\n    } = this.getProgress();\n    var colorFrom = new rgbcolor__WEBPACK_IMPORTED_MODULE_13__(from.getColor());\n    var colorTo = new rgbcolor__WEBPACK_IMPORTED_MODULE_13__(to.getColor());\n\n    if (colorFrom.ok && colorTo.ok) {\n      // tween color linearly\n      var r = colorFrom.r + (colorTo.r - colorFrom.r) * progress;\n      var g = colorFrom.g + (colorTo.g - colorFrom.g) * progress;\n      var b = colorFrom.b + (colorTo.b - colorFrom.b) * progress; // ? alpha\n\n      return \"rgb(\".concat(Math.floor(r), \", \").concat(Math.floor(g), \", \").concat(Math.floor(b), \")\");\n    }\n\n    return this.getAttribute('from').getColor();\n  }\n\n}\n\nclass AnimateTransformElement extends AnimateElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'animateTransform';\n  }\n\n  calcValue() {\n    var {\n      progress,\n      from,\n      to\n    } = this.getProgress(); // tween value linearly\n\n    var transformFrom = toNumbers(from.getString());\n    var transformTo = toNumbers(to.getString());\n    var newValue = transformFrom.map((from, i) => {\n      var to = transformTo[i];\n      return from + (to - from) * progress;\n    }).join(' ');\n    return newValue;\n  }\n\n}\n\nclass FontElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'font';\n    this.glyphs = Object.create(null);\n    this.horizAdvX = this.getAttribute('horiz-adv-x').getNumber();\n    var {\n      definitions\n    } = document;\n    var {\n      children\n    } = this;\n\n    for (var child of children) {\n      switch (child.type) {\n        case 'font-face':\n          {\n            this.fontFace = child;\n            var fontFamilyStyle = child.getStyle('font-family');\n\n            if (fontFamilyStyle.hasValue()) {\n              definitions[fontFamilyStyle.getString()] = this;\n            }\n\n            break;\n          }\n\n        case 'missing-glyph':\n          this.missingGlyph = child;\n          break;\n\n        case 'glyph':\n          {\n            var glyph = child;\n\n            if (glyph.arabicForm) {\n              this.isRTL = true;\n              this.isArabic = true;\n\n              if (typeof this.glyphs[glyph.unicode] === 'undefined') {\n                this.glyphs[glyph.unicode] = Object.create(null);\n              }\n\n              this.glyphs[glyph.unicode][glyph.arabicForm] = glyph;\n            } else {\n              this.glyphs[glyph.unicode] = glyph;\n            }\n\n            break;\n          }\n      }\n    }\n  }\n\n  render() {// NO RENDER\n  }\n\n}\n\nclass FontFaceElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'font-face';\n    this.ascent = this.getAttribute('ascent').getNumber();\n    this.descent = this.getAttribute('descent').getNumber();\n    this.unitsPerEm = this.getAttribute('units-per-em').getNumber();\n  }\n\n}\n\nclass MissingGlyphElement extends PathElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'missing-glyph';\n    this.horizAdvX = 0;\n  }\n\n}\n\nclass TRefElement extends TextElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'tref';\n  }\n\n  getText() {\n    var element = this.getHrefAttribute().getDefinition();\n\n    if (element) {\n      var firstChild = element.children[0];\n\n      if (firstChild) {\n        return firstChild.getText();\n      }\n    }\n\n    return '';\n  }\n\n}\n\nclass AElement extends TextElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'a';\n    var {\n      childNodes\n    } = node;\n    var firstChild = childNodes[0];\n    var hasText = childNodes.length > 0 && Array.from(childNodes).every(node => node.nodeType === 3);\n    this.hasText = hasText;\n    this.text = hasText ? this.getTextFromNode(firstChild) : '';\n  }\n\n  getText() {\n    return this.text;\n  }\n\n  renderChildren(ctx) {\n    if (this.hasText) {\n      // render as text element\n      super.renderChildren(ctx);\n      var {\n        document,\n        x,\n        y\n      } = this;\n      var {\n        mouse\n      } = document.screen;\n      var fontSize = new Property(document, 'fontSize', Font.parse(document.ctx.font).fontSize); // Do not calc bounding box if mouse is not working.\n\n      if (mouse.isWorking()) {\n        mouse.checkBoundingBox(this, new BoundingBox(x, y - fontSize.getPixels('y'), x + this.measureText(ctx), y));\n      }\n    } else if (this.children.length > 0) {\n      // render as temporary group\n      var g = new GElement(this.document, null);\n      g.children = this.children;\n      g.parent = this;\n      g.render(ctx);\n    }\n  }\n\n  onClick() {\n    var {\n      window\n    } = this.document;\n\n    if (window) {\n      window.open(this.getHrefAttribute().getString());\n    }\n  }\n\n  onMouseMove() {\n    var ctx = this.document.ctx;\n    ctx.canvas.style.cursor = 'pointer';\n  }\n\n}\n\nfunction ownKeys$2(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread$2(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys$2(Object(source), true).forEach(function (key) { _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_7__(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys$2(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\nclass TextPathElement extends TextElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'textPath';\n    this.textWidth = 0;\n    this.textHeight = 0;\n    this.pathLength = -1;\n    this.glyphInfo = null;\n    this.letterSpacingCache = [];\n    this.measuresCache = new Map([['', 0]]);\n    var pathElement = this.getHrefAttribute().getDefinition();\n    this.text = this.getTextFromNode();\n    this.dataArray = this.parsePathData(pathElement);\n  }\n\n  getText() {\n    return this.text;\n  }\n\n  path(ctx) {\n    var {\n      dataArray\n    } = this;\n\n    if (ctx) {\n      ctx.beginPath();\n    }\n\n    dataArray.forEach(_ref => {\n      var {\n        type,\n        points\n      } = _ref;\n\n      switch (type) {\n        case PathParser.LINE_TO:\n          if (ctx) {\n            ctx.lineTo(points[0], points[1]);\n          }\n\n          break;\n\n        case PathParser.MOVE_TO:\n          if (ctx) {\n            ctx.moveTo(points[0], points[1]);\n          }\n\n          break;\n\n        case PathParser.CURVE_TO:\n          if (ctx) {\n            ctx.bezierCurveTo(points[0], points[1], points[2], points[3], points[4], points[5]);\n          }\n\n          break;\n\n        case PathParser.QUAD_TO:\n          if (ctx) {\n            ctx.quadraticCurveTo(points[0], points[1], points[2], points[3]);\n          }\n\n          break;\n\n        case PathParser.ARC:\n          {\n            var [cx, cy, rx, ry, theta, dTheta, psi, fs] = points;\n            var r = rx > ry ? rx : ry;\n            var scaleX = rx > ry ? 1 : rx / ry;\n            var scaleY = rx > ry ? ry / rx : 1;\n\n            if (ctx) {\n              ctx.translate(cx, cy);\n              ctx.rotate(psi);\n              ctx.scale(scaleX, scaleY);\n              ctx.arc(0, 0, r, theta, theta + dTheta, Boolean(1 - fs));\n              ctx.scale(1 / scaleX, 1 / scaleY);\n              ctx.rotate(-psi);\n              ctx.translate(-cx, -cy);\n            }\n\n            break;\n          }\n\n        case PathParser.CLOSE_PATH:\n          if (ctx) {\n            ctx.closePath();\n          }\n\n          break;\n      }\n    });\n  }\n\n  renderChildren(ctx) {\n    this.setTextData(ctx);\n    ctx.save();\n    var textDecoration = this.parent.getStyle('text-decoration').getString();\n    var fontSize = this.getFontSize();\n    var {\n      glyphInfo\n    } = this;\n    var fill = ctx.fillStyle;\n\n    if (textDecoration === 'underline') {\n      ctx.beginPath();\n    }\n\n    glyphInfo.forEach((glyph, i) => {\n      var {\n        p0,\n        p1,\n        rotation,\n        text: partialText\n      } = glyph;\n      ctx.save();\n      ctx.translate(p0.x, p0.y);\n      ctx.rotate(rotation);\n\n      if (ctx.fillStyle) {\n        ctx.fillText(partialText, 0, 0);\n      }\n\n      if (ctx.strokeStyle) {\n        ctx.strokeText(partialText, 0, 0);\n      }\n\n      ctx.restore();\n\n      if (textDecoration === 'underline') {\n        if (i === 0) {\n          ctx.moveTo(p0.x, p0.y + fontSize / 8);\n        }\n\n        ctx.lineTo(p1.x, p1.y + fontSize / 5);\n      } // // To assist with debugging visually, uncomment following\n      //\n      // ctx.beginPath();\n      // if (i % 2)\n      // \tctx.strokeStyle = 'red';\n      // else\n      // \tctx.strokeStyle = 'green';\n      // ctx.moveTo(p0.x, p0.y);\n      // ctx.lineTo(p1.x, p1.y);\n      // ctx.stroke();\n      // ctx.closePath();\n\n    });\n\n    if (textDecoration === 'underline') {\n      ctx.lineWidth = fontSize / 20;\n      ctx.strokeStyle = fill;\n      ctx.stroke();\n      ctx.closePath();\n    }\n\n    ctx.restore();\n  }\n\n  getLetterSpacingAt() {\n    var idx = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n    return this.letterSpacingCache[idx] || 0;\n  }\n\n  findSegmentToFitChar(ctx, anchor, textFullWidth, fullPathWidth, spacesNumber, inputOffset, dy, c, charI) {\n    var offset = inputOffset;\n    var glyphWidth = this.measureText(ctx, c);\n\n    if (c === ' ' && anchor === 'justify' && textFullWidth < fullPathWidth) {\n      glyphWidth += (fullPathWidth - textFullWidth) / spacesNumber;\n    }\n\n    if (charI > -1) {\n      offset += this.getLetterSpacingAt(charI);\n    }\n\n    var splineStep = this.textHeight / 20;\n    var p0 = this.getEquidistantPointOnPath(offset, splineStep, 0);\n    var p1 = this.getEquidistantPointOnPath(offset + glyphWidth, splineStep, 0);\n    var segment = {\n      p0,\n      p1\n    };\n    var rotation = p0 && p1 ? Math.atan2(p1.y - p0.y, p1.x - p0.x) : 0;\n\n    if (dy) {\n      var dyX = Math.cos(Math.PI / 2 + rotation) * dy;\n      var dyY = Math.cos(-rotation) * dy;\n      segment.p0 = _objectSpread$2(_objectSpread$2({}, p0), {}, {\n        x: p0.x + dyX,\n        y: p0.y + dyY\n      });\n      segment.p1 = _objectSpread$2(_objectSpread$2({}, p1), {}, {\n        x: p1.x + dyX,\n        y: p1.y + dyY\n      });\n    }\n\n    offset += glyphWidth;\n    return {\n      offset,\n      segment,\n      rotation\n    };\n  }\n\n  measureText(ctx, text) {\n    var {\n      measuresCache\n    } = this;\n    var targetText = text || this.getText();\n\n    if (measuresCache.has(targetText)) {\n      return measuresCache.get(targetText);\n    }\n\n    var measure = this.measureTargetText(ctx, targetText);\n    measuresCache.set(targetText, measure);\n    return measure;\n  } // This method supposes what all custom fonts already loaded.\n  // If some font will be loaded after this method call, <textPath> will not be rendered correctly.\n  // You need to call this method manually to update glyphs cache.\n\n\n  setTextData(ctx) {\n    if (this.glyphInfo) {\n      return;\n    }\n\n    var renderText = this.getText();\n    var chars = renderText.split('');\n    var spacesNumber = renderText.split(' ').length - 1;\n    var dx = this.parent.getAttribute('dx').split().map(_ => _.getPixels('x'));\n    var dy = this.parent.getAttribute('dy').getPixels('y');\n    var anchor = this.parent.getStyle('text-anchor').getString('start');\n    var thisSpacing = this.getStyle('letter-spacing');\n    var parentSpacing = this.parent.getStyle('letter-spacing');\n    var letterSpacing = 0;\n\n    if (!thisSpacing.hasValue() || thisSpacing.getValue() === 'inherit') {\n      letterSpacing = parentSpacing.getPixels();\n    } else if (thisSpacing.hasValue()) {\n      if (thisSpacing.getValue() !== 'initial' && thisSpacing.getValue() !== 'unset') {\n        letterSpacing = thisSpacing.getPixels();\n      }\n    } // fill letter-spacing cache\n\n\n    var letterSpacingCache = [];\n    var textLen = renderText.length;\n    this.letterSpacingCache = letterSpacingCache;\n\n    for (var i = 0; i < textLen; i++) {\n      letterSpacingCache.push(typeof dx[i] !== 'undefined' ? dx[i] : letterSpacing);\n    }\n\n    var dxSum = letterSpacingCache.reduce((acc, cur, i) => i === 0 ? 0 : acc + cur || 0, 0);\n    var textWidth = this.measureText(ctx);\n    var textFullWidth = Math.max(textWidth + dxSum, 0);\n    this.textWidth = textWidth;\n    this.textHeight = this.getFontSize();\n    this.glyphInfo = [];\n    var fullPathWidth = this.getPathLength();\n    var startOffset = this.getStyle('startOffset').getNumber(0) * fullPathWidth;\n    var offset = 0;\n\n    if (anchor === 'middle' || anchor === 'center') {\n      offset = -textFullWidth / 2;\n    }\n\n    if (anchor === 'end' || anchor === 'right') {\n      offset = -textFullWidth;\n    }\n\n    offset += startOffset;\n    chars.forEach((char, i) => {\n      // Find such segment what distance between p0 and p1 is approx. width of glyph\n      var {\n        offset: nextOffset,\n        segment,\n        rotation\n      } = this.findSegmentToFitChar(ctx, anchor, textFullWidth, fullPathWidth, spacesNumber, offset, dy, char, i);\n      offset = nextOffset;\n\n      if (!segment.p0 || !segment.p1) {\n        return;\n      } // const width = this.getLineLength(\n      // \tsegment.p0.x,\n      // \tsegment.p0.y,\n      // \tsegment.p1.x,\n      // \tsegment.p1.y\n      // );\n      // Note: Since glyphs are rendered one at a time, any kerning pair data built into the font will not be used.\n      // Can foresee having a rough pair table built in that the developer can override as needed.\n      // Or use \"dx\" attribute of the <text> node as a naive replacement\n      // const kern = 0;\n      // placeholder for future implementation\n      // const midpoint = this.getPointOnLine(\n      // \tkern + width / 2.0,\n      // \tsegment.p0.x, segment.p0.y, segment.p1.x, segment.p1.y\n      // );\n\n\n      this.glyphInfo.push({\n        // transposeX: midpoint.x,\n        // transposeY: midpoint.y,\n        text: chars[i],\n        p0: segment.p0,\n        p1: segment.p1,\n        rotation\n      });\n    });\n  }\n\n  parsePathData(path) {\n    this.pathLength = -1; // reset path length\n\n    if (!path) {\n      return [];\n    }\n\n    var pathCommands = [];\n    var {\n      pathParser\n    } = path;\n    pathParser.reset(); // convert l, H, h, V, and v to L\n\n    while (!pathParser.isEnd()) {\n      var {\n        current\n      } = pathParser;\n      var startX = current ? current.x : 0;\n      var startY = current ? current.y : 0;\n      var command = pathParser.next();\n      var nextCommandType = command.type;\n      var points = [];\n\n      switch (command.type) {\n        case PathParser.MOVE_TO:\n          this.pathM(pathParser, points);\n          break;\n\n        case PathParser.LINE_TO:\n          nextCommandType = this.pathL(pathParser, points);\n          break;\n\n        case PathParser.HORIZ_LINE_TO:\n          nextCommandType = this.pathH(pathParser, points);\n          break;\n\n        case PathParser.VERT_LINE_TO:\n          nextCommandType = this.pathV(pathParser, points);\n          break;\n\n        case PathParser.CURVE_TO:\n          this.pathC(pathParser, points);\n          break;\n\n        case PathParser.SMOOTH_CURVE_TO:\n          nextCommandType = this.pathS(pathParser, points);\n          break;\n\n        case PathParser.QUAD_TO:\n          this.pathQ(pathParser, points);\n          break;\n\n        case PathParser.SMOOTH_QUAD_TO:\n          nextCommandType = this.pathT(pathParser, points);\n          break;\n\n        case PathParser.ARC:\n          points = this.pathA(pathParser);\n          break;\n\n        case PathParser.CLOSE_PATH:\n          PathElement.pathZ(pathParser);\n          break;\n      }\n\n      if (command.type !== PathParser.CLOSE_PATH) {\n        pathCommands.push({\n          type: nextCommandType,\n          points,\n          start: {\n            x: startX,\n            y: startY\n          },\n          pathLength: this.calcLength(startX, startY, nextCommandType, points)\n        });\n      } else {\n        pathCommands.push({\n          type: PathParser.CLOSE_PATH,\n          points: [],\n          pathLength: 0\n        });\n      }\n    }\n\n    return pathCommands;\n  }\n\n  pathM(pathParser, points) {\n    var {\n      x,\n      y\n    } = PathElement.pathM(pathParser).point;\n    points.push(x, y);\n  }\n\n  pathL(pathParser, points) {\n    var {\n      x,\n      y\n    } = PathElement.pathL(pathParser).point;\n    points.push(x, y);\n    return PathParser.LINE_TO;\n  }\n\n  pathH(pathParser, points) {\n    var {\n      x,\n      y\n    } = PathElement.pathH(pathParser).point;\n    points.push(x, y);\n    return PathParser.LINE_TO;\n  }\n\n  pathV(pathParser, points) {\n    var {\n      x,\n      y\n    } = PathElement.pathV(pathParser).point;\n    points.push(x, y);\n    return PathParser.LINE_TO;\n  }\n\n  pathC(pathParser, points) {\n    var {\n      point,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathC(pathParser);\n    points.push(point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n  }\n\n  pathS(pathParser, points) {\n    var {\n      point,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathS(pathParser);\n    points.push(point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    return PathParser.CURVE_TO;\n  }\n\n  pathQ(pathParser, points) {\n    var {\n      controlPoint,\n      currentPoint\n    } = PathElement.pathQ(pathParser);\n    points.push(controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n  }\n\n  pathT(pathParser, points) {\n    var {\n      controlPoint,\n      currentPoint\n    } = PathElement.pathT(pathParser);\n    points.push(controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    return PathParser.QUAD_TO;\n  }\n\n  pathA(pathParser) {\n    var {\n      rX,\n      rY,\n      sweepFlag,\n      xAxisRotation,\n      centp,\n      a1,\n      ad\n    } = PathElement.pathA(pathParser);\n\n    if (sweepFlag === 0 && ad > 0) {\n      ad -= 2 * Math.PI;\n    }\n\n    if (sweepFlag === 1 && ad < 0) {\n      ad += 2 * Math.PI;\n    }\n\n    return [centp.x, centp.y, rX, rY, a1, ad, xAxisRotation, sweepFlag];\n  }\n\n  calcLength(x, y, commandType, points) {\n    var len = 0;\n    var p1 = null;\n    var p2 = null;\n    var t = 0;\n\n    switch (commandType) {\n      case PathParser.LINE_TO:\n        return this.getLineLength(x, y, points[0], points[1]);\n\n      case PathParser.CURVE_TO:\n        // Approximates by breaking curve into 100 line segments\n        len = 0.0;\n        p1 = this.getPointOnCubicBezier(0, x, y, points[0], points[1], points[2], points[3], points[4], points[5]);\n\n        for (t = 0.01; t <= 1; t += 0.01) {\n          p2 = this.getPointOnCubicBezier(t, x, y, points[0], points[1], points[2], points[3], points[4], points[5]);\n          len += this.getLineLength(p1.x, p1.y, p2.x, p2.y);\n          p1 = p2;\n        }\n\n        return len;\n\n      case PathParser.QUAD_TO:\n        // Approximates by breaking curve into 100 line segments\n        len = 0.0;\n        p1 = this.getPointOnQuadraticBezier(0, x, y, points[0], points[1], points[2], points[3]);\n\n        for (t = 0.01; t <= 1; t += 0.01) {\n          p2 = this.getPointOnQuadraticBezier(t, x, y, points[0], points[1], points[2], points[3]);\n          len += this.getLineLength(p1.x, p1.y, p2.x, p2.y);\n          p1 = p2;\n        }\n\n        return len;\n\n      case PathParser.ARC:\n        {\n          // Approximates by breaking curve into line segments\n          len = 0.0;\n          var start = points[4]; // 4 = theta\n\n          var dTheta = points[5]; // 5 = dTheta\n\n          var end = points[4] + dTheta;\n          var inc = Math.PI / 180.0; // 1 degree resolution\n\n          if (Math.abs(start - end) < inc) {\n            inc = Math.abs(start - end);\n          } // Note: for purpose of calculating arc length, not going to worry about rotating X-axis by angle psi\n\n\n          p1 = this.getPointOnEllipticalArc(points[0], points[1], points[2], points[3], start, 0);\n\n          if (dTheta < 0) {\n            // clockwise\n            for (t = start - inc; t > end; t -= inc) {\n              p2 = this.getPointOnEllipticalArc(points[0], points[1], points[2], points[3], t, 0);\n              len += this.getLineLength(p1.x, p1.y, p2.x, p2.y);\n              p1 = p2;\n            }\n          } else {\n            // counter-clockwise\n            for (t = start + inc; t < end; t += inc) {\n              p2 = this.getPointOnEllipticalArc(points[0], points[1], points[2], points[3], t, 0);\n              len += this.getLineLength(p1.x, p1.y, p2.x, p2.y);\n              p1 = p2;\n            }\n          }\n\n          p2 = this.getPointOnEllipticalArc(points[0], points[1], points[2], points[3], end, 0);\n          len += this.getLineLength(p1.x, p1.y, p2.x, p2.y);\n          return len;\n        }\n    }\n\n    return 0;\n  }\n\n  getPointOnLine(dist, p1x, p1y, p2x, p2y) {\n    var fromX = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : p1x;\n    var fromY = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : p1y;\n    var m = (p2y - p1y) / (p2x - p1x + PSEUDO_ZERO);\n    var run = Math.sqrt(dist * dist / (1 + m * m));\n\n    if (p2x < p1x) {\n      run *= -1;\n    }\n\n    var rise = m * run;\n    var pt = null;\n\n    if (p2x === p1x) {\n      // vertical line\n      pt = {\n        x: fromX,\n        y: fromY + rise\n      };\n    } else if ((fromY - p1y) / (fromX - p1x + PSEUDO_ZERO) === m) {\n      pt = {\n        x: fromX + run,\n        y: fromY + rise\n      };\n    } else {\n      var ix = 0;\n      var iy = 0;\n      var len = this.getLineLength(p1x, p1y, p2x, p2y);\n\n      if (len < PSEUDO_ZERO) {\n        return null;\n      }\n\n      var u = (fromX - p1x) * (p2x - p1x) + (fromY - p1y) * (p2y - p1y);\n      u /= len * len;\n      ix = p1x + u * (p2x - p1x);\n      iy = p1y + u * (p2y - p1y);\n      var pRise = this.getLineLength(fromX, fromY, ix, iy);\n      var pRun = Math.sqrt(dist * dist - pRise * pRise);\n      run = Math.sqrt(pRun * pRun / (1 + m * m));\n\n      if (p2x < p1x) {\n        run *= -1;\n      }\n\n      rise = m * run;\n      pt = {\n        x: ix + run,\n        y: iy + rise\n      };\n    }\n\n    return pt;\n  }\n\n  getPointOnPath(distance) {\n    var fullLen = this.getPathLength();\n    var cumulativePathLength = 0;\n    var p = null;\n\n    if (distance < -0.00005 || distance - 0.00005 > fullLen) {\n      return null;\n    }\n\n    var {\n      dataArray\n    } = this;\n\n    for (var command of dataArray) {\n      if (command && (command.pathLength < 0.00005 || cumulativePathLength + command.pathLength + 0.00005 < distance)) {\n        cumulativePathLength += command.pathLength;\n        continue;\n      }\n\n      var delta = distance - cumulativePathLength;\n      var currentT = 0;\n\n      switch (command.type) {\n        case PathParser.LINE_TO:\n          p = this.getPointOnLine(delta, command.start.x, command.start.y, command.points[0], command.points[1], command.start.x, command.start.y);\n          break;\n\n        case PathParser.ARC:\n          {\n            var start = command.points[4]; // 4 = theta\n\n            var dTheta = command.points[5]; // 5 = dTheta\n\n            var end = command.points[4] + dTheta;\n            currentT = start + delta / command.pathLength * dTheta;\n\n            if (dTheta < 0 && currentT < end || dTheta >= 0 && currentT > end) {\n              break;\n            }\n\n            p = this.getPointOnEllipticalArc(command.points[0], command.points[1], command.points[2], command.points[3], currentT, command.points[6]);\n            break;\n          }\n\n        case PathParser.CURVE_TO:\n          currentT = delta / command.pathLength;\n\n          if (currentT > 1) {\n            currentT = 1;\n          }\n\n          p = this.getPointOnCubicBezier(currentT, command.start.x, command.start.y, command.points[0], command.points[1], command.points[2], command.points[3], command.points[4], command.points[5]);\n          break;\n\n        case PathParser.QUAD_TO:\n          currentT = delta / command.pathLength;\n\n          if (currentT > 1) {\n            currentT = 1;\n          }\n\n          p = this.getPointOnQuadraticBezier(currentT, command.start.x, command.start.y, command.points[0], command.points[1], command.points[2], command.points[3]);\n          break;\n      }\n\n      if (p) {\n        return p;\n      }\n\n      break;\n    }\n\n    return null;\n  }\n\n  getLineLength(x1, y1, x2, y2) {\n    return Math.sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1));\n  }\n\n  getPathLength() {\n    if (this.pathLength === -1) {\n      this.pathLength = this.dataArray.reduce((length, command) => command.pathLength > 0 ? length + command.pathLength : length, 0);\n    }\n\n    return this.pathLength;\n  }\n\n  getPointOnCubicBezier(pct, p1x, p1y, p2x, p2y, p3x, p3y, p4x, p4y) {\n    var x = p4x * CB1(pct) + p3x * CB2(pct) + p2x * CB3(pct) + p1x * CB4(pct);\n    var y = p4y * CB1(pct) + p3y * CB2(pct) + p2y * CB3(pct) + p1y * CB4(pct);\n    return {\n      x,\n      y\n    };\n  }\n\n  getPointOnQuadraticBezier(pct, p1x, p1y, p2x, p2y, p3x, p3y) {\n    var x = p3x * QB1(pct) + p2x * QB2(pct) + p1x * QB3(pct);\n    var y = p3y * QB1(pct) + p2y * QB2(pct) + p1y * QB3(pct);\n    return {\n      x,\n      y\n    };\n  }\n\n  getPointOnEllipticalArc(cx, cy, rx, ry, theta, psi) {\n    var cosPsi = Math.cos(psi);\n    var sinPsi = Math.sin(psi);\n    var pt = {\n      x: rx * Math.cos(theta),\n      y: ry * Math.sin(theta)\n    };\n    return {\n      x: cx + (pt.x * cosPsi - pt.y * sinPsi),\n      y: cy + (pt.x * sinPsi + pt.y * cosPsi)\n    };\n  } // TODO need some optimisations. possibly build cache only for curved segments?\n\n\n  buildEquidistantCache(inputStep, inputPrecision) {\n    var fullLen = this.getPathLength();\n    var precision = inputPrecision || 0.25; // accuracy vs performance\n\n    var step = inputStep || fullLen / 100;\n\n    if (!this.equidistantCache || this.equidistantCache.step !== step || this.equidistantCache.precision !== precision) {\n      // Prepare cache\n      this.equidistantCache = {\n        step,\n        precision,\n        points: []\n      }; // Calculate points\n\n      var s = 0;\n\n      for (var l = 0; l <= fullLen; l += precision) {\n        var p0 = this.getPointOnPath(l);\n        var p1 = this.getPointOnPath(l + precision);\n\n        if (!p0 || !p1) {\n          continue;\n        }\n\n        s += this.getLineLength(p0.x, p0.y, p1.x, p1.y);\n\n        if (s >= step) {\n          this.equidistantCache.points.push({\n            x: p0.x,\n            y: p0.y,\n            distance: l\n          });\n          s -= step;\n        }\n      }\n    }\n  }\n\n  getEquidistantPointOnPath(targetDistance, step, precision) {\n    this.buildEquidistantCache(step, precision);\n\n    if (targetDistance < 0 || targetDistance - this.getPathLength() > 0.00005) {\n      return null;\n    }\n\n    var idx = Math.round(targetDistance / this.getPathLength() * (this.equidistantCache.points.length - 1));\n    return this.equidistantCache.points[idx] || null;\n  }\n\n}\n\nvar dataUriRegex = /^\\s*data:(([^/,;]+\\/[^/,;]+)(?:;([^,;=]+=[^,;=]+))?)?(?:;(base64))?,(.*)$/i;\nclass ImageElement extends RenderedElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'image';\n    this.loaded = false;\n    var href = this.getHrefAttribute().getString();\n\n    if (!href) {\n      return;\n    }\n\n    var isSvg = href.endsWith('.svg') || /^\\s*data:image\\/svg\\+xml/i.test(href);\n    document.images.push(this);\n\n    if (!isSvg) {\n      void this.loadImage(href);\n    } else {\n      void this.loadSvg(href);\n    }\n\n    this.isSvg = isSvg;\n  }\n\n  loadImage(href) {\n    var _this = this;\n\n    return _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__(function* () {\n      try {\n        var image = yield _this.document.createImage(href);\n        _this.image = image;\n      } catch (err) {\n        console.error(\"Error while loading image \\\"\".concat(href, \"\\\":\"), err);\n      }\n\n      _this.loaded = true;\n    })();\n  }\n\n  loadSvg(href) {\n    var _this2 = this;\n\n    return _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__(function* () {\n      var match = dataUriRegex.exec(href);\n\n      if (match) {\n        var data = match[5];\n\n        if (match[4] === 'base64') {\n          _this2.image = atob(data);\n        } else {\n          _this2.image = decodeURIComponent(data);\n        }\n      } else {\n        try {\n          var response = yield _this2.document.fetch(href);\n          var svg = yield response.text();\n          _this2.image = svg;\n        } catch (err) {\n          console.error(\"Error while loading image \\\"\".concat(href, \"\\\":\"), err);\n        }\n      }\n\n      _this2.loaded = true;\n    })();\n  }\n\n  renderChildren(ctx) {\n    var {\n      document,\n      image,\n      loaded\n    } = this;\n    var x = this.getAttribute('x').getPixels('x');\n    var y = this.getAttribute('y').getPixels('y');\n    var width = this.getStyle('width').getPixels('x');\n    var height = this.getStyle('height').getPixels('y');\n\n    if (!loaded || !image || !width || !height) {\n      return;\n    }\n\n    ctx.save();\n    ctx.translate(x, y);\n\n    if (this.isSvg) {\n      var subDocument = document.canvg.forkString(ctx, this.image, {\n        ignoreMouse: true,\n        ignoreAnimation: true,\n        ignoreDimensions: true,\n        ignoreClear: true,\n        offsetX: 0,\n        offsetY: 0,\n        scaleWidth: width,\n        scaleHeight: height\n      });\n      subDocument.document.documentElement.parent = this;\n      void subDocument.render();\n    } else {\n      var _image = this.image;\n      document.setViewBox({\n        ctx,\n        aspectRatio: this.getAttribute('preserveAspectRatio').getString(),\n        width,\n        desiredWidth: _image.width,\n        height,\n        desiredHeight: _image.height\n      });\n\n      if (this.loaded) {\n        if (typeof _image.complete === 'undefined' || _image.complete) {\n          ctx.drawImage(_image, 0, 0);\n        }\n      }\n    }\n\n    ctx.restore();\n  }\n\n  getBoundingBox() {\n    var x = this.getAttribute('x').getPixels('x');\n    var y = this.getAttribute('y').getPixels('y');\n    var width = this.getStyle('width').getPixels('x');\n    var height = this.getStyle('height').getPixels('y');\n    return new BoundingBox(x, y, x + width, y + height);\n  }\n\n}\n\nclass SymbolElement extends RenderedElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'symbol';\n  }\n\n  render(_) {// NO RENDER\n  }\n\n}\n\nclass SVGFontLoader {\n  constructor(document) {\n    this.document = document;\n    this.loaded = false;\n    document.fonts.push(this);\n  }\n\n  load(fontFamily, url) {\n    var _this = this;\n\n    return _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__(function* () {\n      try {\n        var {\n          document\n        } = _this;\n        var svgDocument = yield document.canvg.parser.load(url);\n        var fonts = svgDocument.getElementsByTagName('font');\n        Array.from(fonts).forEach(fontNode => {\n          var font = document.createElement(fontNode);\n          document.definitions[fontFamily] = font;\n        });\n      } catch (err) {\n        console.error(\"Error while loading font \\\"\".concat(url, \"\\\":\"), err);\n      }\n\n      _this.loaded = true;\n    })();\n  }\n\n}\n\nclass StyleElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'style';\n    var css = compressSpaces(Array.from(node.childNodes) // NEED TEST\n    .map(_ => _.textContent).join('').replace(/(\\/\\*([^*]|[\\r\\n]|(\\*+([^*/]|[\\r\\n])))*\\*+\\/)|(^[\\s]*\\/\\/.*)/gm, '') // remove comments\n    .replace(/@import.*;/g, '') // remove imports\n    );\n    var cssDefs = css.split('}');\n    cssDefs.forEach(_ => {\n      var def = _.trim();\n\n      if (!def) {\n        return;\n      }\n\n      var cssParts = def.split('{');\n      var cssClasses = cssParts[0].split(',');\n      var cssProps = cssParts[1].split(';');\n      cssClasses.forEach(_ => {\n        var cssClass = _.trim();\n\n        if (!cssClass) {\n          return;\n        }\n\n        var props = document.styles[cssClass] || {};\n        cssProps.forEach(cssProp => {\n          var prop = cssProp.indexOf(':');\n          var name = cssProp.substr(0, prop).trim();\n          var value = cssProp.substr(prop + 1, cssProp.length - prop).trim();\n\n          if (name && value) {\n            props[name] = new Property(document, name, value);\n          }\n        });\n        document.styles[cssClass] = props;\n        document.stylesSpecificity[cssClass] = getSelectorSpecificity(cssClass);\n\n        if (cssClass === '@font-face') {\n          //  && !nodeEnv\n          var fontFamily = props['font-family'].getString().replace(/\"|'/g, '');\n          var srcs = props.src.getString().split(',');\n          srcs.forEach(src => {\n            if (src.indexOf('format(\"svg\")') > 0) {\n              var url = parseExternalUrl(src);\n\n              if (url) {\n                void new SVGFontLoader(document).load(fontFamily, url);\n              }\n            }\n          });\n        }\n      });\n    });\n  }\n\n}\nStyleElement.parseExternalUrl = parseExternalUrl;\n\nclass UseElement extends RenderedElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'use';\n  }\n\n  setContext(ctx) {\n    super.setContext(ctx);\n    var xAttr = this.getAttribute('x');\n    var yAttr = this.getAttribute('y');\n\n    if (xAttr.hasValue()) {\n      ctx.translate(xAttr.getPixels('x'), 0);\n    }\n\n    if (yAttr.hasValue()) {\n      ctx.translate(0, yAttr.getPixels('y'));\n    }\n  }\n\n  path(ctx) {\n    var {\n      element\n    } = this;\n\n    if (element) {\n      element.path(ctx);\n    }\n  }\n\n  renderChildren(ctx) {\n    var {\n      document,\n      element\n    } = this;\n\n    if (element) {\n      var tempSvg = element;\n\n      if (element.type === 'symbol') {\n        // render me using a temporary svg element in symbol cases (http://www.w3.org/TR/SVG/struct.html#UseElement)\n        tempSvg = new SVGElement(document, null);\n        tempSvg.attributes.viewBox = new Property(document, 'viewBox', element.getAttribute('viewBox').getString());\n        tempSvg.attributes.preserveAspectRatio = new Property(document, 'preserveAspectRatio', element.getAttribute('preserveAspectRatio').getString());\n        tempSvg.attributes.overflow = new Property(document, 'overflow', element.getAttribute('overflow').getString());\n        tempSvg.children = element.children; // element is still the parent of the children\n\n        element.styles.opacity = new Property(document, 'opacity', this.calculateOpacity());\n      }\n\n      if (tempSvg.type === 'svg') {\n        var widthStyle = this.getStyle('width', false, true);\n        var heightStyle = this.getStyle('height', false, true); // if symbol or svg, inherit width/height from me\n\n        if (widthStyle.hasValue()) {\n          tempSvg.attributes.width = new Property(document, 'width', widthStyle.getString());\n        }\n\n        if (heightStyle.hasValue()) {\n          tempSvg.attributes.height = new Property(document, 'height', heightStyle.getString());\n        }\n      }\n\n      var oldParent = tempSvg.parent;\n      tempSvg.parent = this;\n      tempSvg.render(ctx);\n      tempSvg.parent = oldParent;\n    }\n  }\n\n  getBoundingBox(ctx) {\n    var {\n      element\n    } = this;\n\n    if (element) {\n      return element.getBoundingBox(ctx);\n    }\n\n    return null;\n  }\n\n  elementTransform() {\n    var {\n      document,\n      element\n    } = this;\n    return Transform.fromElement(document, element);\n  }\n\n  get element() {\n    if (!this.cachedElement) {\n      this.cachedElement = this.getHrefAttribute().getDefinition();\n    }\n\n    return this.cachedElement;\n  }\n\n}\n\nfunction imGet(img, x, y, width, _height, rgba) {\n  return img[y * width * 4 + x * 4 + rgba];\n}\n\nfunction imSet(img, x, y, width, _height, rgba, val) {\n  img[y * width * 4 + x * 4 + rgba] = val;\n}\n\nfunction m(matrix, i, v) {\n  var mi = matrix[i];\n  return mi * v;\n}\n\nfunction c(a, m1, m2, m3) {\n  return m1 + Math.cos(a) * m2 + Math.sin(a) * m3;\n}\n\nclass FeColorMatrixElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'feColorMatrix';\n    var matrix = toNumbers(this.getAttribute('values').getString());\n\n    switch (this.getAttribute('type').getString('matrix')) {\n      // http://www.w3.org/TR/SVG/filters.html#feColorMatrixElement\n      case 'saturate':\n        {\n          var s = matrix[0];\n          /* eslint-disable array-element-newline */\n\n          matrix = [0.213 + 0.787 * s, 0.715 - 0.715 * s, 0.072 - 0.072 * s, 0, 0, 0.213 - 0.213 * s, 0.715 + 0.285 * s, 0.072 - 0.072 * s, 0, 0, 0.213 - 0.213 * s, 0.715 - 0.715 * s, 0.072 + 0.928 * s, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1];\n          /* eslint-enable array-element-newline */\n\n          break;\n        }\n\n      case 'hueRotate':\n        {\n          var a = matrix[0] * Math.PI / 180.0;\n          /* eslint-disable array-element-newline */\n\n          matrix = [c(a, 0.213, 0.787, -0.213), c(a, 0.715, -0.715, -0.715), c(a, 0.072, -0.072, 0.928), 0, 0, c(a, 0.213, -0.213, 0.143), c(a, 0.715, 0.285, 0.140), c(a, 0.072, -0.072, -0.283), 0, 0, c(a, 0.213, -0.213, -0.787), c(a, 0.715, -0.715, 0.715), c(a, 0.072, 0.928, 0.072), 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1];\n          /* eslint-enable array-element-newline */\n\n          break;\n        }\n\n      case 'luminanceToAlpha':\n        /* eslint-disable array-element-newline */\n        matrix = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.2125, 0.7154, 0.0721, 0, 0, 0, 0, 0, 0, 1];\n        /* eslint-enable array-element-newline */\n\n        break;\n    }\n\n    this.matrix = matrix;\n    this.includeOpacity = this.getAttribute('includeOpacity').hasValue();\n  }\n\n  apply(ctx, _x, _y, width, height) {\n    // assuming x==0 && y==0 for now\n    var {\n      includeOpacity,\n      matrix\n    } = this;\n    var srcData = ctx.getImageData(0, 0, width, height);\n\n    for (var y = 0; y < height; y++) {\n      for (var x = 0; x < width; x++) {\n        var r = imGet(srcData.data, x, y, width, height, 0);\n        var g = imGet(srcData.data, x, y, width, height, 1);\n        var b = imGet(srcData.data, x, y, width, height, 2);\n        var a = imGet(srcData.data, x, y, width, height, 3);\n        var nr = m(matrix, 0, r) + m(matrix, 1, g) + m(matrix, 2, b) + m(matrix, 3, a) + m(matrix, 4, 1);\n        var ng = m(matrix, 5, r) + m(matrix, 6, g) + m(matrix, 7, b) + m(matrix, 8, a) + m(matrix, 9, 1);\n        var nb = m(matrix, 10, r) + m(matrix, 11, g) + m(matrix, 12, b) + m(matrix, 13, a) + m(matrix, 14, 1);\n        var na = m(matrix, 15, r) + m(matrix, 16, g) + m(matrix, 17, b) + m(matrix, 18, a) + m(matrix, 19, 1);\n\n        if (includeOpacity) {\n          nr = 0;\n          ng = 0;\n          nb = 0;\n          na *= a / 255;\n        }\n\n        imSet(srcData.data, x, y, width, height, 0, nr);\n        imSet(srcData.data, x, y, width, height, 1, ng);\n        imSet(srcData.data, x, y, width, height, 2, nb);\n        imSet(srcData.data, x, y, width, height, 3, na);\n      }\n    }\n\n    ctx.clearRect(0, 0, width, height);\n    ctx.putImageData(srcData, 0, 0);\n  }\n\n}\n\nclass MaskElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'mask';\n  }\n\n  apply(ctx, element) {\n    var {\n      document\n    } = this; // render as temp svg\n\n    var x = this.getAttribute('x').getPixels('x');\n    var y = this.getAttribute('y').getPixels('y');\n    var width = this.getStyle('width').getPixels('x');\n    var height = this.getStyle('height').getPixels('y');\n\n    if (!width && !height) {\n      var boundingBox = new BoundingBox();\n      this.children.forEach(child => {\n        boundingBox.addBoundingBox(child.getBoundingBox(ctx));\n      });\n      x = Math.floor(boundingBox.x1);\n      y = Math.floor(boundingBox.y1);\n      width = Math.floor(boundingBox.width);\n      height = Math.floor(boundingBox.height);\n    }\n\n    var ignoredStyles = this.removeStyles(element, MaskElement.ignoreStyles);\n    var maskCanvas = document.createCanvas(x + width, y + height);\n    var maskCtx = maskCanvas.getContext('2d');\n    document.screen.setDefaults(maskCtx);\n    this.renderChildren(maskCtx); // convert mask to alpha with a fake node\n    // TODO: refactor out apply from feColorMatrix\n\n    new FeColorMatrixElement(document, {\n      nodeType: 1,\n      childNodes: [],\n      attributes: [{\n        nodeName: 'type',\n        value: 'luminanceToAlpha'\n      }, {\n        nodeName: 'includeOpacity',\n        value: 'true'\n      }]\n    }).apply(maskCtx, 0, 0, x + width, y + height);\n    var tmpCanvas = document.createCanvas(x + width, y + height);\n    var tmpCtx = tmpCanvas.getContext('2d');\n    document.screen.setDefaults(tmpCtx);\n    element.render(tmpCtx);\n    tmpCtx.globalCompositeOperation = 'destination-in';\n    tmpCtx.fillStyle = maskCtx.createPattern(maskCanvas, 'no-repeat');\n    tmpCtx.fillRect(0, 0, x + width, y + height);\n    ctx.fillStyle = tmpCtx.createPattern(tmpCanvas, 'no-repeat');\n    ctx.fillRect(0, 0, x + width, y + height); // reassign mask\n\n    this.restoreStyles(element, ignoredStyles);\n  }\n\n  render(_) {// NO RENDER\n  }\n\n}\nMaskElement.ignoreStyles = ['mask', 'transform', 'clip-path'];\n\nvar noop = () => {// NOOP\n};\n\nclass ClipPathElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'clipPath';\n  }\n\n  apply(ctx) {\n    var {\n      document\n    } = this;\n    var contextProto = Reflect.getPrototypeOf(ctx);\n    var {\n      beginPath,\n      closePath\n    } = ctx;\n\n    if (contextProto) {\n      contextProto.beginPath = noop;\n      contextProto.closePath = noop;\n    }\n\n    Reflect.apply(beginPath, ctx, []);\n    this.children.forEach(child => {\n      if (typeof child.path === 'undefined') {\n        return;\n      }\n\n      var transform = typeof child.elementTransform !== 'undefined' ? child.elementTransform() : null; // handle <use />\n\n      if (!transform) {\n        transform = Transform.fromElement(document, child);\n      }\n\n      if (transform) {\n        transform.apply(ctx);\n      }\n\n      child.path(ctx);\n\n      if (contextProto) {\n        contextProto.closePath = closePath;\n      }\n\n      if (transform) {\n        transform.unapply(ctx);\n      }\n    });\n    Reflect.apply(closePath, ctx, []);\n    ctx.clip();\n\n    if (contextProto) {\n      contextProto.beginPath = beginPath;\n      contextProto.closePath = closePath;\n    }\n  }\n\n  render(_) {// NO RENDER\n  }\n\n}\n\nclass FilterElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'filter';\n  }\n\n  apply(ctx, element) {\n    // render as temp svg\n    var {\n      document,\n      children\n    } = this;\n    var boundingBox = element.getBoundingBox(ctx);\n\n    if (!boundingBox) {\n      return;\n    }\n\n    var px = 0;\n    var py = 0;\n    children.forEach(child => {\n      var efd = child.extraFilterDistance || 0;\n      px = Math.max(px, efd);\n      py = Math.max(py, efd);\n    });\n    var width = Math.floor(boundingBox.width);\n    var height = Math.floor(boundingBox.height);\n    var tmpCanvasWidth = width + 2 * px;\n    var tmpCanvasHeight = height + 2 * py;\n\n    if (tmpCanvasWidth < 1 || tmpCanvasHeight < 1) {\n      return;\n    }\n\n    var x = Math.floor(boundingBox.x);\n    var y = Math.floor(boundingBox.y);\n    var ignoredStyles = this.removeStyles(element, FilterElement.ignoreStyles);\n    var tmpCanvas = document.createCanvas(tmpCanvasWidth, tmpCanvasHeight);\n    var tmpCtx = tmpCanvas.getContext('2d');\n    document.screen.setDefaults(tmpCtx);\n    tmpCtx.translate(-x + px, -y + py);\n    element.render(tmpCtx); // apply filters\n\n    children.forEach(child => {\n      if (typeof child.apply === 'function') {\n        child.apply(tmpCtx, 0, 0, tmpCanvasWidth, tmpCanvasHeight);\n      }\n    }); // render on me\n\n    ctx.drawImage(tmpCanvas, 0, 0, tmpCanvasWidth, tmpCanvasHeight, x - px, y - py, tmpCanvasWidth, tmpCanvasHeight);\n    this.restoreStyles(element, ignoredStyles);\n  }\n\n  render(_) {// NO RENDER\n  }\n\n}\nFilterElement.ignoreStyles = ['filter', 'transform', 'clip-path'];\n\nclass FeDropShadowElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'feDropShadow';\n    this.addStylesFromStyleDefinition();\n  }\n\n  apply(_, _x, _y, _width, _height) {// TODO: implement\n  }\n\n}\n\nclass FeMorphologyElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'feMorphology';\n  }\n\n  apply(_, _x, _y, _width, _height) {// TODO: implement\n  }\n\n}\n\nclass FeCompositeElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'feComposite';\n  }\n\n  apply(_, _x, _y, _width, _height) {// TODO: implement\n  }\n\n}\n\nclass FeGaussianBlurElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'feGaussianBlur';\n    this.blurRadius = Math.floor(this.getAttribute('stdDeviation').getNumber());\n    this.extraFilterDistance = this.blurRadius;\n  }\n\n  apply(ctx, x, y, width, height) {\n    var {\n      document,\n      blurRadius\n    } = this;\n    var body = document.window ? document.window.document.body : null;\n    var canvas = ctx.canvas; // StackBlur requires canvas be on document\n\n    canvas.id = document.getUniqueId();\n\n    if (body) {\n      canvas.style.display = 'none';\n      body.appendChild(canvas);\n    }\n\n    (0,stackblur_canvas__WEBPACK_IMPORTED_MODULE_19__.canvasRGBA)(canvas, x, y, width, height, blurRadius);\n\n    if (body) {\n      body.removeChild(canvas);\n    }\n  }\n\n}\n\nclass TitleElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'title';\n  }\n\n}\n\nclass DescElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'desc';\n  }\n\n}\n\nvar elements = {\n  'svg': SVGElement,\n  'rect': RectElement,\n  'circle': CircleElement,\n  'ellipse': EllipseElement,\n  'line': LineElement,\n  'polyline': PolylineElement,\n  'polygon': PolygonElement,\n  'path': PathElement,\n  'pattern': PatternElement,\n  'marker': MarkerElement,\n  'defs': DefsElement,\n  'linearGradient': LinearGradientElement,\n  'radialGradient': RadialGradientElement,\n  'stop': StopElement,\n  'animate': AnimateElement,\n  'animateColor': AnimateColorElement,\n  'animateTransform': AnimateTransformElement,\n  'font': FontElement,\n  'font-face': FontFaceElement,\n  'missing-glyph': MissingGlyphElement,\n  'glyph': GlyphElement,\n  'text': TextElement,\n  'tspan': TSpanElement,\n  'tref': TRefElement,\n  'a': AElement,\n  'textPath': TextPathElement,\n  'image': ImageElement,\n  'g': GElement,\n  'symbol': SymbolElement,\n  'style': StyleElement,\n  'use': UseElement,\n  'mask': MaskElement,\n  'clipPath': ClipPathElement,\n  'filter': FilterElement,\n  'feDropShadow': FeDropShadowElement,\n  'feMorphology': FeMorphologyElement,\n  'feComposite': FeCompositeElement,\n  'feColorMatrix': FeColorMatrixElement,\n  'feGaussianBlur': FeGaussianBlurElement,\n  'title': TitleElement,\n  'desc': DescElement\n};\n\nfunction ownKeys$1(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread$1(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys$1(Object(source), true).forEach(function (key) { _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_7__(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys$1(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction createCanvas(width, height) {\n  var canvas = document.createElement('canvas');\n  canvas.width = width;\n  canvas.height = height;\n  return canvas;\n}\n\nfunction createImage(_x) {\n  return _createImage.apply(this, arguments);\n}\n\nfunction _createImage() {\n  _createImage = _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__(function* (src) {\n    var anonymousCrossOrigin = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var image = document.createElement('img');\n\n    if (anonymousCrossOrigin) {\n      image.crossOrigin = 'Anonymous';\n    }\n\n    return new Promise((resolve, reject) => {\n      image.onload = () => {\n        resolve(image);\n      };\n\n      image.onerror = (_event, _source, _lineno, _colno, error) => {\n        reject(error);\n      };\n\n      image.src = src;\n    });\n  });\n  return _createImage.apply(this, arguments);\n}\n\nclass Document {\n  constructor(canvg) {\n    var {\n      rootEmSize = 12,\n      emSize = 12,\n      createCanvas = Document.createCanvas,\n      createImage = Document.createImage,\n      anonymousCrossOrigin\n    } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.canvg = canvg;\n    this.definitions = Object.create(null);\n    this.styles = Object.create(null);\n    this.stylesSpecificity = Object.create(null);\n    this.images = [];\n    this.fonts = [];\n    this.emSizeStack = [];\n    this.uniqueId = 0;\n    this.screen = canvg.screen;\n    this.rootEmSize = rootEmSize;\n    this.emSize = emSize;\n    this.createCanvas = createCanvas;\n    this.createImage = this.bindCreateImage(createImage, anonymousCrossOrigin);\n    this.screen.wait(this.isImagesLoaded.bind(this));\n    this.screen.wait(this.isFontsLoaded.bind(this));\n  }\n\n  bindCreateImage(createImage, anonymousCrossOrigin) {\n    if (typeof anonymousCrossOrigin === 'boolean') {\n      return (source, forceAnonymousCrossOrigin) => createImage(source, typeof forceAnonymousCrossOrigin === 'boolean' ? forceAnonymousCrossOrigin : anonymousCrossOrigin);\n    }\n\n    return createImage;\n  }\n\n  get window() {\n    return this.screen.window;\n  }\n\n  get fetch() {\n    return this.screen.fetch;\n  }\n\n  get ctx() {\n    return this.screen.ctx;\n  }\n\n  get emSize() {\n    var {\n      emSizeStack\n    } = this;\n    return emSizeStack[emSizeStack.length - 1];\n  }\n\n  set emSize(value) {\n    var {\n      emSizeStack\n    } = this;\n    emSizeStack.push(value);\n  }\n\n  popEmSize() {\n    var {\n      emSizeStack\n    } = this;\n    emSizeStack.pop();\n  }\n\n  getUniqueId() {\n    return \"canvg\".concat(++this.uniqueId);\n  }\n\n  isImagesLoaded() {\n    return this.images.every(_ => _.loaded);\n  }\n\n  isFontsLoaded() {\n    return this.fonts.every(_ => _.loaded);\n  }\n\n  createDocumentElement(document) {\n    var documentElement = this.createElement(document.documentElement);\n    documentElement.root = true;\n    documentElement.addStylesFromStyleDefinition();\n    this.documentElement = documentElement;\n    return documentElement;\n  }\n\n  createElement(node) {\n    var elementType = node.nodeName.replace(/^[^:]+:/, '');\n    var ElementType = Document.elementTypes[elementType];\n\n    if (typeof ElementType !== 'undefined') {\n      return new ElementType(this, node);\n    }\n\n    return new UnknownElement(this, node);\n  }\n\n  createTextNode(node) {\n    return new TextNode(this, node);\n  }\n\n  setViewBox(config) {\n    this.screen.setViewBox(_objectSpread$1({\n      document: this\n    }, config));\n  }\n\n}\nDocument.createCanvas = createCanvas;\nDocument.createImage = createImage;\nDocument.elementTypes = elements;\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_7__(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n/**\r\n * SVG renderer on canvas.\r\n */\n\nclass Canvg {\n  /**\r\n   * Main constructor.\r\n   * @param ctx - Rendering context.\r\n   * @param svg - SVG Document.\r\n   * @param options - Rendering options.\r\n   */\n  constructor(ctx, svg) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    this.parser = new Parser(options);\n    this.screen = new Screen(ctx, options);\n    this.options = options;\n    var document = new Document(this, options);\n    var documentElement = document.createDocumentElement(svg);\n    this.document = document;\n    this.documentElement = documentElement;\n  }\n  /**\r\n   * Create Canvg instance from SVG source string or URL.\r\n   * @param ctx - Rendering context.\r\n   * @param svg - SVG source string or URL.\r\n   * @param options - Rendering options.\r\n   * @returns Canvg instance.\r\n   */\n\n\n  static from(ctx, svg) {\n    var _arguments = arguments;\n    return _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__(function* () {\n      var options = _arguments.length > 2 && _arguments[2] !== undefined ? _arguments[2] : {};\n      var parser = new Parser(options);\n      var svgDocument = yield parser.parse(svg);\n      return new Canvg(ctx, svgDocument, options);\n    })();\n  }\n  /**\r\n   * Create Canvg instance from SVG source string.\r\n   * @param ctx - Rendering context.\r\n   * @param svg - SVG source string.\r\n   * @param options - Rendering options.\r\n   * @returns Canvg instance.\r\n   */\n\n\n  static fromString(ctx, svg) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    var parser = new Parser(options);\n    var svgDocument = parser.parseFromString(svg);\n    return new Canvg(ctx, svgDocument, options);\n  }\n  /**\r\n   * Create new Canvg instance with inherited options.\r\n   * @param ctx - Rendering context.\r\n   * @param svg - SVG source string or URL.\r\n   * @param options - Rendering options.\r\n   * @returns Canvg instance.\r\n   */\n\n\n  fork(ctx, svg) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    return Canvg.from(ctx, svg, _objectSpread(_objectSpread({}, this.options), options));\n  }\n  /**\r\n   * Create new Canvg instance with inherited options.\r\n   * @param ctx - Rendering context.\r\n   * @param svg - SVG source string.\r\n   * @param options - Rendering options.\r\n   * @returns Canvg instance.\r\n   */\n\n\n  forkString(ctx, svg) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    return Canvg.fromString(ctx, svg, _objectSpread(_objectSpread({}, this.options), options));\n  }\n  /**\r\n   * Document is ready promise.\r\n   * @returns Ready promise.\r\n   */\n\n\n  ready() {\n    return this.screen.ready();\n  }\n  /**\r\n   * Document is ready value.\r\n   * @returns Is ready or not.\r\n   */\n\n\n  isReady() {\n    return this.screen.isReady();\n  }\n  /**\r\n   * Render only first frame, ignoring animations and mouse.\r\n   * @param options - Rendering options.\r\n   */\n\n\n  render() {\n    var _arguments2 = arguments,\n        _this = this;\n\n    return _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__(function* () {\n      var options = _arguments2.length > 0 && _arguments2[0] !== undefined ? _arguments2[0] : {};\n\n      _this.start(_objectSpread({\n        enableRedraw: true,\n        ignoreAnimation: true,\n        ignoreMouse: true\n      }, options));\n\n      yield _this.ready();\n\n      _this.stop();\n    })();\n  }\n  /**\r\n   * Start rendering.\r\n   * @param options - Render options.\r\n   */\n\n\n  start() {\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var {\n      documentElement,\n      screen,\n      options: baseOptions\n    } = this;\n    screen.start(documentElement, _objectSpread(_objectSpread({\n      enableRedraw: true\n    }, baseOptions), options));\n  }\n  /**\r\n   * Stop rendering.\r\n   */\n\n\n  stop() {\n    this.screen.stop();\n  }\n  /**\r\n   * Resize SVG to fit in given size.\r\n   * @param width\r\n   * @param height\r\n   * @param preserveAspectRatio\r\n   */\n\n\n  resize(width) {\n    var height = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : width;\n    var preserveAspectRatio = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    this.documentElement.resize(width, height, preserveAspectRatio);\n  }\n\n}\n\n\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/canvg/lib/index.es.js\n");

/***/ })

};
;