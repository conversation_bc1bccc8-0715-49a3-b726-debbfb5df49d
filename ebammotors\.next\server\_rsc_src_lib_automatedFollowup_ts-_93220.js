"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_automatedFollowup_ts-_93220";
exports.ids = ["_rsc_src_lib_automatedFollowup_ts-_93220"];
exports.modules = {

/***/ "(rsc)/./src/lib/automatedFollowup.ts":
/*!**************************************!*\
  !*** ./src/lib/automatedFollowup.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initializeAutomatedFollowups: () => (/* binding */ initializeAutomatedFollowups),\n/* harmony export */   processAutomatedFollowups: () => (/* binding */ processAutomatedFollowups),\n/* harmony export */   scheduleAutoFollowupForCustomer: () => (/* binding */ scheduleAutoFollowupForCustomer),\n/* harmony export */   scheduleAutoFollowupForLead: () => (/* binding */ scheduleAutoFollowupForLead),\n/* harmony export */   triggerFollowupProcessing: () => (/* binding */ triggerFollowupProcessing)\n/* harmony export */ });\n/* harmony import */ var _crmStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./crmStorage */ \"(rsc)/./src/lib/crmStorage.ts\");\n/* harmony import */ var _resendService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./resendService */ \"(rsc)/./src/lib/resendService.ts\");\n\n\nconst DEFAULT_CONFIG = {\n    delayDays: 3,\n    enableEmail: true,\n    enableSMS: true,\n    emailTemplate: 'customer_followup',\n    smsTemplate: 'customer_followup_sms'\n};\n/**\n * Schedule automatic follow-up for new customer\n */ async function scheduleAutoFollowupForCustomer(customerId, customerData) {\n    try {\n        const followupDate = new Date();\n        followupDate.setDate(followupDate.getDate() + DEFAULT_CONFIG.delayDays);\n        // Schedule email follow-up\n        if (DEFAULT_CONFIG.enableEmail) {\n            await (0,_crmStorage__WEBPACK_IMPORTED_MODULE_0__.createFollowUp)({\n                type: 'email',\n                status: 'pending',\n                priority: 'medium',\n                customerId,\n                title: '3-Day Customer Follow-up (Email)',\n                description: `Automated follow-up email to check customer satisfaction and offer assistance`,\n                scheduledDate: followupDate.toISOString(),\n                automationRule: {\n                    trigger: 'customer_created',\n                    delay: DEFAULT_CONFIG.delayDays * 24,\n                    conditions: {\n                        type: 'email',\n                        template: DEFAULT_CONFIG.emailTemplate\n                    }\n                },\n                createdBy: 'system'\n            });\n        }\n        // Schedule SMS follow-up\n        if (DEFAULT_CONFIG.enableSMS && customerData.personalInfo?.phone) {\n            await (0,_crmStorage__WEBPACK_IMPORTED_MODULE_0__.createFollowUp)({\n                type: 'sms',\n                status: 'pending',\n                priority: 'medium',\n                customerId,\n                title: '3-Day Customer Follow-up (SMS)',\n                description: `Automated SMS follow-up to check customer satisfaction`,\n                scheduledDate: followupDate.toISOString(),\n                automationRule: {\n                    trigger: 'customer_created',\n                    delay: DEFAULT_CONFIG.delayDays * 24,\n                    conditions: {\n                        type: 'sms',\n                        template: DEFAULT_CONFIG.smsTemplate,\n                        phone: customerData.personalInfo.phone\n                    }\n                },\n                createdBy: 'system'\n            });\n        }\n        console.log(`📅 Scheduled automated follow-ups for customer ${customerId} in ${DEFAULT_CONFIG.delayDays} days`);\n    } catch (error) {\n        console.error('Error scheduling auto follow-up for customer:', error);\n    }\n}\n/**\n * Schedule automatic follow-up for new lead\n */ async function scheduleAutoFollowupForLead(leadId, leadData) {\n    try {\n        const followupDate = new Date();\n        followupDate.setDate(followupDate.getDate() + DEFAULT_CONFIG.delayDays);\n        // Schedule email follow-up\n        if (DEFAULT_CONFIG.enableEmail && leadData.customerInfo?.email) {\n            await (0,_crmStorage__WEBPACK_IMPORTED_MODULE_0__.createFollowUp)({\n                type: 'email',\n                status: 'pending',\n                priority: 'high',\n                leadId,\n                title: '3-Day Lead Follow-up (Email)',\n                description: `Automated follow-up email for lead nurturing and conversion`,\n                scheduledDate: followupDate.toISOString(),\n                automationRule: {\n                    trigger: 'lead_created',\n                    delay: DEFAULT_CONFIG.delayDays * 24,\n                    conditions: {\n                        type: 'email',\n                        template: 'lead_followup',\n                        leadSource: leadData.source,\n                        productInterest: leadData.inquiry?.productInterest\n                    }\n                },\n                createdBy: 'system'\n            });\n        }\n        // Schedule SMS follow-up\n        if (DEFAULT_CONFIG.enableSMS && leadData.customerInfo?.phone) {\n            await (0,_crmStorage__WEBPACK_IMPORTED_MODULE_0__.createFollowUp)({\n                type: 'sms',\n                status: 'pending',\n                priority: 'high',\n                leadId,\n                title: '3-Day Lead Follow-up (SMS)',\n                description: `Automated SMS follow-up for lead conversion`,\n                scheduledDate: followupDate.toISOString(),\n                automationRule: {\n                    trigger: 'lead_created',\n                    delay: DEFAULT_CONFIG.delayDays * 24,\n                    conditions: {\n                        type: 'sms',\n                        template: 'lead_followup_sms',\n                        phone: leadData.customerInfo.phone,\n                        productInterest: leadData.inquiry?.productInterest\n                    }\n                },\n                createdBy: 'system'\n            });\n        }\n        console.log(`📅 Scheduled automated follow-ups for lead ${leadId} in ${DEFAULT_CONFIG.delayDays} days`);\n    } catch (error) {\n        console.error('Error scheduling auto follow-up for lead:', error);\n    }\n}\n/**\n * Process pending automated follow-ups\n * This should be called periodically (e.g., every hour) to send due follow-ups\n */ async function processAutomatedFollowups() {\n    try {\n        const { getAllFollowUps, updateFollowUp, getCustomerById, getLeadById } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./crmStorage */ \"(rsc)/./src/lib/crmStorage.ts\"));\n        const followups = await getAllFollowUps();\n        const now = new Date();\n        // Filter for pending automated follow-ups that are due\n        const dueFollowups = followups.filter((followup)=>followup.status === 'pending' && followup.automationRule && new Date(followup.scheduledDate) <= now);\n        console.log(`🔄 Processing ${dueFollowups.length} due automated follow-ups`);\n        for (const followup of dueFollowups){\n            try {\n                await processIndividualFollowup(followup);\n                // Mark as completed\n                await updateFollowUp(followup.id, {\n                    status: 'completed',\n                    completedAt: new Date().toISOString(),\n                    notes: `${followup.notes || ''}\\n\\nAutomatically processed on ${new Date().toLocaleString()}`\n                });\n            } catch (error) {\n                console.error(`Error processing followup ${followup.id}:`, error);\n                // Mark as failed\n                await updateFollowUp(followup.id, {\n                    status: 'failed',\n                    notes: `${followup.notes || ''}\\n\\nFailed to process: ${error instanceof Error ? error.message : 'Unknown error'}`\n                });\n            }\n        }\n    } catch (error) {\n        console.error('Error processing automated follow-ups:', error);\n    }\n}\n/**\n * Process individual follow-up (send email/SMS)\n */ async function processIndividualFollowup(followup) {\n    const { getCustomerById, getLeadById } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./crmStorage */ \"(rsc)/./src/lib/crmStorage.ts\"));\n    let recipient = null;\n    let recipientType = '';\n    // Get recipient data\n    if (followup.customerId) {\n        recipient = await getCustomerById(followup.customerId);\n        recipientType = 'customer';\n    } else if (followup.leadId) {\n        recipient = await getLeadById(followup.leadId);\n        recipientType = 'lead';\n    }\n    if (!recipient) {\n        throw new Error(`Recipient not found for followup ${followup.id}`);\n    }\n    const conditions = followup.automationRule?.conditions || {};\n    if (followup.type === 'email') {\n        await sendAutomatedEmail(recipient, recipientType, conditions, followup);\n    } else if (followup.type === 'sms') {\n        await sendAutomatedSMS(recipient, recipientType, conditions, followup);\n    }\n    // Log interaction\n    await (0,_crmStorage__WEBPACK_IMPORTED_MODULE_0__.createInteraction)({\n        customerId: followup.customerId,\n        leadId: followup.leadId,\n        type: followup.type === 'email' ? 'email' : 'sms',\n        direction: 'outbound',\n        channel: 'automation',\n        content: `Automated ${followup.type} follow-up sent: ${followup.title}`,\n        subject: followup.title,\n        tags: [\n            'automated',\n            'follow_up',\n            followup.type\n        ],\n        createdBy: 'system'\n    });\n}\n/**\n * Send automated email\n */ async function sendAutomatedEmail(recipient, recipientType, conditions, followup) {\n    const email = recipientType === 'customer' ? recipient.personalInfo?.email : recipient.customerInfo?.email;\n    if (!email) {\n        throw new Error('No email address found for recipient');\n    }\n    const name = recipientType === 'customer' ? recipient.personalInfo?.name : recipient.customerInfo?.name;\n    // Prepare email data based on template\n    let emailData = {\n        to: email,\n        customerName: name,\n        followupType: followup.title,\n        description: followup.description\n    };\n    if (conditions.template === 'lead_followup') {\n        emailData = {\n            ...emailData,\n            productInterest: conditions.productInterest || 'our vehicles',\n            leadSource: conditions.leadSource || 'website',\n            inquiryDetails: recipient.inquiry?.message || ''\n        };\n    }\n    // Send email using the email service\n    await _resendService__WEBPACK_IMPORTED_MODULE_1__.emailService.sendFollowUpEmail(emailData);\n    console.log(`📧 Automated email sent to ${email}`);\n}\n/**\n * Send automated SMS\n */ async function sendAutomatedSMS(recipient, recipientType, conditions, followup) {\n    const phone = conditions.phone;\n    if (!phone) {\n        throw new Error('No phone number found for recipient');\n    }\n    const name = recipientType === 'customer' ? recipient.personalInfo?.name : recipient.customerInfo?.name;\n    // Prepare SMS message\n    let message = '';\n    if (conditions.template === 'lead_followup_sms') {\n        const productInterest = conditions.productInterest || 'vehicles';\n        message = `Hi ${name}! Following up on your interest in ${productInterest}. We have great options available. Any questions? Reply or call +233245375692. - EBAM Motors`;\n    } else {\n        message = `Hi ${name}! Hope you're satisfied with our service. Need any assistance with your vehicle or have questions? We're here to help! Call +233245375692. - EBAM Motors`;\n    }\n    // For now, we'll log the SMS (you can integrate with SMS service like Twilio)\n    console.log(`📱 Automated SMS would be sent to ${phone}: ${message}`);\n// TODO: Integrate with actual SMS service\n// await smsService.send(phone, message);\n}\n/**\n * Initialize automated follow-up system\n * Call this when the application starts\n */ function initializeAutomatedFollowups() {\n    console.log('🚀 Initializing automated follow-up system...');\n    // Process pending follow-ups every hour\n    setInterval(processAutomatedFollowups, 60 * 60 * 1000);\n    // Also process immediately on startup\n    setTimeout(processAutomatedFollowups, 5000); // Wait 5 seconds after startup\n    console.log('✅ Automated follow-up system initialized');\n}\n/**\n * Manual trigger for processing follow-ups (for testing)\n */ async function triggerFollowupProcessing() {\n    console.log('🔄 Manually triggering follow-up processing...');\n    try {\n        await processAutomatedFollowups();\n        return {\n            processed: 1,\n            errors: 0\n        };\n    } catch (error) {\n        console.error('Error in manual follow-up processing:', error);\n        return {\n            processed: 0,\n            errors: 1\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/automatedFollowup.ts\n");

/***/ })

};
;