/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/cars/route";
exports.ids = ["app/api/admin/cars/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcars%2Froute&page=%2Fapi%2Fadmin%2Fcars%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcars%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcars%2Froute&page=%2Fapi%2Fadmin%2Fcars%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcars%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_User_Desktop_website_ebammotors_src_app_api_admin_cars_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/cars/route.ts */ \"(rsc)/./src/app/api/admin/cars/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/cars/route\",\n        pathname: \"/api/admin/cars\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/cars/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\app\\\\api\\\\admin\\\\cars\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_User_Desktop_website_ebammotors_src_app_api_admin_cars_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZhZG1pbiUyRmNhcnMlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRmFkbWluJTJGY2FycyUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRmFkbWluJTJGY2FycyUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNVc2VyJTVDRGVza3RvcCU1Q3dlYnNpdGUlNUNlYmFtbW90b3JzJTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1DJTNBJTVDVXNlcnMlNUNVc2VyJTVDRGVza3RvcCU1Q3dlYnNpdGUlNUNlYmFtbW90b3JzJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNxQjtBQUNtQztBQUNoSDtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IseUdBQW1CO0FBQzNDO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLHNEQUFzRDtBQUM5RDtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUMwRjs7QUFFMUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiQzpcXFxcVXNlcnNcXFxcVXNlclxcXFxEZXNrdG9wXFxcXHdlYnNpdGVcXFxcZWJhbW1vdG9yc1xcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxhZG1pblxcXFxjYXJzXFxcXHJvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9hZG1pbi9jYXJzL3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvYWRtaW4vY2Fyc1wiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvYWRtaW4vY2Fycy9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIkM6XFxcXFVzZXJzXFxcXFVzZXJcXFxcRGVza3RvcFxcXFx3ZWJzaXRlXFxcXGViYW1tb3RvcnNcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcYWRtaW5cXFxcY2Fyc1xcXFxyb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcyB9ID0gcm91dGVNb2R1bGU7XG5mdW5jdGlvbiBwYXRjaEZldGNoKCkge1xuICAgIHJldHVybiBfcGF0Y2hGZXRjaCh7XG4gICAgICAgIHdvcmtBc3luY1N0b3JhZ2UsXG4gICAgICAgIHdvcmtVbml0QXN5bmNTdG9yYWdlXG4gICAgfSk7XG59XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBwYXRjaEZldGNoLCAgfTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXJvdXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcars%2Froute&page=%2Fapi%2Fadmin%2Fcars%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcars%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/cars/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/admin/cars/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_adminMiddleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/adminMiddleware */ \"(rsc)/./src/lib/adminMiddleware.ts\");\n/* harmony import */ var _vercel_postgres__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @vercel/postgres */ \"(rsc)/./node_modules/@vercel/postgres/dist/index-node.js\");\n\n\n\nasync function GET(request) {\n    try {\n        // Verify admin authentication\n        const adminAuth = (0,_lib_adminMiddleware__WEBPACK_IMPORTED_MODULE_1__.getAdminAuth)(request);\n        if (!adminAuth.isValid) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get('page') || '1');\n        const perPage = parseInt(searchParams.get('per_page') || '20');\n        const search = searchParams.get('search') || '';\n        const sortBy = searchParams.get('sort_by') || 'created_at';\n        const sortOrder = searchParams.get('sort_order') || 'desc';\n        // Filters\n        const make = searchParams.get('make') || '';\n        const model = searchParams.get('model') || '';\n        const status = searchParams.get('status') || '';\n        const yearMin = searchParams.get('year_min') || '';\n        const yearMax = searchParams.get('year_max') || '';\n        const priceMin = searchParams.get('price_min') || '';\n        const priceMax = searchParams.get('price_max') || '';\n        const isFeatured = searchParams.get('is_featured') || '';\n        const location = searchParams.get('location') || '';\n        // Build WHERE clause\n        const conditions = [\n            '1=1'\n        ];\n        const values = [];\n        let paramIndex = 1;\n        if (search) {\n            conditions.push(`(\n        LOWER(title) LIKE $${paramIndex} OR \n        LOWER(make) LIKE $${paramIndex} OR \n        LOWER(model) LIKE $${paramIndex} OR \n        car_id LIKE $${paramIndex}\n      )`);\n            values.push(`%${search.toLowerCase()}%`);\n            paramIndex++;\n        }\n        if (make) {\n            conditions.push(`LOWER(make) = $${paramIndex}`);\n            values.push(make.toLowerCase());\n            paramIndex++;\n        }\n        if (model) {\n            conditions.push(`LOWER(model) = $${paramIndex}`);\n            values.push(model.toLowerCase());\n            paramIndex++;\n        }\n        if (status) {\n            conditions.push(`LOWER(status) = $${paramIndex}`);\n            values.push(status.toLowerCase());\n            paramIndex++;\n        }\n        if (yearMin) {\n            conditions.push(`year >= $${paramIndex}`);\n            values.push(parseInt(yearMin));\n            paramIndex++;\n        }\n        if (yearMax) {\n            conditions.push(`year <= $${paramIndex}`);\n            values.push(parseInt(yearMax));\n            paramIndex++;\n        }\n        if (priceMin) {\n            conditions.push(`price >= $${paramIndex}`);\n            values.push(parseFloat(priceMin));\n            paramIndex++;\n        }\n        if (priceMax) {\n            conditions.push(`price <= $${paramIndex}`);\n            values.push(parseFloat(priceMax));\n            paramIndex++;\n        }\n        if (isFeatured) {\n            conditions.push(`is_featured = $${paramIndex}`);\n            values.push(isFeatured === 'true');\n            paramIndex++;\n        }\n        if (location) {\n            conditions.push(`LOWER(location) = $${paramIndex}`);\n            values.push(location.toLowerCase());\n            paramIndex++;\n        }\n        const whereClause = conditions.join(' AND ');\n        // Validate sort column\n        const validSortColumns = [\n            'created_at',\n            'updated_at',\n            'title',\n            'make',\n            'model',\n            'year',\n            'price',\n            'status'\n        ];\n        const sortColumn = validSortColumns.includes(sortBy) ? sortBy : 'created_at';\n        const sortDirection = sortOrder.toLowerCase() === 'asc' ? 'ASC' : 'DESC';\n        // Get total count\n        const countQuery = `SELECT COUNT(*) as total FROM cars WHERE ${whereClause}`;\n        const countResult = await _vercel_postgres__WEBPACK_IMPORTED_MODULE_2__.sql.query(countQuery, values);\n        const totalCount = parseInt(countResult.rows[0]?.total || '0');\n        // Get cars with pagination\n        const offset = (page - 1) * perPage;\n        const carsQuery = `\n      SELECT \n        id,\n        car_id,\n        make,\n        model,\n        year,\n        title,\n        price,\n        original_price,\n        currency,\n        status,\n        mileage,\n        fuel_type,\n        transmission,\n        body_condition,\n        location,\n        is_featured,\n        stock_quantity,\n        main_image,\n        images,\n        created_at,\n        updated_at\n      FROM cars \n      WHERE ${whereClause}\n      ORDER BY ${sortColumn} ${sortDirection}\n      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}\n    `;\n        values.push(perPage, offset);\n        const carsResult = await _vercel_postgres__WEBPACK_IMPORTED_MODULE_2__.sql.query(carsQuery, values);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            cars: carsResult.rows,\n            total_count: totalCount,\n            page,\n            per_page: perPage,\n            total_pages: Math.ceil(totalCount / perPage)\n        });\n    } catch (error) {\n        console.error('Error fetching cars:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Failed to fetch cars'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        // Verify admin authentication\n        const adminAuth = (0,_lib_adminMiddleware__WEBPACK_IMPORTED_MODULE_1__.getAdminAuth)(request);\n        if (!adminAuth.isValid) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const carData = await request.json();\n        // Validate required fields\n        const requiredFields = [\n            'car_id',\n            'make',\n            'model',\n            'year',\n            'title',\n            'price'\n        ];\n        for (const field of requiredFields){\n            if (!carData[field]) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    message: `Missing required field: ${field}`\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Insert new car\n        const result = await (0,_vercel_postgres__WEBPACK_IMPORTED_MODULE_2__.sql)`\n      INSERT INTO cars (\n        car_id, make, model, year, title, price, original_price, currency,\n        mileage, fuel_type, transmission, engine_size, drive_type, seats, doors, body_type,\n        body_condition, interior_condition, exterior_color, interior_color,\n        main_image, images, image_folder, specs, features,\n        status, stock_quantity, location, slug, description, meta_title, meta_description,\n        is_featured, import_source, import_notes\n      ) VALUES (\n        ${carData.car_id}, ${carData.make}, ${carData.model}, ${carData.year}, ${carData.title},\n        ${carData.price}, ${carData.original_price || null}, ${carData.currency || 'JPY'},\n        ${carData.mileage || null}, ${carData.fuel_type || null}, ${carData.transmission || null},\n        ${carData.engine_size || null}, ${carData.drive_type || null}, ${carData.seats || null},\n        ${carData.doors || null}, ${carData.body_type || null}, ${carData.body_condition || 'Good'},\n        ${carData.interior_condition || 'Good'}, ${carData.exterior_color || null}, ${carData.interior_color || null},\n        ${carData.main_image || null}, ${carData.images || []}, ${carData.image_folder || null},\n        ${carData.specs || []}, ${carData.features || []}, ${carData.status || 'Available'},\n        ${carData.stock_quantity || 1}, ${carData.location || 'Japan'}, ${carData.slug || null},\n        ${carData.description || null}, ${carData.meta_title || null}, ${carData.meta_description || null},\n        ${carData.is_featured || false}, 'admin', 'Added via admin panel'\n      )\n      RETURNING id, car_id\n    `;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Car added successfully',\n            car: result.rows[0]\n        });\n    } catch (error) {\n        console.error('Error adding car:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Failed to add car'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PUT(request) {\n    try {\n        // Verify admin authentication\n        const adminAuth = (0,_lib_adminMiddleware__WEBPACK_IMPORTED_MODULE_1__.getAdminAuth)(request);\n        if (!adminAuth.isValid) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const carId = searchParams.get('id');\n        if (!carId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Car ID is required'\n            }, {\n                status: 400\n            });\n        }\n        const carData = await request.json();\n        // Update car\n        const result = await (0,_vercel_postgres__WEBPACK_IMPORTED_MODULE_2__.sql)`\n      UPDATE cars SET\n        make = ${carData.make},\n        model = ${carData.model},\n        year = ${carData.year},\n        title = ${carData.title},\n        price = ${carData.price},\n        original_price = ${carData.original_price || null},\n        currency = ${carData.currency || 'JPY'},\n        mileage = ${carData.mileage || null},\n        fuel_type = ${carData.fuel_type || null},\n        transmission = ${carData.transmission || null},\n        body_condition = ${carData.body_condition || 'Good'},\n        status = ${carData.status || 'Available'},\n        stock_quantity = ${carData.stock_quantity || 1},\n        location = ${carData.location || 'Japan'},\n        is_featured = ${carData.is_featured || false},\n        description = ${carData.description || null},\n        updated_at = NOW()\n      WHERE id = ${carId}\n      RETURNING id, car_id\n    `;\n        if (result.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Car not found'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Car updated successfully',\n            car: result.rows[0]\n        });\n    } catch (error) {\n        console.error('Error updating car:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Failed to update car'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(request) {\n    try {\n        // Verify admin authentication\n        const adminAuth = (0,_lib_adminMiddleware__WEBPACK_IMPORTED_MODULE_1__.getAdminAuth)(request);\n        if (!adminAuth.isValid) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const carId = searchParams.get('id');\n        if (!carId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Car ID is required'\n            }, {\n                status: 400\n            });\n        }\n        // Delete car\n        const result = await (0,_vercel_postgres__WEBPACK_IMPORTED_MODULE_2__.sql)`\n      DELETE FROM cars WHERE id = ${carId}\n      RETURNING id, car_id\n    `;\n        if (result.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Car not found'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Car deleted successfully'\n        });\n    } catch (error) {\n        console.error('Error deleting car:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Failed to delete car'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/cars/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/adminMiddleware.ts":
/*!************************************!*\
  !*** ./src/lib/adminMiddleware.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAdminAuth: () => (/* binding */ getAdminAuth),\n/* harmony export */   getAdminFromRequest: () => (/* binding */ getAdminFromRequest),\n/* harmony export */   verifyLegacyAdminKey: () => (/* binding */ verifyLegacyAdminKey),\n/* harmony export */   withAdminAuth: () => (/* binding */ withAdminAuth)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n\n\n/**\n * Middleware to verify admin authentication for API routes\n */ function withAdminAuth(handler) {\n    return async (request, context)=>{\n        try {\n            // Get authentication from headers or cookies\n            const authHeader = request.headers.get('authorization');\n            const sessionId = request.cookies.get('admin_session')?.value;\n            // Verify authentication\n            const authResult = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.verifyAdminAuth)(authHeader, sessionId);\n            if (!authResult.isValid) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    message: authResult.message\n                }, {\n                    status: 401\n                });\n            }\n            // Add admin info to request headers for the handler\n            const requestWithAuth = new next_server__WEBPACK_IMPORTED_MODULE_0__.NextRequest(request.url, {\n                method: request.method,\n                headers: {\n                    ...Object.fromEntries(request.headers.entries()),\n                    'x-admin-id': authResult.adminId || 'admin',\n                    'x-admin-authenticated': 'true'\n                },\n                body: request.body\n            });\n            return handler(requestWithAuth, context);\n        } catch (error) {\n            console.error('Admin middleware error:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Authentication error'\n            }, {\n                status: 500\n            });\n        }\n    };\n}\n/**\n * Extract admin authentication from request\n */ function getAdminFromRequest(request) {\n    const adminId = request.headers.get('x-admin-id') || 'admin';\n    const isAuthenticated = request.headers.get('x-admin-authenticated') === 'true';\n    return {\n        adminId,\n        isAuthenticated\n    };\n}\n/**\n * Verify admin authentication for legacy API routes that use adminKey\n */ function verifyLegacyAdminKey(adminKey) {\n    const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';\n    return adminKey === validAdminKey;\n}\n/**\n * Get admin authentication from request (supports both new and legacy methods)\n */ function getAdminAuth(request, body) {\n    // Try new authentication method first\n    const authHeader = request.headers.get('authorization');\n    const sessionId = request.cookies.get('admin_session')?.value;\n    const authResult = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.verifyAdminAuth)(authHeader, sessionId);\n    if (authResult.isValid) {\n        return {\n            isValid: true,\n            adminId: authResult.adminId,\n            method: 'token/session'\n        };\n    }\n    // Fall back to legacy adminKey method\n    const adminKey = body?.adminKey || request.nextUrl.searchParams.get('adminKey');\n    if (adminKey && verifyLegacyAdminKey(adminKey)) {\n        return {\n            isValid: true,\n            adminId: 'admin',\n            method: 'legacy'\n        };\n    }\n    return {\n        isValid: false,\n        method: 'none'\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/adminMiddleware.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticateAdmin: () => (/* binding */ authenticateAdmin),\n/* harmony export */   checkAuthRateLimit: () => (/* binding */ checkAuthRateLimit),\n/* harmony export */   createAdminSession: () => (/* binding */ createAdminSession),\n/* harmony export */   destroyAdminSession: () => (/* binding */ destroyAdminSession),\n/* harmony export */   generateAdminToken: () => (/* binding */ generateAdminToken),\n/* harmony export */   getAdminPasswordHash: () => (/* binding */ getAdminPasswordHash),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   resetAuthRateLimit: () => (/* binding */ resetAuthRateLimit),\n/* harmony export */   validateAdminSession: () => (/* binding */ validateAdminSession),\n/* harmony export */   verifyAdminAuth: () => (/* binding */ verifyAdminAuth),\n/* harmony export */   verifyAdminToken: () => (/* binding */ verifyAdminToken),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Security configuration\nconst SALT_ROUNDS = 12;\nconst JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';\nconst JWT_EXPIRES_IN = '24h';\nconst SESSION_TIMEOUT = 24 * 60 * 60 * 1000; // 24 hours in milliseconds\n// In-memory session store (replace with Redis in production)\nconst activeSessions = new Map();\n/**\n * Hash a password using bcrypt\n */ async function hashPassword(password) {\n    try {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, SALT_ROUNDS);\n    } catch (error) {\n        console.error('Error hashing password:', error);\n        throw new Error('Failed to hash password');\n    }\n}\n/**\n * Verify a password against its hash\n */ async function verifyPassword(password, hash) {\n    try {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hash);\n    } catch (error) {\n        console.error('Error verifying password:', error);\n        return false;\n    }\n}\n/**\n * Generate a JWT token for admin authentication\n */ function generateAdminToken(adminId = 'admin') {\n    try {\n        const payload = {\n            id: adminId,\n            isAdmin: true,\n            iat: Math.floor(Date.now() / 1000)\n        };\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_SECRET, {\n            expiresIn: JWT_EXPIRES_IN\n        });\n    } catch (error) {\n        console.error('Error generating token:', error);\n        throw new Error('Failed to generate authentication token');\n    }\n}\n/**\n * Verify and decode a JWT token\n */ function verifyAdminToken(token) {\n    try {\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, JWT_SECRET);\n        if (decoded.isAdmin) {\n            return {\n                id: decoded.id,\n                isAdmin: decoded.isAdmin\n            };\n        }\n        return null;\n    } catch (error) {\n        // Token is invalid or expired\n        return null;\n    }\n}\n/**\n * Create a new admin session\n */ function createAdminSession(adminId = 'admin') {\n    const sessionId = generateSessionId();\n    const now = Date.now();\n    const session = {\n        id: adminId,\n        isAdmin: true,\n        createdAt: now,\n        expiresAt: now + SESSION_TIMEOUT,\n        lastActivity: now\n    };\n    activeSessions.set(sessionId, session);\n    // Clean up expired sessions\n    cleanupExpiredSessions();\n    return sessionId;\n}\n/**\n * Validate an admin session\n */ function validateAdminSession(sessionId) {\n    const session = activeSessions.get(sessionId);\n    if (!session) {\n        return null;\n    }\n    const now = Date.now();\n    // Check if session has expired\n    if (now > session.expiresAt) {\n        activeSessions.delete(sessionId);\n        return null;\n    }\n    // Update last activity\n    session.lastActivity = now;\n    activeSessions.set(sessionId, session);\n    return session;\n}\n/**\n * Destroy an admin session\n */ function destroyAdminSession(sessionId) {\n    return activeSessions.delete(sessionId);\n}\n/**\n * Generate a secure session ID\n */ function generateSessionId() {\n    return `admin_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;\n}\n/**\n * Clean up expired sessions\n */ function cleanupExpiredSessions() {\n    const now = Date.now();\n    for (const [sessionId, session] of activeSessions.entries()){\n        if (now > session.expiresAt) {\n            activeSessions.delete(sessionId);\n        }\n    }\n}\n/**\n * Get admin password hash from environment\n * In production, this should be stored in a secure database\n */ function getAdminPasswordHash() {\n    // For backward compatibility, check if password is already hashed\n    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';\n    // If it starts with $2a$, $2b$, or $2y$, it's already a bcrypt hash\n    if (adminPassword.startsWith('$2a$') || adminPassword.startsWith('$2b$') || adminPassword.startsWith('$2y$')) {\n        return adminPassword;\n    }\n    // For development/migration: return the plain password (will be handled in auth route)\n    return adminPassword;\n}\n/**\n * Secure admin authentication\n */ async function authenticateAdmin(password) {\n    try {\n        const adminPasswordHash = getAdminPasswordHash();\n        let isValid = false;\n        // Check if stored password is hashed or plain text\n        if (adminPasswordHash.startsWith('$2a$') || adminPasswordHash.startsWith('$2b$') || adminPasswordHash.startsWith('$2y$')) {\n            // Password is hashed, use bcrypt comparison\n            isValid = await verifyPassword(password, adminPasswordHash);\n        } else {\n            // Password is plain text (development/migration), use direct comparison\n            isValid = password === adminPasswordHash;\n        }\n        if (isValid) {\n            const token = generateAdminToken();\n            const sessionId = createAdminSession();\n            return {\n                success: true,\n                token,\n                sessionId,\n                message: 'Authentication successful'\n            };\n        } else {\n            return {\n                success: false,\n                message: 'Invalid credentials'\n            };\n        }\n    } catch (error) {\n        console.error('Authentication error:', error);\n        return {\n            success: false,\n            message: 'Authentication failed'\n        };\n    }\n}\n/**\n * Middleware to verify admin authentication\n */ function verifyAdminAuth(authHeader, sessionId) {\n    // Check JWT token\n    if (authHeader && authHeader.startsWith('Bearer ')) {\n        const token = authHeader.substring(7);\n        const decoded = verifyAdminToken(token);\n        if (decoded) {\n            return {\n                isValid: true,\n                adminId: decoded.id,\n                message: 'Token authentication successful'\n            };\n        }\n    }\n    // Check session ID\n    if (sessionId) {\n        const session = validateAdminSession(sessionId);\n        if (session) {\n            return {\n                isValid: true,\n                adminId: session.id,\n                message: 'Session authentication successful'\n            };\n        }\n    }\n    return {\n        isValid: false,\n        message: 'Authentication required'\n    };\n}\n/**\n * Rate limiting for authentication attempts\n */ const authAttempts = new Map();\nconst MAX_AUTH_ATTEMPTS = 5;\nconst AUTH_LOCKOUT_TIME = 15 * 60 * 1000; // 15 minutes\nfunction checkAuthRateLimit(ip) {\n    const now = Date.now();\n    const attempts = authAttempts.get(ip);\n    if (!attempts) {\n        authAttempts.set(ip, {\n            count: 1,\n            lastAttempt: now\n        });\n        return {\n            allowed: true,\n            remainingAttempts: MAX_AUTH_ATTEMPTS - 1\n        };\n    }\n    // Reset if lockout time has passed\n    if (now - attempts.lastAttempt > AUTH_LOCKOUT_TIME) {\n        authAttempts.set(ip, {\n            count: 1,\n            lastAttempt: now\n        });\n        return {\n            allowed: true,\n            remainingAttempts: MAX_AUTH_ATTEMPTS - 1\n        };\n    }\n    // Check if max attempts exceeded\n    if (attempts.count >= MAX_AUTH_ATTEMPTS) {\n        const lockoutTime = AUTH_LOCKOUT_TIME - (now - attempts.lastAttempt);\n        return {\n            allowed: false,\n            remainingAttempts: 0,\n            lockoutTime\n        };\n    }\n    // Increment attempt count\n    attempts.count++;\n    attempts.lastAttempt = now;\n    authAttempts.set(ip, attempts);\n    return {\n        allowed: true,\n        remainingAttempts: MAX_AUTH_ATTEMPTS - attempts.count\n    };\n}\nfunction resetAuthRateLimit(ip) {\n    authAttempts.delete(ip);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time","vendor-chunks/@neondatabase","vendor-chunks/ws","vendor-chunks/@vercel","vendor-chunks/node-gyp-build","vendor-chunks/bufferutil"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcars%2Froute&page=%2Fapi%2Fadmin%2Fcars%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcars%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();