"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/admin/AdminDashboardLayout.tsx":
/*!*******************************************************!*\
  !*** ./src/components/admin/AdminDashboardLayout.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminDashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Car,LayoutDashboard,LogOut,Menu,MessageSquare,Search,Settings,Shield,ShoppingCart,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Car,LayoutDashboard,LogOut,Menu,MessageSquare,Search,Settings,Shield,ShoppingCart,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/car.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Car,LayoutDashboard,LogOut,Menu,MessageSquare,Search,Settings,Shield,ShoppingCart,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Car,LayoutDashboard,LogOut,Menu,MessageSquare,Search,Settings,Shield,ShoppingCart,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Car,LayoutDashboard,LogOut,Menu,MessageSquare,Search,Settings,Shield,ShoppingCart,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Car,LayoutDashboard,LogOut,Menu,MessageSquare,Search,Settings,Shield,ShoppingCart,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Car,LayoutDashboard,LogOut,Menu,MessageSquare,Search,Settings,Shield,ShoppingCart,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Car,LayoutDashboard,LogOut,Menu,MessageSquare,Search,Settings,Shield,ShoppingCart,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Car,LayoutDashboard,LogOut,Menu,MessageSquare,Search,Settings,Shield,ShoppingCart,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Car,LayoutDashboard,LogOut,Menu,MessageSquare,Search,Settings,Shield,ShoppingCart,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Car,LayoutDashboard,LogOut,Menu,MessageSquare,Search,Settings,Shield,ShoppingCart,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Car,LayoutDashboard,LogOut,Menu,MessageSquare,Search,Settings,Shield,ShoppingCart,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Car,LayoutDashboard,LogOut,Menu,MessageSquare,Search,Settings,Shield,ShoppingCart,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Car,LayoutDashboard,LogOut,Menu,MessageSquare,Search,Settings,Shield,ShoppingCart,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Car,LayoutDashboard,LogOut,Menu,MessageSquare,Search,Settings,Shield,ShoppingCart,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _AdminAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AdminAuth */ \"(app-pages-browser)/./src/components/admin/AdminAuth.tsx\");\n/* harmony import */ var _DashboardOverview__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./DashboardOverview */ \"(app-pages-browser)/./src/components/admin/DashboardOverview.tsx\");\n/* harmony import */ var _SystemHealthMonitoring__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./SystemHealthMonitoring */ \"(app-pages-browser)/./src/components/admin/SystemHealthMonitoring.tsx\");\n/* harmony import */ var _CarManagement__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CarManagement */ \"(app-pages-browser)/./src/components/admin/CarManagement.tsx\");\n/* harmony import */ var _ReviewModeration__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ReviewModeration */ \"(app-pages-browser)/./src/components/admin/ReviewModeration.tsx\");\n/* harmony import */ var _CRMManagement__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CRMManagement */ \"(app-pages-browser)/./src/components/admin/CRMManagement.tsx\");\n/* harmony import */ var _OrderManagement__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./OrderManagement */ \"(app-pages-browser)/./src/components/admin/OrderManagement.tsx\");\n/* harmony import */ var _SystemAnalytics__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./SystemAnalytics */ \"(app-pages-browser)/./src/components/admin/SystemAnalytics.tsx\");\n/* harmony import */ var _SecurityManagement__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./SecurityManagement */ \"(app-pages-browser)/./src/components/admin/SecurityManagement.tsx\");\n/* harmony import */ var _AdminSettings__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./AdminSettings */ \"(app-pages-browser)/./src/components/admin/AdminSettings.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst sidebarItems = [\n    {\n        id: 'overview',\n        label: 'Overview',\n        icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        path: '/admin/dashboard'\n    },\n    {\n        id: 'cars',\n        label: 'Car Management',\n        icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        path: '/admin/cars'\n    },\n    {\n        id: 'orders',\n        label: 'Orders',\n        icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        path: '/admin/orders'\n    },\n    {\n        id: 'customers',\n        label: 'CRM',\n        icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        path: '/admin/customers'\n    },\n    {\n        id: 'reviews',\n        label: 'Reviews',\n        icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n        path: '/admin/reviews'\n    },\n    {\n        id: 'analytics',\n        label: 'Analytics',\n        icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n        path: '/admin/analytics'\n    },\n    {\n        id: 'health',\n        label: 'System Health',\n        icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n        path: '/admin/health'\n    },\n    {\n        id: 'security',\n        label: 'Security',\n        icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n        path: '/admin/security'\n    },\n    {\n        id: 'settings',\n        label: 'Settings',\n        icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n        path: '/admin/settings'\n    }\n];\nfunction AdminDashboardLayout() {\n    var _sidebarItems_find, _sidebarItems_find1;\n    _s();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [authenticated, setAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(3);\n    const [showNotifications, setShowNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const notificationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboardLayout.useEffect\": ()=>{\n            setMounted(true);\n            // Check if user is already authenticated\n            if (true) {\n                const token = localStorage.getItem('admin_token');\n                if (token) {\n                    setAuthenticated(true);\n                }\n            }\n            // Check for section parameter in URL\n            const section = searchParams.get('section');\n            if (section && sidebarItems.find({\n                \"AdminDashboardLayout.useEffect\": (item)=>item.id === section\n            }[\"AdminDashboardLayout.useEffect\"])) {\n                setActiveSection(section);\n            }\n        }\n    }[\"AdminDashboardLayout.useEffect\"], [\n        searchParams\n    ]);\n    // Close notifications when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboardLayout.useEffect\": ()=>{\n            function handleClickOutside(event) {\n                if (notificationRef.current && !notificationRef.current.contains(event.target)) {\n                    setShowNotifications(false);\n                }\n            }\n            if (showNotifications) {\n                document.addEventListener('mousedown', handleClickOutside);\n                return ({\n                    \"AdminDashboardLayout.useEffect\": ()=>{\n                        document.removeEventListener('mousedown', handleClickOutside);\n                    }\n                })[\"AdminDashboardLayout.useEffect\"];\n            }\n        }\n    }[\"AdminDashboardLayout.useEffect\"], [\n        showNotifications\n    ]);\n    const handleLogout = ()=>{\n        if (true) {\n            localStorage.removeItem('admin_token');\n        }\n        setAuthenticated(false);\n    };\n    const handleSectionChange = (sectionId, path)=>{\n        setActiveSection(sectionId);\n        setSidebarOpen(false);\n        // Update URL with section parameter\n        const newUrl = \"/admin/dashboard?section=\".concat(sectionId);\n        router.push(newUrl);\n    };\n    const handleNotificationClick = (notificationType, notificationId)=>{\n        // Close the notifications dropdown\n        setShowNotifications(false);\n        // Navigate based on notification type\n        switch(notificationType){\n            case 'order':\n                handleSectionChange('orders', '/admin/orders');\n                break;\n            case 'review':\n                handleSectionChange('reviews', '/admin/reviews');\n                break;\n            case 'system':\n                handleSectionChange('health', '/admin/health');\n                break;\n            case 'car':\n                handleSectionChange('cars', '/admin/cars');\n                break;\n            default:\n                // For general notifications, stay on overview\n                handleSectionChange('overview', '/admin/dashboard');\n        }\n        // Optionally decrease notification count\n        if (notifications > 0) {\n            setNotifications((prev)=>prev - 1);\n        }\n    };\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                lineNumber: 144,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n            lineNumber: 143,\n            columnNumber: 7\n        }, this);\n    }\n    if (!authenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdminAuth__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            onAuthenticated: ()=>setAuthenticated(true)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n            lineNumber: 153,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform \".concat(sidebarOpen ? 'translate-x-0' : '-translate-x-full', \" transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16 px-6 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold text-gray-900\",\n                                children: \"EBAM Admin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSidebarOpen(false),\n                                className: \"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"mt-6 px-3\",\n                        children: sidebarItems.map((item)=>{\n                            const Icon = item.icon;\n                            const isActive = activeSection === item.id;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleSectionChange(item.id, item.path),\n                                className: \"w-full flex items-center px-3 py-2 mb-1 text-sm font-medium rounded-lg transition-colors \".concat(isActive ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"w-5 h-5 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 17\n                                    }, this),\n                                    item.label,\n                                    item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-auto bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full\",\n                                        children: item.badge\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, item.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col lg:ml-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"bg-white shadow-sm border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between h-16 px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSidebarOpen(true),\n                                            className: \"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4 lg:ml-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-900\",\n                                                children: ((_sidebarItems_find = sidebarItems.find((item)=>item.id === activeSection)) === null || _sidebarItems_find === void 0 ? void 0 : _sidebarItems_find.label) || 'Dashboard'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden md:block relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Search...\",\n                                                    className: \"pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            ref: notificationRef,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowNotifications(!showNotifications),\n                                                    className: \"relative p-2 text-gray-400 hover:text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                            lineNumber: 237,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        notifications > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\",\n                                                            children: notifications\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 17\n                                                }, this),\n                                                showNotifications && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 border-b border-gray-200\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-900\",\n                                                                children: \"Notifications\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                lineNumber: 249,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"max-h-96 overflow-y-auto\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleNotificationClick('order', 'ORD-001'),\n                                                                    className: \"w-full p-4 border-b border-gray-100 hover:bg-gray-50 text-left transition-colors\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-start\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-shrink-0\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                        className: \"w-4 h-4 text-blue-600\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                        lineNumber: 260,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                    lineNumber: 259,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                lineNumber: 258,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"ml-3 flex-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                                        children: \"New order received\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                        lineNumber: 264,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-600\",\n                                                                                        children: \"Order #ORD-001 from John Doe\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                        lineNumber: 265,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                                                        children: \"2 minutes ago\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                        lineNumber: 266,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                lineNumber: 263,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                        lineNumber: 257,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                    lineNumber: 253,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-4 border-b border-gray-100 hover:bg-gray-50\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-start\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-shrink-0\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                        className: \"w-4 h-4 text-green-600\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                        lineNumber: 275,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                    lineNumber: 274,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                lineNumber: 273,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"ml-3 flex-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                                        children: \"New review submitted\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                        lineNumber: 279,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-600\",\n                                                                                        children: \"5-star review for Toyota Voxy\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                        lineNumber: 280,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                                                        children: \"15 minutes ago\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                        lineNumber: 281,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                lineNumber: 278,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                        lineNumber: 272,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-4 border-b border-gray-100 hover:bg-gray-50\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-start\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-shrink-0\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                        className: \"w-4 h-4 text-yellow-600\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                        lineNumber: 290,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                    lineNumber: 289,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                lineNumber: 288,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"ml-3 flex-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                                        children: \"System alert\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                        lineNumber: 294,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-600\",\n                                                                                        children: \"Low inventory warning for Honda Freed\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                        lineNumber: 295,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                                                        children: \"1 hour ago\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                        lineNumber: 296,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                lineNumber: 293,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                        lineNumber: 287,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                    lineNumber: 286,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 border-t border-gray-200\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setShowNotifications(false),\n                                                                className: \"w-full text-center text-sm text-blue-600 hover:text-blue-800\",\n                                                                children: \"View all notifications\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleLogout,\n                                            className: \"flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Logout\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-6 overflow-auto\",\n                        children: [\n                            activeSection === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DashboardOverview__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 44\n                            }, this),\n                            activeSection === 'health' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SystemHealthMonitoring__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 42\n                            }, this),\n                            activeSection === 'cars' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CarManagement__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 40\n                            }, this),\n                            activeSection === 'reviews' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ReviewModeration__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 43\n                            }, this),\n                            activeSection === 'customers' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CRMManagement__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 45\n                            }, this),\n                            activeSection === 'orders' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OrderManagement__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 42\n                            }, this),\n                            activeSection === 'analytics' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SystemAnalytics__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 45\n                            }, this),\n                            activeSection === 'security' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SecurityManagement__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 44\n                            }, this),\n                            activeSection === 'settings' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdminSettings__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 44\n                            }, this),\n                            activeSection !== 'overview' && activeSection !== 'health' && activeSection !== 'cars' && activeSection !== 'reviews' && activeSection !== 'customers' && activeSection !== 'orders' && activeSection !== 'analytics' && activeSection !== 'security' && activeSection !== 'settings' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: (_sidebarItems_find1 = sidebarItems.find((item)=>item.id === activeSection)) === null || _sidebarItems_find1 === void 0 ? void 0 : _sidebarItems_find1.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"This section is under development. Please check back soon.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this),\n            sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\",\n                onClick: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                lineNumber: 351,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminDashboardLayout, \"YgSc9JmVtcbt04Api0L8c4fWsgw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = AdminDashboardLayout;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboardLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/AdminDashboardLayout.tsx\n"));

/***/ })

});