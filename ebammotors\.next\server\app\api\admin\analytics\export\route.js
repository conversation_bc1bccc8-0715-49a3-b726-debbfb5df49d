/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/analytics/export/route";
exports.ids = ["app/api/admin/analytics/export/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fanalytics%2Fexport%2Froute&page=%2Fapi%2Fadmin%2Fanalytics%2Fexport%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fanalytics%2Fexport%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fanalytics%2Fexport%2Froute&page=%2Fapi%2Fadmin%2Fanalytics%2Fexport%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fanalytics%2Fexport%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_User_Desktop_website_ebammotors_src_app_api_admin_analytics_export_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/analytics/export/route.ts */ \"(rsc)/./src/app/api/admin/analytics/export/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/analytics/export/route\",\n        pathname: \"/api/admin/analytics/export\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/analytics/export/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\app\\\\api\\\\admin\\\\analytics\\\\export\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_User_Desktop_website_ebammotors_src_app_api_admin_analytics_export_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fanalytics%2Fexport%2Froute&page=%2Fapi%2Fadmin%2Fanalytics%2Fexport%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fanalytics%2Fexport%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/analytics/export/route.ts":
/*!*****************************************************!*\
  !*** ./src/app/api/admin/analytics/export/route.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_adminMiddleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/adminMiddleware */ \"(rsc)/./src/lib/adminMiddleware.ts\");\n/* harmony import */ var _lib_orderStorage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/orderStorage */ \"(rsc)/./src/lib/orderStorage.ts\");\n/* harmony import */ var _lib_crmStorage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/crmStorage */ \"(rsc)/./src/lib/crmStorage.ts\");\n\n\n\n\nasync function GET(request) {\n    try {\n        // Verify admin authentication\n        const adminAuth = (0,_lib_adminMiddleware__WEBPACK_IMPORTED_MODULE_1__.getAdminAuth)(request);\n        if (!adminAuth.isValid) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const type = searchParams.get('type') || 'overview';\n        const range = searchParams.get('range') || '30d';\n        // Generate CSV data based on type\n        let csvData = '';\n        let filename = `analytics-${type}-${range}.csv`;\n        switch(type){\n            case 'orders':\n                csvData = await generateOrdersCSV(range);\n                filename = `orders-report-${range}.csv`;\n                break;\n            case 'customers':\n                csvData = await generateCustomersCSV(range);\n                filename = `customers-report-${range}.csv`;\n                break;\n            case 'revenue':\n                csvData = await generateRevenueCSV(range);\n                filename = `revenue-report-${range}.csv`;\n                break;\n            default:\n                csvData = await generateOverviewCSV(range);\n                filename = `overview-report-${range}.csv`;\n        }\n        // Return CSV file\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(csvData, {\n            status: 200,\n            headers: {\n                'Content-Type': 'text/csv',\n                'Content-Disposition': `attachment; filename=\"${filename}\"`\n            }\n        });\n    } catch (error) {\n        console.error('Error exporting analytics:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Failed to export analytics'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function generateOrdersCSV(range) {\n    const orders = await (0,_lib_orderStorage__WEBPACK_IMPORTED_MODULE_2__.getAllOrders)();\n    // Filter by date range\n    const filteredOrders = filterByDateRange(orders, range);\n    const headers = [\n        'Order Number',\n        'Customer Name',\n        'Customer Email',\n        'Vehicle',\n        'Order Amount',\n        'Payment Status',\n        'Order Status',\n        'Created Date',\n        'Payment Method',\n        'Shipping Country'\n    ];\n    const rows = filteredOrders.map((order)=>[\n            order.orderNumber,\n            order.customerInfo.name,\n            order.customerInfo.email,\n            order.vehicle.title,\n            order.totalAmount,\n            order.payment.status,\n            order.status,\n            new Date(order.createdAt).toLocaleDateString(),\n            order.payment.method.name,\n            order.customerInfo.address.country\n        ]);\n    return generateCSV(headers, rows);\n}\nasync function generateCustomersCSV(range) {\n    const customers = await (0,_lib_crmStorage__WEBPACK_IMPORTED_MODULE_3__.getAllCustomers)();\n    // Filter by date range\n    const filteredCustomers = filterByDateRange(customers, range);\n    const headers = [\n        'Customer Name',\n        'Email',\n        'Phone',\n        'City',\n        'Country',\n        'Status',\n        'Segment',\n        'Total Orders',\n        'Total Spent',\n        'Membership Tier',\n        'Loyalty Points',\n        'Created Date',\n        'Last Order Date'\n    ];\n    const rows = filteredCustomers.map((customer)=>[\n            customer.personalInfo.name,\n            customer.personalInfo.email,\n            customer.personalInfo.phone || '',\n            customer.address.city || '',\n            customer.address.country || '',\n            customer.status,\n            customer.segment,\n            customer.totalOrders || 0,\n            customer.totalSpent || 0,\n            customer.membershipTier,\n            customer.loyaltyPoints || 0,\n            new Date(customer.createdAt).toLocaleDateString(),\n            customer.lastOrderDate ? new Date(customer.lastOrderDate).toLocaleDateString() : ''\n        ]);\n    return generateCSV(headers, rows);\n}\nasync function generateRevenueCSV(range) {\n    const orders = await (0,_lib_orderStorage__WEBPACK_IMPORTED_MODULE_2__.getAllOrders)();\n    // Filter by date range and completed payments\n    const filteredOrders = filterByDateRange(orders, range).filter((order)=>order.payment.status === 'completed');\n    // Group by month\n    const monthlyRevenue = {};\n    filteredOrders.forEach((order)=>{\n        const date = new Date(order.createdAt);\n        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;\n        if (!monthlyRevenue[monthKey]) {\n            monthlyRevenue[monthKey] = {\n                revenue: 0,\n                orders: 0,\n                averageOrderValue: 0\n            };\n        }\n        monthlyRevenue[monthKey].revenue += order.totalAmount;\n        monthlyRevenue[monthKey].orders += 1;\n    });\n    // Calculate average order values\n    Object.keys(monthlyRevenue).forEach((month)=>{\n        const data = monthlyRevenue[month];\n        data.averageOrderValue = data.orders > 0 ? data.revenue / data.orders : 0;\n    });\n    const headers = [\n        'Month',\n        'Total Revenue',\n        'Total Orders',\n        'Average Order Value'\n    ];\n    const rows = Object.entries(monthlyRevenue).sort(([a], [b])=>a.localeCompare(b)).map(([month, data])=>[\n            month,\n            data.revenue,\n            data.orders,\n            Math.round(data.averageOrderValue)\n        ]);\n    return generateCSV(headers, rows);\n}\nasync function generateOverviewCSV(range) {\n    const [orders, customers] = await Promise.all([\n        (0,_lib_orderStorage__WEBPACK_IMPORTED_MODULE_2__.getAllOrders)(),\n        (0,_lib_crmStorage__WEBPACK_IMPORTED_MODULE_3__.getAllCustomers)()\n    ]);\n    const filteredOrders = filterByDateRange(orders, range);\n    const filteredCustomers = filterByDateRange(customers, range);\n    const completedOrders = filteredOrders.filter((o)=>o.payment.status === 'completed');\n    const totalRevenue = completedOrders.reduce((sum, o)=>sum + o.totalAmount, 0);\n    const averageOrderValue = completedOrders.length > 0 ? totalRevenue / completedOrders.length : 0;\n    // Calculate metrics by country\n    const countryStats = {};\n    filteredCustomers.forEach((customer)=>{\n        const country = customer.address?.country || 'Unknown';\n        if (!countryStats[country]) {\n            countryStats[country] = {\n                customers: 0,\n                orders: 0,\n                revenue: 0\n            };\n        }\n        countryStats[country].customers += 1;\n    });\n    filteredOrders.forEach((order)=>{\n        const country = order.customerInfo.address.country || 'Unknown';\n        if (!countryStats[country]) {\n            countryStats[country] = {\n                customers: 0,\n                orders: 0,\n                revenue: 0\n            };\n        }\n        countryStats[country].orders += 1;\n        if (order.payment.status === 'completed') {\n            countryStats[country].revenue += order.totalAmount;\n        }\n    });\n    const headers = [\n        'Metric',\n        'Value'\n    ];\n    const overviewRows = [\n        [\n            'Total Orders',\n            filteredOrders.length\n        ],\n        [\n            'Completed Orders',\n            completedOrders.length\n        ],\n        [\n            'Total Customers',\n            filteredCustomers.length\n        ],\n        [\n            'Total Revenue',\n            totalRevenue\n        ],\n        [\n            'Average Order Value',\n            Math.round(averageOrderValue)\n        ],\n        [\n            'Conversion Rate',\n            filteredCustomers.length > 0 ? (completedOrders.length / filteredCustomers.length * 100).toFixed(2) + '%' : '0%'\n        ]\n    ];\n    let csv = generateCSV(headers, overviewRows);\n    // Add country breakdown\n    csv += '\\n\\nCountry Breakdown\\n';\n    csv += generateCSV([\n        'Country',\n        'Customers',\n        'Orders',\n        'Revenue'\n    ], Object.entries(countryStats).map(([country, stats])=>[\n            country,\n            stats.customers,\n            stats.orders,\n            stats.revenue\n        ]));\n    return csv;\n}\nfunction filterByDateRange(data, range) {\n    const endDate = new Date();\n    const startDate = new Date();\n    switch(range){\n        case '7d':\n            startDate.setDate(endDate.getDate() - 7);\n            break;\n        case '30d':\n            startDate.setDate(endDate.getDate() - 30);\n            break;\n        case '90d':\n            startDate.setDate(endDate.getDate() - 90);\n            break;\n        case '1y':\n            startDate.setFullYear(endDate.getFullYear() - 1);\n            break;\n        default:\n            startDate.setDate(endDate.getDate() - 30);\n    }\n    return data.filter((item)=>{\n        const itemDate = new Date(item.createdAt);\n        return itemDate >= startDate && itemDate <= endDate;\n    });\n}\nfunction generateCSV(headers, rows) {\n    const csvRows = [\n        headers,\n        ...rows\n    ];\n    return csvRows.map((row)=>row.map((field)=>{\n            // Escape quotes and wrap in quotes if necessary\n            const stringField = String(field);\n            if (stringField.includes(',') || stringField.includes('\"') || stringField.includes('\\n')) {\n                return `\"${stringField.replace(/\"/g, '\"\"')}\"`;\n            }\n            return stringField;\n        }).join(',')).join('\\n');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/analytics/export/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/adminMiddleware.ts":
/*!************************************!*\
  !*** ./src/lib/adminMiddleware.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAdminAuth: () => (/* binding */ getAdminAuth),\n/* harmony export */   getAdminFromRequest: () => (/* binding */ getAdminFromRequest),\n/* harmony export */   verifyLegacyAdminKey: () => (/* binding */ verifyLegacyAdminKey),\n/* harmony export */   withAdminAuth: () => (/* binding */ withAdminAuth)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n\n\n/**\n * Middleware to verify admin authentication for API routes\n */ function withAdminAuth(handler) {\n    return async (request, context)=>{\n        try {\n            // Get authentication from headers or cookies\n            const authHeader = request.headers.get('authorization');\n            const sessionId = request.cookies.get('admin_session')?.value;\n            // Verify authentication\n            const authResult = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.verifyAdminAuth)(authHeader, sessionId);\n            if (!authResult.isValid) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    message: authResult.message\n                }, {\n                    status: 401\n                });\n            }\n            // Add admin info to request headers for the handler\n            const requestWithAuth = new next_server__WEBPACK_IMPORTED_MODULE_0__.NextRequest(request.url, {\n                method: request.method,\n                headers: {\n                    ...Object.fromEntries(request.headers.entries()),\n                    'x-admin-id': authResult.adminId || 'admin',\n                    'x-admin-authenticated': 'true'\n                },\n                body: request.body\n            });\n            return handler(requestWithAuth, context);\n        } catch (error) {\n            console.error('Admin middleware error:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Authentication error'\n            }, {\n                status: 500\n            });\n        }\n    };\n}\n/**\n * Extract admin authentication from request\n */ function getAdminFromRequest(request) {\n    const adminId = request.headers.get('x-admin-id') || 'admin';\n    const isAuthenticated = request.headers.get('x-admin-authenticated') === 'true';\n    return {\n        adminId,\n        isAuthenticated\n    };\n}\n/**\n * Verify admin authentication for legacy API routes that use adminKey\n */ function verifyLegacyAdminKey(adminKey) {\n    const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';\n    return adminKey === validAdminKey;\n}\n/**\n * Get admin authentication from request (supports both new and legacy methods)\n */ function getAdminAuth(request, body) {\n    // Try new authentication method first\n    const authHeader = request.headers.get('authorization');\n    const sessionId = request.cookies.get('admin_session')?.value;\n    const authResult = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.verifyAdminAuth)(authHeader, sessionId);\n    if (authResult.isValid) {\n        return {\n            isValid: true,\n            adminId: authResult.adminId,\n            method: 'token/session'\n        };\n    }\n    // Fall back to legacy adminKey method\n    const adminKey = body?.adminKey || request.nextUrl.searchParams.get('adminKey');\n    if (adminKey && verifyLegacyAdminKey(adminKey)) {\n        return {\n            isValid: true,\n            adminId: 'admin',\n            method: 'legacy'\n        };\n    }\n    return {\n        isValid: false,\n        method: 'none'\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/adminMiddleware.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticateAdmin: () => (/* binding */ authenticateAdmin),\n/* harmony export */   checkAuthRateLimit: () => (/* binding */ checkAuthRateLimit),\n/* harmony export */   createAdminSession: () => (/* binding */ createAdminSession),\n/* harmony export */   destroyAdminSession: () => (/* binding */ destroyAdminSession),\n/* harmony export */   generateAdminToken: () => (/* binding */ generateAdminToken),\n/* harmony export */   getAdminPasswordHash: () => (/* binding */ getAdminPasswordHash),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   resetAuthRateLimit: () => (/* binding */ resetAuthRateLimit),\n/* harmony export */   validateAdminSession: () => (/* binding */ validateAdminSession),\n/* harmony export */   verifyAdminAuth: () => (/* binding */ verifyAdminAuth),\n/* harmony export */   verifyAdminToken: () => (/* binding */ verifyAdminToken),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Security configuration\nconst SALT_ROUNDS = 12;\nconst JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';\nconst JWT_EXPIRES_IN = '24h';\nconst SESSION_TIMEOUT = 24 * 60 * 60 * 1000; // 24 hours in milliseconds\n// In-memory session store (replace with Redis in production)\nconst activeSessions = new Map();\n/**\n * Hash a password using bcrypt\n */ async function hashPassword(password) {\n    try {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, SALT_ROUNDS);\n    } catch (error) {\n        console.error('Error hashing password:', error);\n        throw new Error('Failed to hash password');\n    }\n}\n/**\n * Verify a password against its hash\n */ async function verifyPassword(password, hash) {\n    try {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hash);\n    } catch (error) {\n        console.error('Error verifying password:', error);\n        return false;\n    }\n}\n/**\n * Generate a JWT token for admin authentication\n */ function generateAdminToken(adminId = 'admin') {\n    try {\n        const payload = {\n            id: adminId,\n            isAdmin: true,\n            iat: Math.floor(Date.now() / 1000)\n        };\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_SECRET, {\n            expiresIn: JWT_EXPIRES_IN\n        });\n    } catch (error) {\n        console.error('Error generating token:', error);\n        throw new Error('Failed to generate authentication token');\n    }\n}\n/**\n * Verify and decode a JWT token\n */ function verifyAdminToken(token) {\n    try {\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, JWT_SECRET);\n        if (decoded.isAdmin) {\n            return {\n                id: decoded.id,\n                isAdmin: decoded.isAdmin\n            };\n        }\n        return null;\n    } catch (error) {\n        // Token is invalid or expired\n        return null;\n    }\n}\n/**\n * Create a new admin session\n */ function createAdminSession(adminId = 'admin') {\n    const sessionId = generateSessionId();\n    const now = Date.now();\n    const session = {\n        id: adminId,\n        isAdmin: true,\n        createdAt: now,\n        expiresAt: now + SESSION_TIMEOUT,\n        lastActivity: now\n    };\n    activeSessions.set(sessionId, session);\n    // Clean up expired sessions\n    cleanupExpiredSessions();\n    return sessionId;\n}\n/**\n * Validate an admin session\n */ function validateAdminSession(sessionId) {\n    const session = activeSessions.get(sessionId);\n    if (!session) {\n        return null;\n    }\n    const now = Date.now();\n    // Check if session has expired\n    if (now > session.expiresAt) {\n        activeSessions.delete(sessionId);\n        return null;\n    }\n    // Update last activity\n    session.lastActivity = now;\n    activeSessions.set(sessionId, session);\n    return session;\n}\n/**\n * Destroy an admin session\n */ function destroyAdminSession(sessionId) {\n    return activeSessions.delete(sessionId);\n}\n/**\n * Generate a secure session ID\n */ function generateSessionId() {\n    return `admin_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;\n}\n/**\n * Clean up expired sessions\n */ function cleanupExpiredSessions() {\n    const now = Date.now();\n    for (const [sessionId, session] of activeSessions.entries()){\n        if (now > session.expiresAt) {\n            activeSessions.delete(sessionId);\n        }\n    }\n}\n/**\n * Get admin password hash from environment\n * In production, this should be stored in a secure database\n */ function getAdminPasswordHash() {\n    // For backward compatibility, check if password is already hashed\n    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';\n    // If it starts with $2a$, $2b$, or $2y$, it's already a bcrypt hash\n    if (adminPassword.startsWith('$2a$') || adminPassword.startsWith('$2b$') || adminPassword.startsWith('$2y$')) {\n        return adminPassword;\n    }\n    // For development/migration: return the plain password (will be handled in auth route)\n    return adminPassword;\n}\n/**\n * Secure admin authentication\n */ async function authenticateAdmin(password) {\n    try {\n        const adminPasswordHash = getAdminPasswordHash();\n        let isValid = false;\n        // Check if stored password is hashed or plain text\n        if (adminPasswordHash.startsWith('$2a$') || adminPasswordHash.startsWith('$2b$') || adminPasswordHash.startsWith('$2y$')) {\n            // Password is hashed, use bcrypt comparison\n            isValid = await verifyPassword(password, adminPasswordHash);\n        } else {\n            // Password is plain text (development/migration), use direct comparison\n            isValid = password === adminPasswordHash;\n        }\n        if (isValid) {\n            const token = generateAdminToken();\n            const sessionId = createAdminSession();\n            return {\n                success: true,\n                token,\n                sessionId,\n                message: 'Authentication successful'\n            };\n        } else {\n            return {\n                success: false,\n                message: 'Invalid credentials'\n            };\n        }\n    } catch (error) {\n        console.error('Authentication error:', error);\n        return {\n            success: false,\n            message: 'Authentication failed'\n        };\n    }\n}\n/**\n * Middleware to verify admin authentication\n */ function verifyAdminAuth(authHeader, sessionId) {\n    // Check JWT token\n    if (authHeader && authHeader.startsWith('Bearer ')) {\n        const token = authHeader.substring(7);\n        const decoded = verifyAdminToken(token);\n        if (decoded) {\n            return {\n                isValid: true,\n                adminId: decoded.id,\n                message: 'Token authentication successful'\n            };\n        }\n    }\n    // Check session ID\n    if (sessionId) {\n        const session = validateAdminSession(sessionId);\n        if (session) {\n            return {\n                isValid: true,\n                adminId: session.id,\n                message: 'Session authentication successful'\n            };\n        }\n    }\n    return {\n        isValid: false,\n        message: 'Authentication required'\n    };\n}\n/**\n * Rate limiting for authentication attempts\n */ const authAttempts = new Map();\nconst MAX_AUTH_ATTEMPTS = 5;\nconst AUTH_LOCKOUT_TIME = 15 * 60 * 1000; // 15 minutes\nfunction checkAuthRateLimit(ip) {\n    const now = Date.now();\n    const attempts = authAttempts.get(ip);\n    if (!attempts) {\n        authAttempts.set(ip, {\n            count: 1,\n            lastAttempt: now\n        });\n        return {\n            allowed: true,\n            remainingAttempts: MAX_AUTH_ATTEMPTS - 1\n        };\n    }\n    // Reset if lockout time has passed\n    if (now - attempts.lastAttempt > AUTH_LOCKOUT_TIME) {\n        authAttempts.set(ip, {\n            count: 1,\n            lastAttempt: now\n        });\n        return {\n            allowed: true,\n            remainingAttempts: MAX_AUTH_ATTEMPTS - 1\n        };\n    }\n    // Check if max attempts exceeded\n    if (attempts.count >= MAX_AUTH_ATTEMPTS) {\n        const lockoutTime = AUTH_LOCKOUT_TIME - (now - attempts.lastAttempt);\n        return {\n            allowed: false,\n            remainingAttempts: 0,\n            lockoutTime\n        };\n    }\n    // Increment attempt count\n    attempts.count++;\n    attempts.lastAttempt = now;\n    authAttempts.set(ip, attempts);\n    return {\n        allowed: true,\n        remainingAttempts: MAX_AUTH_ATTEMPTS - attempts.count\n    };\n}\nfunction resetAuthRateLimit(ip) {\n    authAttempts.delete(ip);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/crmStorage.ts":
/*!*******************************!*\
  !*** ./src/lib/crmStorage.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCustomerActivity: () => (/* binding */ createCustomerActivity),\n/* harmony export */   createFollowUp: () => (/* binding */ createFollowUp),\n/* harmony export */   createInteraction: () => (/* binding */ createInteraction),\n/* harmony export */   createLead: () => (/* binding */ createLead),\n/* harmony export */   deleteLead: () => (/* binding */ deleteLead),\n/* harmony export */   getActivitiesByCustomerId: () => (/* binding */ getActivitiesByCustomerId),\n/* harmony export */   getAllCustomers: () => (/* binding */ getAllCustomers),\n/* harmony export */   getAllFollowUps: () => (/* binding */ getAllFollowUps),\n/* harmony export */   getAllInteractions: () => (/* binding */ getAllInteractions),\n/* harmony export */   getAllLeads: () => (/* binding */ getAllLeads),\n/* harmony export */   getCustomerByEmail: () => (/* binding */ getCustomerByEmail),\n/* harmony export */   getCustomerById: () => (/* binding */ getCustomerById),\n/* harmony export */   getCustomerOverview: () => (/* binding */ getCustomerOverview),\n/* harmony export */   getFollowUpsByCustomerId: () => (/* binding */ getFollowUpsByCustomerId),\n/* harmony export */   getFollowUpsByStatus: () => (/* binding */ getFollowUpsByStatus),\n/* harmony export */   getInteractionsByCustomerId: () => (/* binding */ getInteractionsByCustomerId),\n/* harmony export */   getInteractionsByLeadId: () => (/* binding */ getInteractionsByLeadId),\n/* harmony export */   getLeadById: () => (/* binding */ getLeadById),\n/* harmony export */   getLeadsBySource: () => (/* binding */ getLeadsBySource),\n/* harmony export */   getLeadsByStatus: () => (/* binding */ getLeadsByStatus),\n/* harmony export */   getPendingFollowUps: () => (/* binding */ getPendingFollowUps),\n/* harmony export */   getRecentActivities: () => (/* binding */ getRecentActivities),\n/* harmony export */   updateCustomer: () => (/* binding */ updateCustomer),\n/* harmony export */   updateFollowUp: () => (/* binding */ updateFollowUp),\n/* harmony export */   updateLead: () => (/* binding */ updateLead),\n/* harmony export */   upsertCustomer: () => (/* binding */ upsertCustomer)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/esm/v4.js\");\n\n\n\n// File paths\nconst DATA_DIR = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'data');\nconst LEADS_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DATA_DIR, 'leads.json');\nconst CUSTOMERS_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DATA_DIR, 'customers.json');\nconst INTERACTIONS_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DATA_DIR, 'interactions.json');\nconst FOLLOWUPS_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DATA_DIR, 'followups.json');\nconst ACTIVITIES_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DATA_DIR, 'activities.json');\n// Check if running in serverless environment\nconst isServerless = process.env.VERCEL || process.env.NETLIFY || process.env.AWS_LAMBDA_FUNCTION_NAME;\n// In-memory storage for serverless environments\nlet leadsMemoryStore = [];\nlet customersMemoryStore = [];\nlet interactionsMemoryStore = [];\nlet followupsMemoryStore = [];\nlet activitiesMemoryStore = [];\n/**\n * Ensure data directory exists\n */ async function ensureDataDirectory() {\n    if (isServerless) return;\n    try {\n        await fs__WEBPACK_IMPORTED_MODULE_0__.promises.access(DATA_DIR);\n    } catch  {\n        await fs__WEBPACK_IMPORTED_MODULE_0__.promises.mkdir(DATA_DIR, {\n            recursive: true\n        });\n    }\n}\n// LEADS MANAGEMENT\n/**\n * Load leads from storage\n */ async function loadLeads() {\n    if (isServerless) {\n        return leadsMemoryStore;\n    }\n    try {\n        await ensureDataDirectory();\n        const data = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.readFile(LEADS_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        return [];\n    }\n}\n/**\n * Save leads to storage\n */ async function saveLeads(leads) {\n    if (isServerless) {\n        leadsMemoryStore = leads;\n        return;\n    }\n    await ensureDataDirectory();\n    await fs__WEBPACK_IMPORTED_MODULE_0__.promises.writeFile(LEADS_FILE, JSON.stringify(leads, null, 2));\n}\n/**\n * Create a new lead\n */ async function createLead(leadData) {\n    const leads = await loadLeads();\n    const newLead = {\n        ...leadData,\n        id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    };\n    leads.push(newLead);\n    await saveLeads(leads);\n    return newLead;\n}\n/**\n * Get all leads\n */ async function getAllLeads() {\n    return await loadLeads();\n}\n/**\n * Get lead by ID\n */ async function getLeadById(leadId) {\n    const leads = await loadLeads();\n    return leads.find((lead)=>lead.id === leadId) || null;\n}\n/**\n * Update lead\n */ async function updateLead(leadId, updates) {\n    const leads = await loadLeads();\n    const leadIndex = leads.findIndex((lead)=>lead.id === leadId);\n    if (leadIndex === -1) return false;\n    leads[leadIndex] = {\n        ...leads[leadIndex],\n        ...updates,\n        updatedAt: new Date().toISOString()\n    };\n    await saveLeads(leads);\n    return true;\n}\n/**\n * Delete lead\n */ async function deleteLead(leadId) {\n    const leads = await loadLeads();\n    const filteredLeads = leads.filter((lead)=>lead.id !== leadId);\n    if (filteredLeads.length === leads.length) return false;\n    await saveLeads(filteredLeads);\n    return true;\n}\n/**\n * Get leads by status\n */ async function getLeadsByStatus(status) {\n    const leads = await loadLeads();\n    return leads.filter((lead)=>lead.status === status);\n}\n/**\n * Get leads by source\n */ async function getLeadsBySource(source) {\n    const leads = await loadLeads();\n    return leads.filter((lead)=>lead.source === source);\n}\n// CUSTOMERS MANAGEMENT\n/**\n * Load customers from storage\n */ async function loadCustomers() {\n    if (isServerless) {\n        return customersMemoryStore;\n    }\n    try {\n        await ensureDataDirectory();\n        const data = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.readFile(CUSTOMERS_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        return [];\n    }\n}\n/**\n * Save customers to storage\n */ async function saveCustomers(customers) {\n    if (isServerless) {\n        customersMemoryStore = customers;\n        return;\n    }\n    await ensureDataDirectory();\n    await fs__WEBPACK_IMPORTED_MODULE_0__.promises.writeFile(CUSTOMERS_FILE, JSON.stringify(customers, null, 2));\n}\n/**\n * Create or update customer\n */ async function upsertCustomer(customerData) {\n    const customers = await loadCustomers();\n    // Check if customer exists by email\n    const existingCustomerIndex = customers.findIndex((c)=>c.personalInfo.email === customerData.personalInfo.email);\n    if (existingCustomerIndex !== -1) {\n        // Update existing customer\n        customers[existingCustomerIndex] = {\n            ...customers[existingCustomerIndex],\n            ...customerData,\n            updatedAt: new Date().toISOString()\n        };\n        await saveCustomers(customers);\n        return customers[existingCustomerIndex];\n    } else {\n        // Create new customer\n        const newCustomer = {\n            ...customerData,\n            id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        customers.push(newCustomer);\n        await saveCustomers(customers);\n        return newCustomer;\n    }\n}\n/**\n * Get all customers\n */ async function getAllCustomers() {\n    return await loadCustomers();\n}\n/**\n * Get customer by ID\n */ async function getCustomerById(customerId) {\n    const customers = await loadCustomers();\n    return customers.find((customer)=>customer.id === customerId) || null;\n}\n/**\n * Get customer by email\n */ async function getCustomerByEmail(email) {\n    const customers = await loadCustomers();\n    return customers.find((customer)=>customer.personalInfo.email === email) || null;\n}\n/**\n * Update customer\n */ async function updateCustomer(customerId, updates) {\n    const customers = await loadCustomers();\n    const customerIndex = customers.findIndex((customer)=>customer.id === customerId);\n    if (customerIndex === -1) return false;\n    customers[customerIndex] = {\n        ...customers[customerIndex],\n        ...updates,\n        updatedAt: new Date().toISOString()\n    };\n    await saveCustomers(customers);\n    return true;\n}\n// INTERACTIONS MANAGEMENT\n/**\n * Load interactions from storage\n */ async function loadInteractions() {\n    if (isServerless) {\n        return interactionsMemoryStore;\n    }\n    try {\n        await ensureDataDirectory();\n        const data = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.readFile(INTERACTIONS_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        return [];\n    }\n}\n/**\n * Save interactions to storage\n */ async function saveInteractions(interactions) {\n    if (isServerless) {\n        interactionsMemoryStore = interactions;\n        return;\n    }\n    await ensureDataDirectory();\n    await fs__WEBPACK_IMPORTED_MODULE_0__.promises.writeFile(INTERACTIONS_FILE, JSON.stringify(interactions, null, 2));\n}\n/**\n * Create a new interaction\n */ async function createInteraction(interactionData) {\n    const interactions = await loadInteractions();\n    const newInteraction = {\n        ...interactionData,\n        id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n        createdAt: new Date().toISOString()\n    };\n    interactions.push(newInteraction);\n    await saveInteractions(interactions);\n    return newInteraction;\n}\n/**\n * Get all interactions\n */ async function getAllInteractions() {\n    return await loadInteractions();\n}\n/**\n * Get interactions by customer ID\n */ async function getInteractionsByCustomerId(customerId) {\n    const interactions = await loadInteractions();\n    return interactions.filter((interaction)=>interaction.customerId === customerId);\n}\n/**\n * Get interactions by lead ID\n */ async function getInteractionsByLeadId(leadId) {\n    const interactions = await loadInteractions();\n    return interactions.filter((interaction)=>interaction.leadId === leadId);\n}\n// FOLLOW-UPS MANAGEMENT\n/**\n * Load follow-ups from storage\n */ async function loadFollowUps() {\n    if (isServerless) {\n        return followupsMemoryStore;\n    }\n    try {\n        await ensureDataDirectory();\n        const data = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.readFile(FOLLOWUPS_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        return [];\n    }\n}\n/**\n * Save follow-ups to storage\n */ async function saveFollowUps(followups) {\n    if (isServerless) {\n        followupsMemoryStore = followups;\n        return;\n    }\n    await ensureDataDirectory();\n    await fs__WEBPACK_IMPORTED_MODULE_0__.promises.writeFile(FOLLOWUPS_FILE, JSON.stringify(followups, null, 2));\n}\n/**\n * Create a new follow-up\n */ async function createFollowUp(followupData) {\n    const followups = await loadFollowUps();\n    const newFollowUp = {\n        ...followupData,\n        id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    };\n    followups.push(newFollowUp);\n    await saveFollowUps(followups);\n    return newFollowUp;\n}\n/**\n * Get all follow-ups\n */ async function getAllFollowUps() {\n    return await loadFollowUps();\n}\n/**\n * Get follow-ups by status\n */ async function getFollowUpsByStatus(status) {\n    const followups = await loadFollowUps();\n    return followups.filter((followup)=>followup.status === status);\n}\n/**\n * Get pending follow-ups (due now or overdue)\n */ async function getPendingFollowUps() {\n    const followups = await loadFollowUps();\n    const now = new Date().toISOString();\n    return followups.filter((followup)=>followup.status === 'pending' && followup.scheduledDate <= now);\n}\n/**\n * Update follow-up\n */ async function updateFollowUp(followupId, updates) {\n    const followups = await loadFollowUps();\n    const followupIndex = followups.findIndex((followup)=>followup.id === followupId);\n    if (followupIndex === -1) return false;\n    followups[followupIndex] = {\n        ...followups[followupIndex],\n        ...updates,\n        updatedAt: new Date().toISOString()\n    };\n    await saveFollowUps(followups);\n    return true;\n}\n/**\n * Get follow-ups by customer ID\n */ async function getFollowUpsByCustomerId(customerId) {\n    const followups = await loadFollowUps();\n    return followups.filter((followup)=>followup.customerId === customerId);\n}\n// CUSTOMER ACTIVITIES MANAGEMENT\n/**\n * Load activities from storage\n */ async function loadActivities() {\n    if (isServerless) {\n        return activitiesMemoryStore;\n    }\n    try {\n        await ensureDataDirectory();\n        const data = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.readFile(ACTIVITIES_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        return [];\n    }\n}\n/**\n * Save activities to storage\n */ async function saveActivities(activities) {\n    if (isServerless) {\n        activitiesMemoryStore = activities;\n        return;\n    }\n    await ensureDataDirectory();\n    await fs__WEBPACK_IMPORTED_MODULE_0__.promises.writeFile(ACTIVITIES_FILE, JSON.stringify(activities, null, 2));\n}\n/**\n * Create a new customer activity\n */ async function createCustomerActivity(activityData) {\n    const activities = await loadActivities();\n    const newActivity = {\n        ...activityData,\n        id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n        timestamp: new Date().toISOString()\n    };\n    activities.push(newActivity);\n    await saveActivities(activities);\n    return newActivity;\n}\n/**\n * Get activities by customer ID\n */ async function getActivitiesByCustomerId(customerId) {\n    const activities = await loadActivities();\n    return activities.filter((activity)=>activity.customerId === customerId);\n}\n/**\n * Get recent activities (last 30 days)\n */ async function getRecentActivities(days = 30) {\n    const activities = await loadActivities();\n    const cutoffDate = new Date();\n    cutoffDate.setDate(cutoffDate.getDate() - days);\n    return activities.filter((activity)=>new Date(activity.timestamp) >= cutoffDate).sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());\n}\n// UTILITY FUNCTIONS\n/**\n * Get customer overview with stats\n */ async function getCustomerOverview(customerId) {\n    const customer = await getCustomerById(customerId);\n    if (!customer) return null;\n    const interactions = await getInteractionsByCustomerId(customerId);\n    const followups = await getFollowUpsByCustomerId(customerId);\n    const activities = await getActivitiesByCustomerId(customerId);\n    return {\n        customer,\n        stats: {\n            totalInteractions: interactions.length,\n            pendingFollowUps: followups.filter((f)=>f.status === 'pending').length,\n            recentActivities: activities.filter((a)=>new Date(a.timestamp) >= new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)).length,\n            lastInteraction: interactions.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())[0]?.createdAt\n        },\n        recentInteractions: interactions.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()).slice(0, 5),\n        upcomingFollowUps: followups.filter((f)=>f.status === 'pending').sort((a, b)=>new Date(a.scheduledDate).getTime() - new Date(b.scheduledDate).getTime()).slice(0, 3)\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/crmStorage.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/orderStorage.ts":
/*!*********************************!*\
  !*** ./src/lib/orderStorage.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addShippingUpdate: () => (/* binding */ addShippingUpdate),\n/* harmony export */   createInvoice: () => (/* binding */ createInvoice),\n/* harmony export */   createOrder: () => (/* binding */ createOrder),\n/* harmony export */   getAllOrders: () => (/* binding */ getAllOrders),\n/* harmony export */   getInvoiceByOrderId: () => (/* binding */ getInvoiceByOrderId),\n/* harmony export */   getOrderById: () => (/* binding */ getOrderById),\n/* harmony export */   getOrdersByCustomerId: () => (/* binding */ getOrdersByCustomerId),\n/* harmony export */   getStorageType: () => (/* binding */ getStorageType),\n/* harmony export */   updateInvoice: () => (/* binding */ updateInvoice),\n/* harmony export */   updateOrder: () => (/* binding */ updateOrder),\n/* harmony export */   updateOrderStatus: () => (/* binding */ updateOrderStatus),\n/* harmony export */   updatePaymentStatus: () => (/* binding */ updatePaymentStatus)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/esm/v4.js\");\n\n\n\n// Check if we're in a serverless environment\nconst isServerless = process.env.VERCEL || process.env.NETLIFY || process.env.AWS_LAMBDA_FUNCTION_NAME;\n// File paths\nconst DATA_DIR = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'data');\nconst ORDERS_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DATA_DIR, 'orders.json');\nconst INVOICES_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DATA_DIR, 'invoices.json');\n// In-memory storage for serverless environments\nlet ordersMemoryStore = [];\nlet invoicesMemoryStore = [];\n/**\n * Get storage type for logging/debugging\n */ function getStorageType() {\n    return isServerless ? 'memory' : 'file';\n}\n/**\n * Ensure data directory exists\n */ async function ensureDataDirectory() {\n    if (isServerless) return;\n    try {\n        await fs__WEBPACK_IMPORTED_MODULE_0__.promises.access(DATA_DIR);\n    } catch  {\n        await fs__WEBPACK_IMPORTED_MODULE_0__.promises.mkdir(DATA_DIR, {\n            recursive: true\n        });\n    }\n}\n/**\n * Load orders from storage\n */ async function loadOrders() {\n    if (isServerless) {\n        return ordersMemoryStore;\n    }\n    try {\n        await ensureDataDirectory();\n        const data = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.readFile(ORDERS_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        // File doesn't exist or is invalid, return empty array\n        return [];\n    }\n}\n/**\n * Save orders to storage\n */ async function saveOrders(orders) {\n    if (isServerless) {\n        ordersMemoryStore = orders;\n        return;\n    }\n    await ensureDataDirectory();\n    await fs__WEBPACK_IMPORTED_MODULE_0__.promises.writeFile(ORDERS_FILE, JSON.stringify(orders, null, 2));\n}\n/**\n * Load invoices from storage\n */ async function loadInvoices() {\n    if (isServerless) {\n        return invoicesMemoryStore;\n    }\n    try {\n        await ensureDataDirectory();\n        const data = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.readFile(INVOICES_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        return [];\n    }\n}\n/**\n * Save invoices to storage\n */ async function saveInvoices(invoices) {\n    if (isServerless) {\n        invoicesMemoryStore = invoices;\n        return;\n    }\n    await ensureDataDirectory();\n    await fs__WEBPACK_IMPORTED_MODULE_0__.promises.writeFile(INVOICES_FILE, JSON.stringify(invoices, null, 2));\n}\n/**\n * Generate order number\n */ function generateOrderNumber() {\n    const timestamp = Date.now().toString();\n    const random = Math.random().toString(36).substr(2, 4).toUpperCase();\n    return `EB${timestamp.slice(-6)}${random}`;\n}\n/**\n * Generate invoice number\n */ function generateInvoiceNumber() {\n    const timestamp = Date.now().toString();\n    const random = Math.random().toString(36).substr(2, 3).toUpperCase();\n    return `INV-${timestamp.slice(-6)}-${random}`;\n}\n/**\n * Create a new order\n */ async function createOrder(orderData) {\n    const orders = await loadOrders();\n    const newOrder = {\n        ...orderData,\n        id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n        orderNumber: generateOrderNumber(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    };\n    orders.push(newOrder);\n    await saveOrders(orders);\n    return newOrder;\n}\n/**\n * Get all orders\n */ async function getAllOrders() {\n    return await loadOrders();\n}\n/**\n * Get order by ID\n */ async function getOrderById(orderId) {\n    const orders = await loadOrders();\n    return orders.find((order)=>order.id === orderId) || null;\n}\n/**\n * Get orders by customer ID\n */ async function getOrdersByCustomerId(customerId) {\n    const orders = await loadOrders();\n    return orders.filter((order)=>order.customerId === customerId);\n}\n/**\n * Update order\n */ async function updateOrder(orderId, updates) {\n    const orders = await loadOrders();\n    const orderIndex = orders.findIndex((order)=>order.id === orderId);\n    if (orderIndex === -1) {\n        return false;\n    }\n    orders[orderIndex] = {\n        ...orders[orderIndex],\n        ...updates,\n        updatedAt: new Date().toISOString()\n    };\n    await saveOrders(orders);\n    return true;\n}\n/**\n * Update order status\n */ async function updateOrderStatus(orderId, status) {\n    return await updateOrder(orderId, {\n        status\n    });\n}\n/**\n * Update payment status\n */ async function updatePaymentStatus(orderId, paymentStatus) {\n    const order = await getOrderById(orderId);\n    if (!order) return false;\n    const updatedPayment = {\n        ...order.payment,\n        ...paymentStatus\n    };\n    return await updateOrder(orderId, {\n        payment: updatedPayment\n    });\n}\n/**\n * Add shipping update\n */ async function addShippingUpdate(orderId, update) {\n    const order = await getOrderById(orderId);\n    if (!order) return false;\n    const newUpdate = {\n        ...update,\n        id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])()\n    };\n    const updatedShipping = {\n        ...order.shipping,\n        updates: [\n            ...order.shipping.updates,\n            newUpdate\n        ]\n    };\n    return await updateOrder(orderId, {\n        shipping: updatedShipping\n    });\n}\n/**\n * Create invoice\n */ async function createInvoice(invoiceData) {\n    const invoices = await loadInvoices();\n    const newInvoice = {\n        ...invoiceData,\n        id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n        invoiceNumber: generateInvoiceNumber()\n    };\n    invoices.push(newInvoice);\n    await saveInvoices(invoices);\n    return newInvoice;\n}\n/**\n * Get invoice by order ID\n */ async function getInvoiceByOrderId(orderId) {\n    const invoices = await loadInvoices();\n    return invoices.find((invoice)=>invoice.orderId === orderId) || null;\n}\n/**\n * Update invoice\n */ async function updateInvoice(invoiceId, updates) {\n    const invoices = await loadInvoices();\n    const invoiceIndex = invoices.findIndex((invoice)=>invoice.id === invoiceId);\n    if (invoiceIndex === -1) {\n        return false;\n    }\n    invoices[invoiceIndex] = {\n        ...invoices[invoiceIndex],\n        ...updates\n    };\n    await saveInvoices(invoices);\n    return true;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/orderStorage.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time","vendor-chunks/uuid"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fanalytics%2Fexport%2Froute&page=%2Fapi%2Fadmin%2Fanalytics%2Fexport%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fanalytics%2Fexport%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();