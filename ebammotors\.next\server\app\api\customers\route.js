/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/customers/route";
exports.ids = ["app/api/customers/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcustomers%2Froute&page=%2Fapi%2Fcustomers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcustomers%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcustomers%2Froute&page=%2Fapi%2Fcustomers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcustomers%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_User_Desktop_website_ebammotors_src_app_api_customers_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/customers/route.ts */ \"(rsc)/./src/app/api/customers/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/customers/route\",\n        pathname: \"/api/customers\",\n        filename: \"route\",\n        bundlePath: \"app/api/customers/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\app\\\\api\\\\customers\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_User_Desktop_website_ebammotors_src_app_api_customers_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcustomers%2Froute&page=%2Fapi%2Fcustomers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcustomers%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/customers/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/customers/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   syncCustomerFromOrder: () => (/* binding */ syncCustomerFromOrder)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_crmStorage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/crmStorage */ \"(rsc)/./src/lib/crmStorage.ts\");\n\n\n// GET - Fetch customers\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const adminKey = searchParams.get('adminKey');\n        const customerId = searchParams.get('id');\n        const email = searchParams.get('email');\n        const overview = searchParams.get('overview') === 'true';\n        // Verify admin authentication\n        const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';\n        if (adminKey !== validAdminKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // Get customer overview with stats\n        if (customerId && overview) {\n            const customerOverview = await (0,_lib_crmStorage__WEBPACK_IMPORTED_MODULE_1__.getCustomerOverview)(customerId);\n            if (!customerOverview) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    message: 'Customer not found'\n                }, {\n                    status: 404\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: customerOverview\n            });\n        }\n        // Get specific customer by ID\n        if (customerId) {\n            const customer = await (0,_lib_crmStorage__WEBPACK_IMPORTED_MODULE_1__.getCustomerById)(customerId);\n            if (!customer) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    message: 'Customer not found'\n                }, {\n                    status: 404\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                customer\n            });\n        }\n        // Get customer by email\n        if (email) {\n            const customer = await (0,_lib_crmStorage__WEBPACK_IMPORTED_MODULE_1__.getCustomerByEmail)(email);\n            if (!customer) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    message: 'Customer not found'\n                }, {\n                    status: 404\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                customer\n            });\n        }\n        // Get all customers\n        const customers = await (0,_lib_crmStorage__WEBPACK_IMPORTED_MODULE_1__.getAllCustomers)();\n        // Sort by most recent first\n        customers.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            customers\n        });\n    } catch (error) {\n        console.error('Error fetching customers:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Failed to fetch customers'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - Create or update customer\nasync function POST(request) {\n    try {\n        const customerData = await request.json();\n        // Validate required fields\n        if (!customerData.personalInfo?.name || !customerData.personalInfo?.email) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Name and email are required'\n            }, {\n                status: 400\n            });\n        }\n        // Set defaults for new customer\n        const newCustomerData = {\n            status: 'active',\n            segment: 'new',\n            loyaltyPoints: 0,\n            membershipTier: 'Bronze',\n            totalSpent: 0,\n            totalOrders: 0,\n            averageOrderValue: 0,\n            acquisitionSource: 'website',\n            tags: [],\n            preferences: {\n                notifications: {\n                    email: true,\n                    sms: false,\n                    push: true,\n                    marketing: false\n                },\n                currency: 'JPY',\n                communicationPreference: 'email'\n            },\n            address: {},\n            ...customerData\n        };\n        const customer = await (0,_lib_crmStorage__WEBPACK_IMPORTED_MODULE_1__.upsertCustomer)(newCustomerData);\n        // Log interaction for customer creation/update\n        const { createInteraction } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/crmStorage */ \"(rsc)/./src/lib/crmStorage.ts\"));\n        await createInteraction({\n            customerId: customer.id,\n            type: 'support',\n            direction: 'inbound',\n            channel: 'website',\n            content: 'Customer profile created/updated',\n            subject: 'Customer Registration',\n            tags: [\n                'customer_registration',\n                'profile_update'\n            ],\n            createdBy: 'system'\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Customer saved successfully',\n            customer\n        });\n    } catch (error) {\n        console.error('Error creating/updating customer:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Failed to save customer'\n        }, {\n            status: 500\n        });\n    }\n}\n// PATCH - Update customer\nasync function PATCH(request) {\n    try {\n        const { customerId, adminKey, ...updates } = await request.json();\n        // Verify admin authentication\n        const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';\n        if (adminKey !== validAdminKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        if (!customerId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Customer ID is required'\n            }, {\n                status: 400\n            });\n        }\n        const success = await (0,_lib_crmStorage__WEBPACK_IMPORTED_MODULE_1__.updateCustomer)(customerId, updates);\n        if (!success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Customer not found'\n            }, {\n                status: 404\n            });\n        }\n        // Log interaction for customer update\n        const { createInteraction } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/crmStorage */ \"(rsc)/./src/lib/crmStorage.ts\"));\n        await createInteraction({\n            customerId,\n            type: 'support',\n            direction: 'outbound',\n            channel: 'website',\n            content: `Customer profile updated: ${Object.keys(updates).join(', ')}`,\n            subject: 'Profile Update',\n            tags: [\n                'profile_update',\n                'admin_action'\n            ],\n            createdBy: 'admin'\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Customer updated successfully'\n        });\n    } catch (error) {\n        console.error('Error updating customer:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Failed to update customer'\n        }, {\n            status: 500\n        });\n    }\n}\n// Utility function to sync customer data from orders\nasync function syncCustomerFromOrder(orderData) {\n    try {\n        const customerData = {\n            personalInfo: {\n                name: orderData.customerInfo.name,\n                email: orderData.customerInfo.email,\n                phone: orderData.customerInfo.phone\n            },\n            address: orderData.customerInfo.address,\n            preferences: {\n                notifications: {\n                    email: true,\n                    sms: false,\n                    push: true,\n                    marketing: false\n                },\n                currency: orderData.currency || 'JPY',\n                communicationPreference: 'email'\n            },\n            status: 'active',\n            segment: 'regular',\n            loyaltyPoints: 0,\n            membershipTier: 'Bronze',\n            totalSpent: orderData.totalAmount,\n            totalOrders: 1,\n            averageOrderValue: orderData.totalAmount,\n            acquisitionSource: 'order',\n            tags: [\n                'customer'\n            ]\n        };\n        const customer = await (0,_lib_crmStorage__WEBPACK_IMPORTED_MODULE_1__.upsertCustomer)(customerData);\n        // Update customer stats if they already existed\n        if (customer) {\n            const existingCustomer = await (0,_lib_crmStorage__WEBPACK_IMPORTED_MODULE_1__.getCustomerById)(customer.id);\n            if (existingCustomer && existingCustomer.totalOrders > 0) {\n                await (0,_lib_crmStorage__WEBPACK_IMPORTED_MODULE_1__.updateCustomer)(customer.id, {\n                    totalSpent: existingCustomer.totalSpent + orderData.totalAmount,\n                    totalOrders: existingCustomer.totalOrders + 1,\n                    averageOrderValue: (existingCustomer.totalSpent + orderData.totalAmount) / (existingCustomer.totalOrders + 1),\n                    lastOrderDate: new Date().toISOString()\n                });\n            }\n        }\n        return customer;\n    } catch (error) {\n        console.error('Error syncing customer from order:', error);\n        return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/customers/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/crmStorage.ts":
/*!*******************************!*\
  !*** ./src/lib/crmStorage.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCustomerActivity: () => (/* binding */ createCustomerActivity),\n/* harmony export */   createFollowUp: () => (/* binding */ createFollowUp),\n/* harmony export */   createInteraction: () => (/* binding */ createInteraction),\n/* harmony export */   createLead: () => (/* binding */ createLead),\n/* harmony export */   deleteLead: () => (/* binding */ deleteLead),\n/* harmony export */   getActivitiesByCustomerId: () => (/* binding */ getActivitiesByCustomerId),\n/* harmony export */   getAllCustomers: () => (/* binding */ getAllCustomers),\n/* harmony export */   getAllFollowUps: () => (/* binding */ getAllFollowUps),\n/* harmony export */   getAllInteractions: () => (/* binding */ getAllInteractions),\n/* harmony export */   getAllLeads: () => (/* binding */ getAllLeads),\n/* harmony export */   getCustomerByEmail: () => (/* binding */ getCustomerByEmail),\n/* harmony export */   getCustomerById: () => (/* binding */ getCustomerById),\n/* harmony export */   getCustomerOverview: () => (/* binding */ getCustomerOverview),\n/* harmony export */   getFollowUpsByCustomerId: () => (/* binding */ getFollowUpsByCustomerId),\n/* harmony export */   getFollowUpsByStatus: () => (/* binding */ getFollowUpsByStatus),\n/* harmony export */   getInteractionsByCustomerId: () => (/* binding */ getInteractionsByCustomerId),\n/* harmony export */   getInteractionsByLeadId: () => (/* binding */ getInteractionsByLeadId),\n/* harmony export */   getLeadById: () => (/* binding */ getLeadById),\n/* harmony export */   getLeadsBySource: () => (/* binding */ getLeadsBySource),\n/* harmony export */   getLeadsByStatus: () => (/* binding */ getLeadsByStatus),\n/* harmony export */   getPendingFollowUps: () => (/* binding */ getPendingFollowUps),\n/* harmony export */   getRecentActivities: () => (/* binding */ getRecentActivities),\n/* harmony export */   updateCustomer: () => (/* binding */ updateCustomer),\n/* harmony export */   updateFollowUp: () => (/* binding */ updateFollowUp),\n/* harmony export */   updateLead: () => (/* binding */ updateLead),\n/* harmony export */   upsertCustomer: () => (/* binding */ upsertCustomer)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/esm/v4.js\");\n\n\n\n// File paths\nconst DATA_DIR = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'data');\nconst LEADS_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DATA_DIR, 'leads.json');\nconst CUSTOMERS_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DATA_DIR, 'customers.json');\nconst INTERACTIONS_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DATA_DIR, 'interactions.json');\nconst FOLLOWUPS_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DATA_DIR, 'followups.json');\nconst ACTIVITIES_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DATA_DIR, 'activities.json');\n// Check if running in serverless environment\nconst isServerless = process.env.VERCEL || process.env.NETLIFY || process.env.AWS_LAMBDA_FUNCTION_NAME;\n// In-memory storage for serverless environments\nlet leadsMemoryStore = [];\nlet customersMemoryStore = [];\nlet interactionsMemoryStore = [];\nlet followupsMemoryStore = [];\nlet activitiesMemoryStore = [];\n/**\n * Ensure data directory exists\n */ async function ensureDataDirectory() {\n    if (isServerless) return;\n    try {\n        await fs__WEBPACK_IMPORTED_MODULE_0__.promises.access(DATA_DIR);\n    } catch  {\n        await fs__WEBPACK_IMPORTED_MODULE_0__.promises.mkdir(DATA_DIR, {\n            recursive: true\n        });\n    }\n}\n// LEADS MANAGEMENT\n/**\n * Load leads from storage\n */ async function loadLeads() {\n    if (isServerless) {\n        return leadsMemoryStore;\n    }\n    try {\n        await ensureDataDirectory();\n        const data = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.readFile(LEADS_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        return [];\n    }\n}\n/**\n * Save leads to storage\n */ async function saveLeads(leads) {\n    if (isServerless) {\n        leadsMemoryStore = leads;\n        return;\n    }\n    await ensureDataDirectory();\n    await fs__WEBPACK_IMPORTED_MODULE_0__.promises.writeFile(LEADS_FILE, JSON.stringify(leads, null, 2));\n}\n/**\n * Create a new lead\n */ async function createLead(leadData) {\n    const leads = await loadLeads();\n    const newLead = {\n        ...leadData,\n        id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    };\n    leads.push(newLead);\n    await saveLeads(leads);\n    return newLead;\n}\n/**\n * Get all leads\n */ async function getAllLeads() {\n    return await loadLeads();\n}\n/**\n * Get lead by ID\n */ async function getLeadById(leadId) {\n    const leads = await loadLeads();\n    return leads.find((lead)=>lead.id === leadId) || null;\n}\n/**\n * Update lead\n */ async function updateLead(leadId, updates) {\n    const leads = await loadLeads();\n    const leadIndex = leads.findIndex((lead)=>lead.id === leadId);\n    if (leadIndex === -1) return false;\n    leads[leadIndex] = {\n        ...leads[leadIndex],\n        ...updates,\n        updatedAt: new Date().toISOString()\n    };\n    await saveLeads(leads);\n    return true;\n}\n/**\n * Delete lead\n */ async function deleteLead(leadId) {\n    const leads = await loadLeads();\n    const filteredLeads = leads.filter((lead)=>lead.id !== leadId);\n    if (filteredLeads.length === leads.length) return false;\n    await saveLeads(filteredLeads);\n    return true;\n}\n/**\n * Get leads by status\n */ async function getLeadsByStatus(status) {\n    const leads = await loadLeads();\n    return leads.filter((lead)=>lead.status === status);\n}\n/**\n * Get leads by source\n */ async function getLeadsBySource(source) {\n    const leads = await loadLeads();\n    return leads.filter((lead)=>lead.source === source);\n}\n// CUSTOMERS MANAGEMENT\n/**\n * Load customers from storage\n */ async function loadCustomers() {\n    if (isServerless) {\n        return customersMemoryStore;\n    }\n    try {\n        await ensureDataDirectory();\n        const data = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.readFile(CUSTOMERS_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        return [];\n    }\n}\n/**\n * Save customers to storage\n */ async function saveCustomers(customers) {\n    if (isServerless) {\n        customersMemoryStore = customers;\n        return;\n    }\n    await ensureDataDirectory();\n    await fs__WEBPACK_IMPORTED_MODULE_0__.promises.writeFile(CUSTOMERS_FILE, JSON.stringify(customers, null, 2));\n}\n/**\n * Create or update customer\n */ async function upsertCustomer(customerData) {\n    const customers = await loadCustomers();\n    // Check if customer exists by email\n    const existingCustomerIndex = customers.findIndex((c)=>c.personalInfo.email === customerData.personalInfo.email);\n    if (existingCustomerIndex !== -1) {\n        // Update existing customer\n        customers[existingCustomerIndex] = {\n            ...customers[existingCustomerIndex],\n            ...customerData,\n            updatedAt: new Date().toISOString()\n        };\n        await saveCustomers(customers);\n        return customers[existingCustomerIndex];\n    } else {\n        // Create new customer\n        const newCustomer = {\n            ...customerData,\n            id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        customers.push(newCustomer);\n        await saveCustomers(customers);\n        return newCustomer;\n    }\n}\n/**\n * Get all customers\n */ async function getAllCustomers() {\n    return await loadCustomers();\n}\n/**\n * Get customer by ID\n */ async function getCustomerById(customerId) {\n    const customers = await loadCustomers();\n    return customers.find((customer)=>customer.id === customerId) || null;\n}\n/**\n * Get customer by email\n */ async function getCustomerByEmail(email) {\n    const customers = await loadCustomers();\n    return customers.find((customer)=>customer.personalInfo.email === email) || null;\n}\n/**\n * Update customer\n */ async function updateCustomer(customerId, updates) {\n    const customers = await loadCustomers();\n    const customerIndex = customers.findIndex((customer)=>customer.id === customerId);\n    if (customerIndex === -1) return false;\n    customers[customerIndex] = {\n        ...customers[customerIndex],\n        ...updates,\n        updatedAt: new Date().toISOString()\n    };\n    await saveCustomers(customers);\n    return true;\n}\n// INTERACTIONS MANAGEMENT\n/**\n * Load interactions from storage\n */ async function loadInteractions() {\n    if (isServerless) {\n        return interactionsMemoryStore;\n    }\n    try {\n        await ensureDataDirectory();\n        const data = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.readFile(INTERACTIONS_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        return [];\n    }\n}\n/**\n * Save interactions to storage\n */ async function saveInteractions(interactions) {\n    if (isServerless) {\n        interactionsMemoryStore = interactions;\n        return;\n    }\n    await ensureDataDirectory();\n    await fs__WEBPACK_IMPORTED_MODULE_0__.promises.writeFile(INTERACTIONS_FILE, JSON.stringify(interactions, null, 2));\n}\n/**\n * Create a new interaction\n */ async function createInteraction(interactionData) {\n    const interactions = await loadInteractions();\n    const newInteraction = {\n        ...interactionData,\n        id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n        createdAt: new Date().toISOString()\n    };\n    interactions.push(newInteraction);\n    await saveInteractions(interactions);\n    return newInteraction;\n}\n/**\n * Get all interactions\n */ async function getAllInteractions() {\n    return await loadInteractions();\n}\n/**\n * Get interactions by customer ID\n */ async function getInteractionsByCustomerId(customerId) {\n    const interactions = await loadInteractions();\n    return interactions.filter((interaction)=>interaction.customerId === customerId);\n}\n/**\n * Get interactions by lead ID\n */ async function getInteractionsByLeadId(leadId) {\n    const interactions = await loadInteractions();\n    return interactions.filter((interaction)=>interaction.leadId === leadId);\n}\n// FOLLOW-UPS MANAGEMENT\n/**\n * Load follow-ups from storage\n */ async function loadFollowUps() {\n    if (isServerless) {\n        return followupsMemoryStore;\n    }\n    try {\n        await ensureDataDirectory();\n        const data = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.readFile(FOLLOWUPS_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        return [];\n    }\n}\n/**\n * Save follow-ups to storage\n */ async function saveFollowUps(followups) {\n    if (isServerless) {\n        followupsMemoryStore = followups;\n        return;\n    }\n    await ensureDataDirectory();\n    await fs__WEBPACK_IMPORTED_MODULE_0__.promises.writeFile(FOLLOWUPS_FILE, JSON.stringify(followups, null, 2));\n}\n/**\n * Create a new follow-up\n */ async function createFollowUp(followupData) {\n    const followups = await loadFollowUps();\n    const newFollowUp = {\n        ...followupData,\n        id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    };\n    followups.push(newFollowUp);\n    await saveFollowUps(followups);\n    return newFollowUp;\n}\n/**\n * Get all follow-ups\n */ async function getAllFollowUps() {\n    return await loadFollowUps();\n}\n/**\n * Get follow-ups by status\n */ async function getFollowUpsByStatus(status) {\n    const followups = await loadFollowUps();\n    return followups.filter((followup)=>followup.status === status);\n}\n/**\n * Get pending follow-ups (due now or overdue)\n */ async function getPendingFollowUps() {\n    const followups = await loadFollowUps();\n    const now = new Date().toISOString();\n    return followups.filter((followup)=>followup.status === 'pending' && followup.scheduledDate <= now);\n}\n/**\n * Update follow-up\n */ async function updateFollowUp(followupId, updates) {\n    const followups = await loadFollowUps();\n    const followupIndex = followups.findIndex((followup)=>followup.id === followupId);\n    if (followupIndex === -1) return false;\n    followups[followupIndex] = {\n        ...followups[followupIndex],\n        ...updates,\n        updatedAt: new Date().toISOString()\n    };\n    await saveFollowUps(followups);\n    return true;\n}\n/**\n * Get follow-ups by customer ID\n */ async function getFollowUpsByCustomerId(customerId) {\n    const followups = await loadFollowUps();\n    return followups.filter((followup)=>followup.customerId === customerId);\n}\n// CUSTOMER ACTIVITIES MANAGEMENT\n/**\n * Load activities from storage\n */ async function loadActivities() {\n    if (isServerless) {\n        return activitiesMemoryStore;\n    }\n    try {\n        await ensureDataDirectory();\n        const data = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.readFile(ACTIVITIES_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        return [];\n    }\n}\n/**\n * Save activities to storage\n */ async function saveActivities(activities) {\n    if (isServerless) {\n        activitiesMemoryStore = activities;\n        return;\n    }\n    await ensureDataDirectory();\n    await fs__WEBPACK_IMPORTED_MODULE_0__.promises.writeFile(ACTIVITIES_FILE, JSON.stringify(activities, null, 2));\n}\n/**\n * Create a new customer activity\n */ async function createCustomerActivity(activityData) {\n    const activities = await loadActivities();\n    const newActivity = {\n        ...activityData,\n        id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n        timestamp: new Date().toISOString()\n    };\n    activities.push(newActivity);\n    await saveActivities(activities);\n    return newActivity;\n}\n/**\n * Get activities by customer ID\n */ async function getActivitiesByCustomerId(customerId) {\n    const activities = await loadActivities();\n    return activities.filter((activity)=>activity.customerId === customerId);\n}\n/**\n * Get recent activities (last 30 days)\n */ async function getRecentActivities(days = 30) {\n    const activities = await loadActivities();\n    const cutoffDate = new Date();\n    cutoffDate.setDate(cutoffDate.getDate() - days);\n    return activities.filter((activity)=>new Date(activity.timestamp) >= cutoffDate).sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());\n}\n// UTILITY FUNCTIONS\n/**\n * Get customer overview with stats\n */ async function getCustomerOverview(customerId) {\n    const customer = await getCustomerById(customerId);\n    if (!customer) return null;\n    const interactions = await getInteractionsByCustomerId(customerId);\n    const followups = await getFollowUpsByCustomerId(customerId);\n    const activities = await getActivitiesByCustomerId(customerId);\n    return {\n        customer,\n        stats: {\n            totalInteractions: interactions.length,\n            pendingFollowUps: followups.filter((f)=>f.status === 'pending').length,\n            recentActivities: activities.filter((a)=>new Date(a.timestamp) >= new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)).length,\n            lastInteraction: interactions.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())[0]?.createdAt\n        },\n        recentInteractions: interactions.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()).slice(0, 5),\n        upcomingFollowUps: followups.filter((f)=>f.status === 'pending').sort((a, b)=>new Date(a.scheduledDate).getTime() - new Date(b.scheduledDate).getTime()).slice(0, 3)\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/crmStorage.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/uuid"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcustomers%2Froute&page=%2Fapi%2Fcustomers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcustomers%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();