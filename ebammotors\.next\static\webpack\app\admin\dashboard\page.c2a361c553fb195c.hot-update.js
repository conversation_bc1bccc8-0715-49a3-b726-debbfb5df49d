"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/admin/AdminDashboardLayout.tsx":
/*!*******************************************************!*\
  !*** ./src/components/admin/AdminDashboardLayout.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminDashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Car,LayoutDashboard,LogOut,Menu,MessageSquare,Search,Settings,Shield,ShoppingCart,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Car,LayoutDashboard,LogOut,Menu,MessageSquare,Search,Settings,Shield,ShoppingCart,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/car.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Car,LayoutDashboard,LogOut,Menu,MessageSquare,Search,Settings,Shield,ShoppingCart,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Car,LayoutDashboard,LogOut,Menu,MessageSquare,Search,Settings,Shield,ShoppingCart,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Car,LayoutDashboard,LogOut,Menu,MessageSquare,Search,Settings,Shield,ShoppingCart,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Car,LayoutDashboard,LogOut,Menu,MessageSquare,Search,Settings,Shield,ShoppingCart,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Car,LayoutDashboard,LogOut,Menu,MessageSquare,Search,Settings,Shield,ShoppingCart,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Car,LayoutDashboard,LogOut,Menu,MessageSquare,Search,Settings,Shield,ShoppingCart,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Car,LayoutDashboard,LogOut,Menu,MessageSquare,Search,Settings,Shield,ShoppingCart,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Car,LayoutDashboard,LogOut,Menu,MessageSquare,Search,Settings,Shield,ShoppingCart,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Car,LayoutDashboard,LogOut,Menu,MessageSquare,Search,Settings,Shield,ShoppingCart,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Car,LayoutDashboard,LogOut,Menu,MessageSquare,Search,Settings,Shield,ShoppingCart,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Car,LayoutDashboard,LogOut,Menu,MessageSquare,Search,Settings,Shield,ShoppingCart,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Car,LayoutDashboard,LogOut,Menu,MessageSquare,Search,Settings,Shield,ShoppingCart,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Car,LayoutDashboard,LogOut,Menu,MessageSquare,Search,Settings,Shield,ShoppingCart,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _AdminAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AdminAuth */ \"(app-pages-browser)/./src/components/admin/AdminAuth.tsx\");\n/* harmony import */ var _DashboardOverview__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./DashboardOverview */ \"(app-pages-browser)/./src/components/admin/DashboardOverview.tsx\");\n/* harmony import */ var _SystemHealthMonitoring__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./SystemHealthMonitoring */ \"(app-pages-browser)/./src/components/admin/SystemHealthMonitoring.tsx\");\n/* harmony import */ var _CarManagement__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CarManagement */ \"(app-pages-browser)/./src/components/admin/CarManagement.tsx\");\n/* harmony import */ var _ReviewModeration__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ReviewModeration */ \"(app-pages-browser)/./src/components/admin/ReviewModeration.tsx\");\n/* harmony import */ var _CRMManagement__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CRMManagement */ \"(app-pages-browser)/./src/components/admin/CRMManagement.tsx\");\n/* harmony import */ var _OrderManagement__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./OrderManagement */ \"(app-pages-browser)/./src/components/admin/OrderManagement.tsx\");\n/* harmony import */ var _SystemAnalytics__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./SystemAnalytics */ \"(app-pages-browser)/./src/components/admin/SystemAnalytics.tsx\");\n/* harmony import */ var _SecurityManagement__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./SecurityManagement */ \"(app-pages-browser)/./src/components/admin/SecurityManagement.tsx\");\n/* harmony import */ var _AdminSettings__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./AdminSettings */ \"(app-pages-browser)/./src/components/admin/AdminSettings.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst sidebarItems = [\n    {\n        id: 'overview',\n        label: 'Overview',\n        icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        path: '/admin/dashboard'\n    },\n    {\n        id: 'cars',\n        label: 'Car Management',\n        icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        path: '/admin/cars'\n    },\n    {\n        id: 'orders',\n        label: 'Orders',\n        icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        path: '/admin/orders'\n    },\n    {\n        id: 'customers',\n        label: 'CRM',\n        icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        path: '/admin/customers'\n    },\n    {\n        id: 'reviews',\n        label: 'Reviews',\n        icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n        path: '/admin/reviews'\n    },\n    {\n        id: 'analytics',\n        label: 'Analytics',\n        icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n        path: '/admin/analytics'\n    },\n    {\n        id: 'health',\n        label: 'System Health',\n        icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n        path: '/admin/health'\n    },\n    {\n        id: 'security',\n        label: 'Security',\n        icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n        path: '/admin/security'\n    },\n    {\n        id: 'settings',\n        label: 'Settings',\n        icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n        path: '/admin/settings'\n    }\n];\nfunction AdminDashboardLayout() {\n    var _sidebarItems_find, _sidebarItems_find1;\n    _s();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [authenticated, setAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(3);\n    const [showNotifications, setShowNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const notificationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboardLayout.useEffect\": ()=>{\n            setMounted(true);\n            // Check if user is already authenticated\n            if (true) {\n                const token = localStorage.getItem('admin_token');\n                if (token) {\n                    setAuthenticated(true);\n                }\n            }\n            // Check for section parameter in URL\n            const section = searchParams.get('section');\n            if (section && sidebarItems.find({\n                \"AdminDashboardLayout.useEffect\": (item)=>item.id === section\n            }[\"AdminDashboardLayout.useEffect\"])) {\n                setActiveSection(section);\n            }\n        }\n    }[\"AdminDashboardLayout.useEffect\"], [\n        searchParams\n    ]);\n    // Close notifications when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboardLayout.useEffect\": ()=>{\n            function handleClickOutside(event) {\n                if (notificationRef.current && !notificationRef.current.contains(event.target)) {\n                    setShowNotifications(false);\n                }\n            }\n            if (showNotifications) {\n                document.addEventListener('mousedown', handleClickOutside);\n                return ({\n                    \"AdminDashboardLayout.useEffect\": ()=>{\n                        document.removeEventListener('mousedown', handleClickOutside);\n                    }\n                })[\"AdminDashboardLayout.useEffect\"];\n            }\n        }\n    }[\"AdminDashboardLayout.useEffect\"], [\n        showNotifications\n    ]);\n    const handleLogout = ()=>{\n        if (true) {\n            localStorage.removeItem('admin_token');\n        }\n        setAuthenticated(false);\n    };\n    const handleSectionChange = (sectionId, path)=>{\n        setActiveSection(sectionId);\n        setSidebarOpen(false);\n        // Update URL with section parameter\n        const newUrl = \"/admin/dashboard?section=\".concat(sectionId);\n        router.push(newUrl);\n    };\n    const handleNotificationClick = (notificationType, notificationId)=>{\n        // Close the notifications dropdown\n        setShowNotifications(false);\n        // Navigate based on notification type\n        switch(notificationType){\n            case 'order':\n                handleSectionChange('orders', '/admin/orders');\n                break;\n            case 'review':\n                handleSectionChange('reviews', '/admin/reviews');\n                break;\n            case 'system':\n                handleSectionChange('health', '/admin/health');\n                break;\n            case 'car':\n                handleSectionChange('cars', '/admin/cars');\n                break;\n            default:\n                // For general notifications, stay on overview\n                handleSectionChange('overview', '/admin/dashboard');\n        }\n        // Optionally decrease notification count\n        if (notifications > 0) {\n            setNotifications((prev)=>prev - 1);\n        }\n    };\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                lineNumber: 144,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n            lineNumber: 143,\n            columnNumber: 7\n        }, this);\n    }\n    if (!authenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdminAuth__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            onAuthenticated: ()=>setAuthenticated(true)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n            lineNumber: 153,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform \".concat(sidebarOpen ? 'translate-x-0' : '-translate-x-full', \" transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16 px-6 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold text-gray-900\",\n                                children: \"EBAM Admin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSidebarOpen(false),\n                                className: \"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"mt-6 px-3\",\n                        children: sidebarItems.map((item)=>{\n                            const Icon = item.icon;\n                            const isActive = activeSection === item.id;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleSectionChange(item.id, item.path),\n                                className: \"w-full flex items-center px-3 py-2 mb-1 text-sm font-medium rounded-lg transition-colors \".concat(isActive ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"w-5 h-5 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 17\n                                    }, this),\n                                    item.label,\n                                    item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-auto bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full\",\n                                        children: item.badge\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, item.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col lg:ml-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"bg-white shadow-sm border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between h-16 px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSidebarOpen(true),\n                                            className: \"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4 lg:ml-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-900\",\n                                                children: ((_sidebarItems_find = sidebarItems.find((item)=>item.id === activeSection)) === null || _sidebarItems_find === void 0 ? void 0 : _sidebarItems_find.label) || 'Dashboard'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden md:block relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Search...\",\n                                                    className: \"pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            ref: notificationRef,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowNotifications(!showNotifications),\n                                                    className: \"relative p-2 text-gray-400 hover:text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                            lineNumber: 237,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        notifications > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\",\n                                                            children: notifications\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 17\n                                                }, this),\n                                                showNotifications && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 border-b border-gray-200\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-900\",\n                                                                children: \"Notifications\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                lineNumber: 249,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"max-h-96 overflow-y-auto\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleNotificationClick('order', 'ORD-001'),\n                                                                    className: \"w-full p-4 border-b border-gray-100 hover:bg-gray-50 text-left transition-colors\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-start\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-shrink-0\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                        className: \"w-4 h-4 text-blue-600\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                        lineNumber: 260,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                    lineNumber: 259,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                lineNumber: 258,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"ml-3 flex-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                                        children: \"New order received\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                        lineNumber: 264,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-600\",\n                                                                                        children: \"Order #ORD-001 from John Doe\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                        lineNumber: 265,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                                                        children: \"2 minutes ago\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                        lineNumber: 266,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                lineNumber: 263,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                        lineNumber: 257,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                    lineNumber: 253,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleNotificationClick('review', 'REV-001'),\n                                                                    className: \"w-full p-4 border-b border-gray-100 hover:bg-gray-50 text-left transition-colors\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-start\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-shrink-0\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                        className: \"w-4 h-4 text-green-600\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                        lineNumber: 278,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                    lineNumber: 277,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                lineNumber: 276,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"ml-3 flex-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                                        children: \"New review submitted\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                        lineNumber: 282,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-600\",\n                                                                                        children: \"5-star review for Toyota Voxy\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                        lineNumber: 283,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                                                        children: \"15 minutes ago\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                        lineNumber: 284,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                lineNumber: 281,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                        lineNumber: 275,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleNotificationClick('system', 'SYS-001'),\n                                                                    className: \"w-full p-4 border-b border-gray-100 hover:bg-gray-50 text-left transition-colors\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-start\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-shrink-0\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                        className: \"w-4 h-4 text-yellow-600\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                        lineNumber: 296,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                    lineNumber: 295,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                lineNumber: 294,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"ml-3 flex-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                                        children: \"System alert\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                        lineNumber: 300,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-600\",\n                                                                                        children: \"Low inventory warning for Honda Freed\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                        lineNumber: 301,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                                                        children: \"1 hour ago\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                        lineNumber: 302,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                                lineNumber: 299,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                        lineNumber: 293,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                    lineNumber: 289,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 border-t border-gray-200\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setShowNotifications(false),\n                                                                className: \"w-full text-center text-sm text-blue-600 hover:text-blue-800\",\n                                                                children: \"View all notifications\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleLogout,\n                                            className: \"flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Car_LayoutDashboard_LogOut_Menu_MessageSquare_Search_Settings_Shield_ShoppingCart_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Logout\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-6 overflow-auto\",\n                        children: [\n                            activeSection === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DashboardOverview__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 44\n                            }, this),\n                            activeSection === 'health' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SystemHealthMonitoring__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 42\n                            }, this),\n                            activeSection === 'cars' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CarManagement__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 40\n                            }, this),\n                            activeSection === 'reviews' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ReviewModeration__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 43\n                            }, this),\n                            activeSection === 'customers' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CRMManagement__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 45\n                            }, this),\n                            activeSection === 'orders' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OrderManagement__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 42\n                            }, this),\n                            activeSection === 'analytics' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SystemAnalytics__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 45\n                            }, this),\n                            activeSection === 'security' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SecurityManagement__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 44\n                            }, this),\n                            activeSection === 'settings' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdminSettings__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 44\n                            }, this),\n                            activeSection !== 'overview' && activeSection !== 'health' && activeSection !== 'cars' && activeSection !== 'reviews' && activeSection !== 'customers' && activeSection !== 'orders' && activeSection !== 'analytics' && activeSection !== 'security' && activeSection !== 'settings' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: (_sidebarItems_find1 = sidebarItems.find((item)=>item.id === activeSection)) === null || _sidebarItems_find1 === void 0 ? void 0 : _sidebarItems_find1.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"This section is under development. Please check back soon.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this),\n            sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\",\n                onClick: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n                lineNumber: 357,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\AdminDashboardLayout.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminDashboardLayout, \"YgSc9JmVtcbt04Api0L8c4fWsgw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = AdminDashboardLayout;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboardLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/AdminDashboardLayout.tsx\n"));

/***/ })

});