"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/admin/SystemHealthMonitoring.tsx":
/*!*********************************************************!*\
  !*** ./src/components/admin/SystemHealthMonitoring.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SystemHealthMonitoring)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,HardDrive,RefreshCw,Server,TrendingDown,TrendingUp,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,HardDrive,RefreshCw,Server,TrendingDown,TrendingUp,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,HardDrive,RefreshCw,Server,TrendingDown,TrendingUp,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,HardDrive,RefreshCw,Server,TrendingDown,TrendingUp,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,HardDrive,RefreshCw,Server,TrendingDown,TrendingUp,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,HardDrive,RefreshCw,Server,TrendingDown,TrendingUp,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,HardDrive,RefreshCw,Server,TrendingDown,TrendingUp,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,HardDrive,RefreshCw,Server,TrendingDown,TrendingUp,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,HardDrive,RefreshCw,Server,TrendingDown,TrendingUp,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,HardDrive,RefreshCw,Server,TrendingDown,TrendingUp,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,HardDrive,RefreshCw,Server,TrendingDown,TrendingUp,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,HardDrive,RefreshCw,Server,TrendingDown,TrendingUp,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SystemHealthMonitoring() {\n    var _healthData_database, _healthData_database1, _healthData_database2, _healthData_database3, _healthData_database4;\n    _s();\n    const [healthData, setHealthData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [autoRefresh, setAutoRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [refreshInterval, setRefreshInterval] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(30); // seconds\n    const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SystemHealthMonitoring.useEffect\": ()=>{\n            fetchHealthData();\n            if (autoRefresh) {\n                intervalRef.current = setInterval(fetchHealthData, refreshInterval * 1000);\n            }\n            return ({\n                \"SystemHealthMonitoring.useEffect\": ()=>{\n                    if (intervalRef.current) {\n                        clearInterval(intervalRef.current);\n                    }\n                }\n            })[\"SystemHealthMonitoring.useEffect\"];\n        }\n    }[\"SystemHealthMonitoring.useEffect\"], [\n        autoRefresh,\n        refreshInterval\n    ]);\n    const fetchHealthData = async ()=>{\n        try {\n            const token = localStorage.getItem('admin_token');\n            const response = await fetch('/api/admin/health', {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setHealthData(data);\n            }\n        } catch (error) {\n            console.error('Failed to fetch health data:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleRefreshToggle = ()=>{\n        setAutoRefresh(!autoRefresh);\n        if (!autoRefresh) {\n            fetchHealthData();\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'healthy':\n                return 'text-green-600 bg-green-100';\n            case 'degraded':\n                return 'text-yellow-600 bg-yellow-100';\n            case 'unhealthy':\n                return 'text-red-600 bg-red-100';\n            default:\n                return 'text-gray-600 bg-gray-100';\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'healthy':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 30\n                }, this);\n            case 'degraded':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 31\n                }, this);\n            case 'unhealthy':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 32\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const formatUptime = (seconds)=>{\n        const days = Math.floor(seconds / 86400);\n        const hours = Math.floor(seconds % 86400 / 3600);\n        const minutes = Math.floor(seconds % 3600 / 60);\n        if (days > 0) return \"\".concat(days, \"d \").concat(hours, \"h \").concat(minutes, \"m\");\n        if (hours > 0) return \"\".concat(hours, \"h \").concat(minutes, \"m\");\n        return \"\".concat(minutes, \"m\");\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-6 bg-gray-200 rounded w-1/4 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded w-3/4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded w-1/2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                lineNumber: 132,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n            lineNumber: 131,\n            columnNumber: 7\n        }, this);\n    }\n    if (!healthData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"w-12 h-12 text-red-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                        children: \"Health Data Unavailable\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"Unable to fetch system health information.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: fetchHealthData,\n                        className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                lineNumber: 148,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n            lineNumber: 147,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 rounded-lg \".concat(getStatusColor(healthData.status)),\n                                        children: getStatusIcon(healthData.status)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold text-gray-900 capitalize\",\n                                                children: [\n                                                    \"System \",\n                                                    healthData.status\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: [\n                                                    \"Last updated: \",\n                                                    new Date(healthData.timestamp).toLocaleTimeString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-gray-600 mr-2\",\n                                                children: \"Auto-refresh:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleRefreshToggle,\n                                                className: \"relative inline-flex h-6 w-11 items-center rounded-full transition-colors \".concat(autoRefresh ? 'bg-blue-600' : 'bg-gray-200'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-block h-4 w-4 transform rounded-full bg-white transition-transform \".concat(autoRefresh ? 'translate-x-6' : 'translate-x-1')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: fetchHealthData,\n                                        className: \"flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Refresh\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-5 h-5 text-gray-400 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Uptime\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-semibold\",\n                                                children: formatUptime(healthData.uptime)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-5 h-5 text-gray-400 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Requests (5m)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-semibold\",\n                                                children: healthData.stats.requests\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-5 h-5 text-gray-400 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Avg Response\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-semibold\",\n                                                children: [\n                                                    healthData.stats.averageResponseTime,\n                                                    \"ms\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this),\n                    healthData.issues.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-medium text-yellow-800 mb-2\",\n                                children: \"Active Issues\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-sm text-yellow-700 space-y-1\",\n                                children: healthData.issues.map((issue, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2 flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 19\n                                            }, this),\n                                            issue\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                children: \"Performance Metrics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Error Rate\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    healthData.stats.errorRate > 5 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4 text-red-500 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 19\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4 text-green-500 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold \".concat(healthData.stats.errorRate > 5 ? 'text-red-600' : 'text-green-600'),\n                                                        children: [\n                                                            healthData.stats.errorRate,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Slow Requests\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-gray-900\",\n                                                children: healthData.stats.slowRequests\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Total Requests\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-gray-900\",\n                                                children: healthData.stats.requests\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                children: \"Database Health\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    ((_healthData_database = healthData.database) === null || _healthData_database === void 0 ? void 0 : _healthData_database.healthy) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        className: \"w-4 h-4 text-green-500 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 19\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"w-4 h-4 text-red-500 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold \".concat(((_healthData_database1 = healthData.database) === null || _healthData_database1 === void 0 ? void 0 : _healthData_database1.healthy) ? 'text-green-600' : 'text-red-600'),\n                                                        children: ((_healthData_database2 = healthData.database) === null || _healthData_database2 === void 0 ? void 0 : _healthData_database2.healthy) ? 'Healthy' : 'Error'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 13\n                                    }, this),\n                                    ((_healthData_database3 = healthData.database) === null || _healthData_database3 === void 0 ? void 0 : _healthData_database3.latency) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Latency\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-gray-900\",\n                                                children: [\n                                                    healthData.database.latency,\n                                                    \"ms\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 15\n                                    }, this),\n                                    ((_healthData_database4 = healthData.database) === null || _healthData_database4 === void 0 ? void 0 : _healthData_database4.error) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-red-50 border border-red-200 rounded\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-700\",\n                                            children: healthData.database.error\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                lineNumber: 253,\n                columnNumber: 7\n            }, this),\n            healthData.memory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: \"Memory Usage\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-6 h-6 text-blue-500 mx-auto mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"RSS\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-semibold text-gray-900\",\n                                        children: [\n                                            healthData.memory.rss,\n                                            \" MB\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-6 h-6 text-green-500 mx-auto mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"Heap Total\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-semibold text-gray-900\",\n                                        children: [\n                                            healthData.memory.heapTotal,\n                                            \" MB\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-6 h-6 text-purple-500 mx-auto mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"Heap Used\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-semibold text-gray-900\",\n                                        children: [\n                                            healthData.memory.heapUsed,\n                                            \" MB\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-6 h-6 text-orange-500 mx-auto mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"External\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-semibold text-gray-900\",\n                                        children: [\n                                            healthData.memory.external,\n                                            \" MB\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                lineNumber: 330,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n        lineNumber: 164,\n        columnNumber: 5\n    }, this);\n}\n_s(SystemHealthMonitoring, \"KCo+lEyGSRP2xz+/fBizK7LeWS0=\");\n_c = SystemHealthMonitoring;\nvar _c;\n$RefreshReg$(_c, \"SystemHealthMonitoring\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/SystemHealthMonitoring.tsx\n"));

/***/ })

});