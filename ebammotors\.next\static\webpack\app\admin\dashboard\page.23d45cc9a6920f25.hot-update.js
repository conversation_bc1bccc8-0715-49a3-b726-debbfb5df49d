"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/admin/SystemHealthMonitoring.tsx":
/*!*********************************************************!*\
  !*** ./src/components/admin/SystemHealthMonitoring.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SystemHealthMonitoring)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,HardDrive,RefreshCw,Server,TrendingDown,TrendingUp,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,HardDrive,RefreshCw,Server,TrendingDown,TrendingUp,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,HardDrive,RefreshCw,Server,TrendingDown,TrendingUp,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,HardDrive,RefreshCw,Server,TrendingDown,TrendingUp,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,HardDrive,RefreshCw,Server,TrendingDown,TrendingUp,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,HardDrive,RefreshCw,Server,TrendingDown,TrendingUp,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,HardDrive,RefreshCw,Server,TrendingDown,TrendingUp,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,HardDrive,RefreshCw,Server,TrendingDown,TrendingUp,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,HardDrive,RefreshCw,Server,TrendingDown,TrendingUp,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,HardDrive,RefreshCw,Server,TrendingDown,TrendingUp,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,HardDrive,RefreshCw,Server,TrendingDown,TrendingUp,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,HardDrive,RefreshCw,Server,TrendingDown,TrendingUp,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SystemHealthMonitoring() {\n    var _healthData_database, _healthData_database1, _healthData_database2, _healthData_database3, _healthData_database4;\n    _s();\n    const [healthData, setHealthData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [autoRefresh, setAutoRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [refreshInterval, setRefreshInterval] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(30); // seconds\n    const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SystemHealthMonitoring.useEffect\": ()=>{\n            fetchHealthData();\n            if (autoRefresh) {\n                intervalRef.current = setInterval(fetchHealthData, refreshInterval * 1000);\n            }\n            return ({\n                \"SystemHealthMonitoring.useEffect\": ()=>{\n                    if (intervalRef.current) {\n                        clearInterval(intervalRef.current);\n                    }\n                }\n            })[\"SystemHealthMonitoring.useEffect\"];\n        }\n    }[\"SystemHealthMonitoring.useEffect\"], [\n        autoRefresh,\n        refreshInterval\n    ]);\n    const fetchHealthData = async ()=>{\n        try {\n            const token = localStorage.getItem('admin_token');\n            const response = await fetch('/api/admin/health', {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setHealthData(data);\n            }\n        } catch (error) {\n            console.error('Failed to fetch health data:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleRefreshToggle = ()=>{\n        setAutoRefresh(!autoRefresh);\n        if (!autoRefresh) {\n            fetchHealthData();\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'healthy':\n                return 'text-green-600 bg-green-100';\n            case 'degraded':\n                return 'text-yellow-600 bg-yellow-100';\n            case 'unhealthy':\n                return 'text-red-600 bg-red-100';\n            default:\n                return 'text-gray-600 bg-gray-100';\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'healthy':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 30\n                }, this);\n            case 'degraded':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 31\n                }, this);\n            case 'unhealthy':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 32\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const formatUptime = (seconds)=>{\n        const days = Math.floor(seconds / 86400);\n        const hours = Math.floor(seconds % 86400 / 3600);\n        const minutes = Math.floor(seconds % 3600 / 60);\n        if (days > 0) return \"\".concat(days, \"d \").concat(hours, \"h \").concat(minutes, \"m\");\n        if (hours > 0) return \"\".concat(hours, \"h \").concat(minutes, \"m\");\n        return \"\".concat(minutes, \"m\");\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-6 bg-gray-200 rounded w-1/4 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded w-3/4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded w-1/2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                lineNumber: 132,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n            lineNumber: 131,\n            columnNumber: 7\n        }, this);\n    }\n    if (!healthData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"w-12 h-12 text-red-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                        children: \"Health Data Unavailable\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"Unable to fetch system health information.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: fetchHealthData,\n                        className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                lineNumber: 148,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n            lineNumber: 147,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 rounded-lg \".concat(getStatusColor(healthData.status)),\n                                        children: getStatusIcon(healthData.status)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold text-gray-900 capitalize\",\n                                                children: [\n                                                    \"System \",\n                                                    healthData.status\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: [\n                                                    \"Last updated: \",\n                                                    new Date(healthData.timestamp).toLocaleTimeString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-gray-600 mr-2\",\n                                                children: \"Auto-refresh:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleRefreshToggle,\n                                                className: \"relative inline-flex h-6 w-11 items-center rounded-full transition-colors \".concat(autoRefresh ? 'bg-blue-600' : 'bg-gray-200'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-block h-4 w-4 transform rounded-full bg-white transition-transform \".concat(autoRefresh ? 'translate-x-6' : 'translate-x-1')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: fetchHealthData,\n                                        className: \"flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Refresh\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-5 h-5 text-gray-400 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Uptime\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-semibold\",\n                                                children: formatUptime(healthData.uptime)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-5 h-5 text-gray-400 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Requests (5m)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-semibold\",\n                                                children: healthData.stats.requests\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-5 h-5 text-gray-400 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Avg Response\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-semibold\",\n                                                children: [\n                                                    healthData.stats.averageResponseTime,\n                                                    \"ms\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this),\n                    healthData.issues.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-medium text-yellow-800 mb-2\",\n                                children: \"Active Issues\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-sm text-yellow-700 space-y-1\",\n                                children: healthData.issues.map((issue, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2 flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 19\n                                            }, this),\n                                            issue\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                children: \"Performance Metrics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Error Rate\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    healthData.stats.errorRate > 5 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4 text-red-500 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 19\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4 text-green-500 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold \".concat(healthData.stats.errorRate > 5 ? 'text-red-600' : 'text-green-600'),\n                                                        children: [\n                                                            healthData.stats.errorRate,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Slow Requests\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-gray-900\",\n                                                children: healthData.stats.slowRequests\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Total Requests\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-gray-900\",\n                                                children: healthData.stats.requests\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                children: \"Database Health\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    ((_healthData_database = healthData.database) === null || _healthData_database === void 0 ? void 0 : _healthData_database.healthy) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        className: \"w-4 h-4 text-green-500 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 19\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"w-4 h-4 text-red-500 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold \".concat(((_healthData_database1 = healthData.database) === null || _healthData_database1 === void 0 ? void 0 : _healthData_database1.healthy) ? 'text-green-600' : 'text-red-600'),\n                                                        children: ((_healthData_database2 = healthData.database) === null || _healthData_database2 === void 0 ? void 0 : _healthData_database2.healthy) ? 'Healthy' : 'Error'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 13\n                                    }, this),\n                                    ((_healthData_database3 = healthData.database) === null || _healthData_database3 === void 0 ? void 0 : _healthData_database3.latency) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Latency\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-gray-900\",\n                                                children: [\n                                                    healthData.database.latency,\n                                                    \"ms\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 15\n                                    }, this),\n                                    ((_healthData_database4 = healthData.database) === null || _healthData_database4 === void 0 ? void 0 : _healthData_database4.error) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-red-50 border border-red-200 rounded\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-700\",\n                                            children: healthData.database.error\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                lineNumber: 253,\n                columnNumber: 7\n            }, this),\n            healthData.memory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: \"Memory Usage\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Memory, {\n                                        className: \"w-6 h-6 text-blue-500 mx-auto mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"RSS\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-semibold text-gray-900\",\n                                        children: [\n                                            healthData.memory.rss,\n                                            \" MB\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-6 h-6 text-green-500 mx-auto mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"Heap Total\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-semibold text-gray-900\",\n                                        children: [\n                                            healthData.memory.heapTotal,\n                                            \" MB\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-6 h-6 text-purple-500 mx-auto mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"Heap Used\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-semibold text-gray-900\",\n                                        children: [\n                                            healthData.memory.heapUsed,\n                                            \" MB\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_HardDrive_RefreshCw_Server_TrendingDown_TrendingUp_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-6 h-6 text-orange-500 mx-auto mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"External\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-semibold text-gray-900\",\n                                        children: [\n                                            healthData.memory.external,\n                                            \" MB\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n                lineNumber: 330,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\admin\\\\SystemHealthMonitoring.tsx\",\n        lineNumber: 164,\n        columnNumber: 5\n    }, this);\n}\n_s(SystemHealthMonitoring, \"KCo+lEyGSRP2xz+/fBizK7LeWS0=\");\n_c = SystemHealthMonitoring;\nvar _c;\n$RefreshReg$(_c, \"SystemHealthMonitoring\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/SystemHealthMonitoring.tsx\n"));

/***/ })

});