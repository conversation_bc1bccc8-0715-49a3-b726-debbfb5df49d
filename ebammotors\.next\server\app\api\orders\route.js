/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/orders/route";
exports.ids = ["app/api/orders/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Forders%2Froute&page=%2Fapi%2Forders%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Forders%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Forders%2Froute&page=%2Fapi%2Forders%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Forders%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_User_Desktop_website_ebammotors_src_app_api_orders_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/orders/route.ts */ \"(rsc)/./src/app/api/orders/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/orders/route\",\n        pathname: \"/api/orders\",\n        filename: \"route\",\n        bundlePath: \"app/api/orders/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\app\\\\api\\\\orders\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_User_Desktop_website_ebammotors_src_app_api_orders_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Forders%2Froute&page=%2Fapi%2Forders%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Forders%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/customers/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/customers/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   syncCustomerFromOrder: () => (/* binding */ syncCustomerFromOrder)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_crmStorage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/crmStorage */ \"(rsc)/./src/lib/crmStorage.ts\");\n\n\n// GET - Fetch customers\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const adminKey = searchParams.get('adminKey');\n        const customerId = searchParams.get('id');\n        const email = searchParams.get('email');\n        const overview = searchParams.get('overview') === 'true';\n        // Verify admin authentication\n        const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';\n        if (adminKey !== validAdminKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // Get customer overview with stats\n        if (customerId && overview) {\n            const customerOverview = await (0,_lib_crmStorage__WEBPACK_IMPORTED_MODULE_1__.getCustomerOverview)(customerId);\n            if (!customerOverview) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    message: 'Customer not found'\n                }, {\n                    status: 404\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: customerOverview\n            });\n        }\n        // Get specific customer by ID\n        if (customerId) {\n            const customer = await (0,_lib_crmStorage__WEBPACK_IMPORTED_MODULE_1__.getCustomerById)(customerId);\n            if (!customer) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    message: 'Customer not found'\n                }, {\n                    status: 404\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                customer\n            });\n        }\n        // Get customer by email\n        if (email) {\n            const customer = await (0,_lib_crmStorage__WEBPACK_IMPORTED_MODULE_1__.getCustomerByEmail)(email);\n            if (!customer) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    message: 'Customer not found'\n                }, {\n                    status: 404\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                customer\n            });\n        }\n        // Get all customers\n        const customers = await (0,_lib_crmStorage__WEBPACK_IMPORTED_MODULE_1__.getAllCustomers)();\n        // Sort by most recent first\n        customers.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            customers\n        });\n    } catch (error) {\n        console.error('Error fetching customers:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Failed to fetch customers'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - Create or update customer\nasync function POST(request) {\n    try {\n        const customerData = await request.json();\n        // Validate required fields\n        if (!customerData.personalInfo?.name || !customerData.personalInfo?.email) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Name and email are required'\n            }, {\n                status: 400\n            });\n        }\n        // Set defaults for new customer\n        const newCustomerData = {\n            status: 'active',\n            segment: 'new',\n            loyaltyPoints: 0,\n            membershipTier: 'Bronze',\n            totalSpent: 0,\n            totalOrders: 0,\n            averageOrderValue: 0,\n            acquisitionSource: 'website',\n            tags: [],\n            preferences: {\n                notifications: {\n                    email: true,\n                    sms: false,\n                    push: true,\n                    marketing: false\n                },\n                currency: 'JPY',\n                communicationPreference: 'email'\n            },\n            address: {},\n            ...customerData\n        };\n        const customer = await (0,_lib_crmStorage__WEBPACK_IMPORTED_MODULE_1__.upsertCustomer)(newCustomerData);\n        // Log interaction for customer creation/update\n        const { createInteraction } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/crmStorage */ \"(rsc)/./src/lib/crmStorage.ts\"));\n        await createInteraction({\n            customerId: customer.id,\n            type: 'support',\n            direction: 'inbound',\n            channel: 'website',\n            content: 'Customer profile created/updated',\n            subject: 'Customer Registration',\n            tags: [\n                'customer_registration',\n                'profile_update'\n            ],\n            createdBy: 'system'\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Customer saved successfully',\n            customer\n        });\n    } catch (error) {\n        console.error('Error creating/updating customer:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Failed to save customer'\n        }, {\n            status: 500\n        });\n    }\n}\n// PATCH - Update customer\nasync function PATCH(request) {\n    try {\n        const { customerId, adminKey, ...updates } = await request.json();\n        // Verify admin authentication\n        const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';\n        if (adminKey !== validAdminKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        if (!customerId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Customer ID is required'\n            }, {\n                status: 400\n            });\n        }\n        const success = await (0,_lib_crmStorage__WEBPACK_IMPORTED_MODULE_1__.updateCustomer)(customerId, updates);\n        if (!success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Customer not found'\n            }, {\n                status: 404\n            });\n        }\n        // Log interaction for customer update\n        const { createInteraction } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/crmStorage */ \"(rsc)/./src/lib/crmStorage.ts\"));\n        await createInteraction({\n            customerId,\n            type: 'support',\n            direction: 'outbound',\n            channel: 'website',\n            content: `Customer profile updated: ${Object.keys(updates).join(', ')}`,\n            subject: 'Profile Update',\n            tags: [\n                'profile_update',\n                'admin_action'\n            ],\n            createdBy: 'admin'\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Customer updated successfully'\n        });\n    } catch (error) {\n        console.error('Error updating customer:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Failed to update customer'\n        }, {\n            status: 500\n        });\n    }\n}\n// Utility function to sync customer data from orders\nasync function syncCustomerFromOrder(orderData) {\n    try {\n        const customerData = {\n            personalInfo: {\n                name: orderData.customerInfo.name,\n                email: orderData.customerInfo.email,\n                phone: orderData.customerInfo.phone\n            },\n            address: orderData.customerInfo.address,\n            preferences: {\n                notifications: {\n                    email: true,\n                    sms: false,\n                    push: true,\n                    marketing: false\n                },\n                currency: orderData.currency || 'JPY',\n                communicationPreference: 'email'\n            },\n            status: 'active',\n            segment: 'regular',\n            loyaltyPoints: 0,\n            membershipTier: 'Bronze',\n            totalSpent: orderData.totalAmount,\n            totalOrders: 1,\n            averageOrderValue: orderData.totalAmount,\n            acquisitionSource: 'order',\n            tags: [\n                'customer'\n            ]\n        };\n        const customer = await (0,_lib_crmStorage__WEBPACK_IMPORTED_MODULE_1__.upsertCustomer)(customerData);\n        // Update customer stats if they already existed\n        if (customer) {\n            const existingCustomer = await (0,_lib_crmStorage__WEBPACK_IMPORTED_MODULE_1__.getCustomerById)(customer.id);\n            if (existingCustomer && existingCustomer.totalOrders > 0) {\n                await (0,_lib_crmStorage__WEBPACK_IMPORTED_MODULE_1__.updateCustomer)(customer.id, {\n                    totalSpent: existingCustomer.totalSpent + orderData.totalAmount,\n                    totalOrders: existingCustomer.totalOrders + 1,\n                    averageOrderValue: (existingCustomer.totalSpent + orderData.totalAmount) / (existingCustomer.totalOrders + 1),\n                    lastOrderDate: new Date().toISOString()\n                });\n            }\n        }\n        return customer;\n    } catch (error) {\n        console.error('Error syncing customer from order:', error);\n        return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/customers/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/orders/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/orders/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_orderStorage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/orderStorage */ \"(rsc)/./src/lib/orderStorage.ts\");\n/* harmony import */ var _lib_invoiceGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/invoiceGenerator */ \"(rsc)/./src/lib/invoiceGenerator.ts\");\n/* harmony import */ var _lib_paymentConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/paymentConfig */ \"(rsc)/./src/lib/paymentConfig.ts\");\n/* harmony import */ var _customers_route__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../customers/route */ \"(rsc)/./src/app/api/customers/route.ts\");\n/* harmony import */ var _lib_crmStorage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/crmStorage */ \"(rsc)/./src/lib/crmStorage.ts\");\n/* harmony import */ var _lib_followupScheduler__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/followupScheduler */ \"(rsc)/./src/lib/followupScheduler.ts\");\n\n\n\n\n// Order type is imported but used in function signatures\n// CRM Integration\n\n\n\nasync function POST(request) {\n    try {\n        const orderData = await request.json();\n        // Validate required fields\n        if (!orderData.customerId || !orderData.vehicle || !orderData.payment || !orderData.shipping) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Missing required order data'\n            }, {\n                status: 400\n            });\n        }\n        // Validate payment method\n        const paymentMethod = (0,_lib_paymentConfig__WEBPACK_IMPORTED_MODULE_3__.getPaymentMethodById)(orderData.payment.method.id);\n        if (!paymentMethod) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Invalid payment method'\n            }, {\n                status: 400\n            });\n        }\n        // Validate shipping method\n        const shippingMethod = (0,_lib_paymentConfig__WEBPACK_IMPORTED_MODULE_3__.getShippingMethodById)(orderData.shipping.method.id);\n        if (!shippingMethod) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Invalid shipping method'\n            }, {\n                status: 400\n            });\n        }\n        // Create order\n        const order = await (0,_lib_orderStorage__WEBPACK_IMPORTED_MODULE_1__.createOrder)({\n            customerId: orderData.customerId,\n            customerInfo: orderData.customerInfo,\n            vehicle: orderData.vehicle,\n            payment: {\n                method: paymentMethod,\n                amount: orderData.payment.amount,\n                currency: orderData.payment.currency || 'JPY',\n                status: 'pending',\n                dueDate: orderData.payment.dueDate\n            },\n            shipping: {\n                method: shippingMethod,\n                cost: orderData.shipping.cost,\n                address: orderData.shipping.address,\n                estimatedDelivery: orderData.shipping.estimatedDelivery,\n                status: 'pending',\n                updates: []\n            },\n            status: 'pending_payment',\n            totalAmount: orderData.totalAmount,\n            currency: orderData.currency || 'JPY',\n            notes: orderData.notes,\n            invoiceGenerated: false\n        });\n        // Generate invoice\n        try {\n            const invoice = await (0,_lib_invoiceGenerator__WEBPACK_IMPORTED_MODULE_2__.generateAndEmailInvoice)(order);\n            await (0,_lib_orderStorage__WEBPACK_IMPORTED_MODULE_1__.updateOrder)(order.id, {\n                invoiceGenerated: true,\n                invoiceUrl: invoice.pdfUrl\n            });\n        } catch (invoiceError) {\n            console.error('Failed to generate invoice:', invoiceError);\n        // Continue without failing the order creation\n        }\n        // CRM Integration: Sync customer data and log interaction\n        try {\n            const customer = await (0,_customers_route__WEBPACK_IMPORTED_MODULE_4__.syncCustomerFromOrder)(orderData);\n            if (customer) {\n                // Log order creation interaction\n                await (0,_lib_crmStorage__WEBPACK_IMPORTED_MODULE_5__.createInteraction)({\n                    customerId: customer.id,\n                    type: 'order',\n                    direction: 'inbound',\n                    channel: 'website',\n                    content: `Order created: ${order.vehicle.title} for ¥${order.totalAmount.toLocaleString()}`,\n                    subject: 'New Order',\n                    tags: [\n                        'order_creation',\n                        'purchase'\n                    ],\n                    relatedOrderId: order.id,\n                    relatedProductId: order.vehicle.id,\n                    createdBy: customer.id\n                });\n            }\n        } catch (crmError) {\n            console.error('CRM integration error:', crmError);\n        // Continue without failing the order creation\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Order created successfully',\n            order\n        });\n    } catch (error) {\n        console.error('Error creating order:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Failed to create order'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const customerId = searchParams.get('customerId');\n        const adminKey = searchParams.get('adminKey');\n        // Admin access to all orders\n        const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';\n        if (adminKey === validAdminKey) {\n            const allOrders = await (0,_lib_orderStorage__WEBPACK_IMPORTED_MODULE_1__.getAllOrders)();\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                orders: allOrders\n            });\n        }\n        // Customer access to their own orders\n        if (customerId) {\n            const orders = await (0,_lib_orderStorage__WEBPACK_IMPORTED_MODULE_1__.getOrdersByCustomerId)(customerId);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                orders\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Customer ID required'\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        console.error('Error fetching orders:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Failed to fetch orders'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PATCH(request) {\n    try {\n        const { orderId, updates, adminKey } = await request.json();\n        // Admin authentication\n        const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';\n        if (adminKey !== validAdminKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const success = await (0,_lib_orderStorage__WEBPACK_IMPORTED_MODULE_1__.updateOrder)(orderId, updates);\n        if (!success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Order not found'\n            }, {\n                status: 404\n            });\n        }\n        // CRM Integration: Log order status changes and schedule follow-ups\n        try {\n            const { getOrderById } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/orderStorage */ \"(rsc)/./src/lib/orderStorage.ts\"));\n            const order = await getOrderById(orderId);\n            if (order) {\n                const { getCustomerByEmail } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/crmStorage */ \"(rsc)/./src/lib/crmStorage.ts\"));\n                const customer = await getCustomerByEmail(order.customerInfo.email);\n                if (customer) {\n                    // Log status change interaction\n                    await (0,_lib_crmStorage__WEBPACK_IMPORTED_MODULE_5__.createInteraction)({\n                        customerId: customer.id,\n                        type: 'order',\n                        direction: 'outbound',\n                        channel: 'website',\n                        content: `Order status updated: ${Object.keys(updates).map((key)=>`${key}: ${updates[key]}`).join(', ')}`,\n                        subject: 'Order Status Update',\n                        tags: [\n                            'order_update',\n                            'status_change'\n                        ],\n                        relatedOrderId: order.id,\n                        createdBy: 'admin'\n                    });\n                    // Schedule follow-up if order is delivered\n                    if (updates.status === 'delivered' || updates.shipping?.status === 'delivered') {\n                        await (0,_lib_followupScheduler__WEBPACK_IMPORTED_MODULE_6__.scheduleOrderDeliveryFollowUp)(customer.id, order.id, {\n                            vehicleTitle: order.vehicle.title,\n                            orderNumber: order.orderNumber\n                        });\n                    }\n                }\n            }\n        } catch (crmError) {\n            console.error('CRM integration error during order update:', crmError);\n        // Continue without failing the order update\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Order updated successfully'\n        });\n    } catch (error) {\n        console.error('Error updating order:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Failed to update order'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/orders/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/crmStorage.ts":
/*!*******************************!*\
  !*** ./src/lib/crmStorage.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCustomerActivity: () => (/* binding */ createCustomerActivity),\n/* harmony export */   createFollowUp: () => (/* binding */ createFollowUp),\n/* harmony export */   createInteraction: () => (/* binding */ createInteraction),\n/* harmony export */   createLead: () => (/* binding */ createLead),\n/* harmony export */   deleteLead: () => (/* binding */ deleteLead),\n/* harmony export */   getActivitiesByCustomerId: () => (/* binding */ getActivitiesByCustomerId),\n/* harmony export */   getAllCustomers: () => (/* binding */ getAllCustomers),\n/* harmony export */   getAllFollowUps: () => (/* binding */ getAllFollowUps),\n/* harmony export */   getAllInteractions: () => (/* binding */ getAllInteractions),\n/* harmony export */   getAllLeads: () => (/* binding */ getAllLeads),\n/* harmony export */   getCustomerByEmail: () => (/* binding */ getCustomerByEmail),\n/* harmony export */   getCustomerById: () => (/* binding */ getCustomerById),\n/* harmony export */   getCustomerOverview: () => (/* binding */ getCustomerOverview),\n/* harmony export */   getFollowUpsByCustomerId: () => (/* binding */ getFollowUpsByCustomerId),\n/* harmony export */   getFollowUpsByStatus: () => (/* binding */ getFollowUpsByStatus),\n/* harmony export */   getInteractionsByCustomerId: () => (/* binding */ getInteractionsByCustomerId),\n/* harmony export */   getInteractionsByLeadId: () => (/* binding */ getInteractionsByLeadId),\n/* harmony export */   getLeadById: () => (/* binding */ getLeadById),\n/* harmony export */   getLeadsBySource: () => (/* binding */ getLeadsBySource),\n/* harmony export */   getLeadsByStatus: () => (/* binding */ getLeadsByStatus),\n/* harmony export */   getPendingFollowUps: () => (/* binding */ getPendingFollowUps),\n/* harmony export */   getRecentActivities: () => (/* binding */ getRecentActivities),\n/* harmony export */   updateCustomer: () => (/* binding */ updateCustomer),\n/* harmony export */   updateFollowUp: () => (/* binding */ updateFollowUp),\n/* harmony export */   updateLead: () => (/* binding */ updateLead),\n/* harmony export */   upsertCustomer: () => (/* binding */ upsertCustomer)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/esm/v4.js\");\n\n\n\n// File paths\nconst DATA_DIR = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'data');\nconst LEADS_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DATA_DIR, 'leads.json');\nconst CUSTOMERS_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DATA_DIR, 'customers.json');\nconst INTERACTIONS_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DATA_DIR, 'interactions.json');\nconst FOLLOWUPS_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DATA_DIR, 'followups.json');\nconst ACTIVITIES_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DATA_DIR, 'activities.json');\n// Check if running in serverless environment\nconst isServerless = process.env.VERCEL || process.env.NETLIFY || process.env.AWS_LAMBDA_FUNCTION_NAME;\n// In-memory storage for serverless environments\nlet leadsMemoryStore = [];\nlet customersMemoryStore = [];\nlet interactionsMemoryStore = [];\nlet followupsMemoryStore = [];\nlet activitiesMemoryStore = [];\n/**\n * Ensure data directory exists\n */ async function ensureDataDirectory() {\n    if (isServerless) return;\n    try {\n        await fs__WEBPACK_IMPORTED_MODULE_0__.promises.access(DATA_DIR);\n    } catch  {\n        await fs__WEBPACK_IMPORTED_MODULE_0__.promises.mkdir(DATA_DIR, {\n            recursive: true\n        });\n    }\n}\n// LEADS MANAGEMENT\n/**\n * Load leads from storage\n */ async function loadLeads() {\n    if (isServerless) {\n        return leadsMemoryStore;\n    }\n    try {\n        await ensureDataDirectory();\n        const data = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.readFile(LEADS_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        return [];\n    }\n}\n/**\n * Save leads to storage\n */ async function saveLeads(leads) {\n    if (isServerless) {\n        leadsMemoryStore = leads;\n        return;\n    }\n    await ensureDataDirectory();\n    await fs__WEBPACK_IMPORTED_MODULE_0__.promises.writeFile(LEADS_FILE, JSON.stringify(leads, null, 2));\n}\n/**\n * Create a new lead\n */ async function createLead(leadData) {\n    const leads = await loadLeads();\n    const newLead = {\n        ...leadData,\n        id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    };\n    leads.push(newLead);\n    await saveLeads(leads);\n    return newLead;\n}\n/**\n * Get all leads\n */ async function getAllLeads() {\n    return await loadLeads();\n}\n/**\n * Get lead by ID\n */ async function getLeadById(leadId) {\n    const leads = await loadLeads();\n    return leads.find((lead)=>lead.id === leadId) || null;\n}\n/**\n * Update lead\n */ async function updateLead(leadId, updates) {\n    const leads = await loadLeads();\n    const leadIndex = leads.findIndex((lead)=>lead.id === leadId);\n    if (leadIndex === -1) return false;\n    leads[leadIndex] = {\n        ...leads[leadIndex],\n        ...updates,\n        updatedAt: new Date().toISOString()\n    };\n    await saveLeads(leads);\n    return true;\n}\n/**\n * Delete lead\n */ async function deleteLead(leadId) {\n    const leads = await loadLeads();\n    const filteredLeads = leads.filter((lead)=>lead.id !== leadId);\n    if (filteredLeads.length === leads.length) return false;\n    await saveLeads(filteredLeads);\n    return true;\n}\n/**\n * Get leads by status\n */ async function getLeadsByStatus(status) {\n    const leads = await loadLeads();\n    return leads.filter((lead)=>lead.status === status);\n}\n/**\n * Get leads by source\n */ async function getLeadsBySource(source) {\n    const leads = await loadLeads();\n    return leads.filter((lead)=>lead.source === source);\n}\n// CUSTOMERS MANAGEMENT\n/**\n * Load customers from storage\n */ async function loadCustomers() {\n    if (isServerless) {\n        return customersMemoryStore;\n    }\n    try {\n        await ensureDataDirectory();\n        const data = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.readFile(CUSTOMERS_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        return [];\n    }\n}\n/**\n * Save customers to storage\n */ async function saveCustomers(customers) {\n    if (isServerless) {\n        customersMemoryStore = customers;\n        return;\n    }\n    await ensureDataDirectory();\n    await fs__WEBPACK_IMPORTED_MODULE_0__.promises.writeFile(CUSTOMERS_FILE, JSON.stringify(customers, null, 2));\n}\n/**\n * Create or update customer\n */ async function upsertCustomer(customerData) {\n    const customers = await loadCustomers();\n    // Check if customer exists by email\n    const existingCustomerIndex = customers.findIndex((c)=>c.personalInfo.email === customerData.personalInfo.email);\n    if (existingCustomerIndex !== -1) {\n        // Update existing customer\n        customers[existingCustomerIndex] = {\n            ...customers[existingCustomerIndex],\n            ...customerData,\n            updatedAt: new Date().toISOString()\n        };\n        await saveCustomers(customers);\n        return customers[existingCustomerIndex];\n    } else {\n        // Create new customer\n        const newCustomer = {\n            ...customerData,\n            id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        customers.push(newCustomer);\n        await saveCustomers(customers);\n        return newCustomer;\n    }\n}\n/**\n * Get all customers\n */ async function getAllCustomers() {\n    return await loadCustomers();\n}\n/**\n * Get customer by ID\n */ async function getCustomerById(customerId) {\n    const customers = await loadCustomers();\n    return customers.find((customer)=>customer.id === customerId) || null;\n}\n/**\n * Get customer by email\n */ async function getCustomerByEmail(email) {\n    const customers = await loadCustomers();\n    return customers.find((customer)=>customer.personalInfo.email === email) || null;\n}\n/**\n * Update customer\n */ async function updateCustomer(customerId, updates) {\n    const customers = await loadCustomers();\n    const customerIndex = customers.findIndex((customer)=>customer.id === customerId);\n    if (customerIndex === -1) return false;\n    customers[customerIndex] = {\n        ...customers[customerIndex],\n        ...updates,\n        updatedAt: new Date().toISOString()\n    };\n    await saveCustomers(customers);\n    return true;\n}\n// INTERACTIONS MANAGEMENT\n/**\n * Load interactions from storage\n */ async function loadInteractions() {\n    if (isServerless) {\n        return interactionsMemoryStore;\n    }\n    try {\n        await ensureDataDirectory();\n        const data = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.readFile(INTERACTIONS_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        return [];\n    }\n}\n/**\n * Save interactions to storage\n */ async function saveInteractions(interactions) {\n    if (isServerless) {\n        interactionsMemoryStore = interactions;\n        return;\n    }\n    await ensureDataDirectory();\n    await fs__WEBPACK_IMPORTED_MODULE_0__.promises.writeFile(INTERACTIONS_FILE, JSON.stringify(interactions, null, 2));\n}\n/**\n * Create a new interaction\n */ async function createInteraction(interactionData) {\n    const interactions = await loadInteractions();\n    const newInteraction = {\n        ...interactionData,\n        id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n        createdAt: new Date().toISOString()\n    };\n    interactions.push(newInteraction);\n    await saveInteractions(interactions);\n    return newInteraction;\n}\n/**\n * Get all interactions\n */ async function getAllInteractions() {\n    return await loadInteractions();\n}\n/**\n * Get interactions by customer ID\n */ async function getInteractionsByCustomerId(customerId) {\n    const interactions = await loadInteractions();\n    return interactions.filter((interaction)=>interaction.customerId === customerId);\n}\n/**\n * Get interactions by lead ID\n */ async function getInteractionsByLeadId(leadId) {\n    const interactions = await loadInteractions();\n    return interactions.filter((interaction)=>interaction.leadId === leadId);\n}\n// FOLLOW-UPS MANAGEMENT\n/**\n * Load follow-ups from storage\n */ async function loadFollowUps() {\n    if (isServerless) {\n        return followupsMemoryStore;\n    }\n    try {\n        await ensureDataDirectory();\n        const data = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.readFile(FOLLOWUPS_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        return [];\n    }\n}\n/**\n * Save follow-ups to storage\n */ async function saveFollowUps(followups) {\n    if (isServerless) {\n        followupsMemoryStore = followups;\n        return;\n    }\n    await ensureDataDirectory();\n    await fs__WEBPACK_IMPORTED_MODULE_0__.promises.writeFile(FOLLOWUPS_FILE, JSON.stringify(followups, null, 2));\n}\n/**\n * Create a new follow-up\n */ async function createFollowUp(followupData) {\n    const followups = await loadFollowUps();\n    const newFollowUp = {\n        ...followupData,\n        id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    };\n    followups.push(newFollowUp);\n    await saveFollowUps(followups);\n    return newFollowUp;\n}\n/**\n * Get all follow-ups\n */ async function getAllFollowUps() {\n    return await loadFollowUps();\n}\n/**\n * Get follow-ups by status\n */ async function getFollowUpsByStatus(status) {\n    const followups = await loadFollowUps();\n    return followups.filter((followup)=>followup.status === status);\n}\n/**\n * Get pending follow-ups (due now or overdue)\n */ async function getPendingFollowUps() {\n    const followups = await loadFollowUps();\n    const now = new Date().toISOString();\n    return followups.filter((followup)=>followup.status === 'pending' && followup.scheduledDate <= now);\n}\n/**\n * Update follow-up\n */ async function updateFollowUp(followupId, updates) {\n    const followups = await loadFollowUps();\n    const followupIndex = followups.findIndex((followup)=>followup.id === followupId);\n    if (followupIndex === -1) return false;\n    followups[followupIndex] = {\n        ...followups[followupIndex],\n        ...updates,\n        updatedAt: new Date().toISOString()\n    };\n    await saveFollowUps(followups);\n    return true;\n}\n/**\n * Get follow-ups by customer ID\n */ async function getFollowUpsByCustomerId(customerId) {\n    const followups = await loadFollowUps();\n    return followups.filter((followup)=>followup.customerId === customerId);\n}\n// CUSTOMER ACTIVITIES MANAGEMENT\n/**\n * Load activities from storage\n */ async function loadActivities() {\n    if (isServerless) {\n        return activitiesMemoryStore;\n    }\n    try {\n        await ensureDataDirectory();\n        const data = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.readFile(ACTIVITIES_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        return [];\n    }\n}\n/**\n * Save activities to storage\n */ async function saveActivities(activities) {\n    if (isServerless) {\n        activitiesMemoryStore = activities;\n        return;\n    }\n    await ensureDataDirectory();\n    await fs__WEBPACK_IMPORTED_MODULE_0__.promises.writeFile(ACTIVITIES_FILE, JSON.stringify(activities, null, 2));\n}\n/**\n * Create a new customer activity\n */ async function createCustomerActivity(activityData) {\n    const activities = await loadActivities();\n    const newActivity = {\n        ...activityData,\n        id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n        timestamp: new Date().toISOString()\n    };\n    activities.push(newActivity);\n    await saveActivities(activities);\n    return newActivity;\n}\n/**\n * Get activities by customer ID\n */ async function getActivitiesByCustomerId(customerId) {\n    const activities = await loadActivities();\n    return activities.filter((activity)=>activity.customerId === customerId);\n}\n/**\n * Get recent activities (last 30 days)\n */ async function getRecentActivities(days = 30) {\n    const activities = await loadActivities();\n    const cutoffDate = new Date();\n    cutoffDate.setDate(cutoffDate.getDate() - days);\n    return activities.filter((activity)=>new Date(activity.timestamp) >= cutoffDate).sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());\n}\n// UTILITY FUNCTIONS\n/**\n * Get customer overview with stats\n */ async function getCustomerOverview(customerId) {\n    const customer = await getCustomerById(customerId);\n    if (!customer) return null;\n    const interactions = await getInteractionsByCustomerId(customerId);\n    const followups = await getFollowUpsByCustomerId(customerId);\n    const activities = await getActivitiesByCustomerId(customerId);\n    return {\n        customer,\n        stats: {\n            totalInteractions: interactions.length,\n            pendingFollowUps: followups.filter((f)=>f.status === 'pending').length,\n            recentActivities: activities.filter((a)=>new Date(a.timestamp) >= new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)).length,\n            lastInteraction: interactions.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())[0]?.createdAt\n        },\n        recentInteractions: interactions.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()).slice(0, 5),\n        upcomingFollowUps: followups.filter((f)=>f.status === 'pending').sort((a, b)=>new Date(a.scheduledDate).getTime() - new Date(b.scheduledDate).getTime()).slice(0, 3)\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/crmStorage.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/emailService.ts":
/*!*********************************!*\
  !*** ./src/lib/emailService.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEmailTemplate: () => (/* binding */ getEmailTemplate),\n/* harmony export */   prepareEmailFromTemplate: () => (/* binding */ prepareEmailFromTemplate),\n/* harmony export */   sendAbandonedCartEmail: () => (/* binding */ sendAbandonedCartEmail),\n/* harmony export */   sendEmail: () => (/* binding */ sendEmail),\n/* harmony export */   sendLeadFollowUpEmail: () => (/* binding */ sendLeadFollowUpEmail),\n/* harmony export */   sendOrderDeliveredEmail: () => (/* binding */ sendOrderDeliveredEmail),\n/* harmony export */   sendReengagementEmail: () => (/* binding */ sendReengagementEmail)\n/* harmony export */ });\n// Email service for automated follow-ups and notifications\n// Note: This is a mock implementation. In production, integrate with services like:\n// - SendGrid, Mailgun, AWS SES, or similar email service providers\n// Email templates for different scenarios\nconst EMAIL_TEMPLATES = {\n    abandoned_cart: {\n        id: 'abandoned_cart',\n        name: 'Abandoned Cart Reminder',\n        subject: 'Complete Your Purchase - {{customerName}}',\n        htmlBody: `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <h2 style=\"color: #333;\">Don't Miss Out on Your Selected Vehicle!</h2>\n        <p>Hi {{customerName}},</p>\n        <p>We noticed you were interested in purchasing a vehicle from EBAM Motors but didn't complete your order.</p>\n        <div style=\"background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n          <h3>Your Selected Vehicle:</h3>\n          <p><strong>{{vehicleTitle}}</strong></p>\n          <p>Price: <strong>¥{{vehiclePrice}}</strong></p>\n        </div>\n        <p>Complete your purchase now to secure this vehicle before it's sold to someone else!</p>\n        <a href=\"{{checkoutUrl}}\" style=\"background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;\">Complete Purchase</a>\n        <p style=\"margin-top: 30px; color: #666; font-size: 14px;\">\n          If you have any questions, feel free to contact <NAME_EMAIL> or +************\n        </p>\n      </div>\n    `,\n        textBody: `Hi {{customerName}},\n\nWe noticed you were interested in purchasing {{vehicleTitle}} for ¥{{vehiclePrice}} but didn't complete your order.\n\nComplete your purchase now: {{checkoutUrl}}\n\nContact us: <EMAIL> or +************`,\n        variables: [\n            'customerName',\n            'vehicleTitle',\n            'vehiclePrice',\n            'checkoutUrl'\n        ]\n    },\n    order_delivered: {\n        id: 'order_delivered',\n        name: 'Order Delivered Follow-up',\n        subject: 'How was your recent purchase? - EBAM Motors',\n        htmlBody: `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <h2 style=\"color: #333;\">Thank You for Your Purchase!</h2>\n        <p>Hi {{customerName}},</p>\n        <p>We hope you're enjoying your recent vehicle purchase from EBAM Motors!</p>\n        <div style=\"background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n          <h3>Your Order:</h3>\n          <p><strong>{{vehicleTitle}}</strong></p>\n          <p>Order #: {{orderNumber}}</p>\n          <p>Delivered: {{deliveryDate}}</p>\n        </div>\n        <p>We'd love to hear about your experience. Would you mind leaving us a review?</p>\n        <a href=\"{{reviewUrl}}\" style=\"background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;\">Leave a Review</a>\n        <p style=\"margin-top: 20px;\">Also, don't forget to refer friends and family - you'll earn loyalty points for each successful referral!</p>\n        <p style=\"margin-top: 30px; color: #666; font-size: 14px;\">\n          Need support? Contact <NAME_EMAIL> or +************\n        </p>\n      </div>\n    `,\n        textBody: `Hi {{customerName}},\n\nThank you for purchasing {{vehicleTitle}} (Order #{{orderNumber}}).\n\nWe'd love to hear about your experience. Leave a review: {{reviewUrl}}\n\nContact us: <EMAIL> or +************`,\n        variables: [\n            'customerName',\n            'vehicleTitle',\n            'orderNumber',\n            'deliveryDate',\n            'reviewUrl'\n        ]\n    },\n    customer_reengagement: {\n        id: 'customer_reengagement',\n        name: 'Customer Re-engagement',\n        subject: 'We Miss You! Check Out Our Latest Vehicles',\n        htmlBody: `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <h2 style=\"color: #333;\">We Miss You at EBAM Motors!</h2>\n        <p>Hi {{customerName}},</p>\n        <p>It's been a while since your last visit, and we wanted to reach out with some exciting updates!</p>\n        <div style=\"background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n          <h3>What's New:</h3>\n          <ul>\n            <li>Fresh vehicle arrivals from Japan</li>\n            <li>Special pricing on popular models</li>\n            <li>Enhanced shipping options to Ghana</li>\n            <li>New loyalty rewards program</li>\n          </ul>\n        </div>\n        <p>Browse our latest stock and find your perfect vehicle today!</p>\n        <a href=\"{{stockUrl}}\" style=\"background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;\">View Latest Stock</a>\n        <p style=\"margin-top: 30px; color: #666; font-size: 14px;\">\n          Questions? Contact <NAME_EMAIL> or +************\n        </p>\n      </div>\n    `,\n        textBody: `Hi {{customerName}},\n\nWe miss you at EBAM Motors! Check out our latest vehicle arrivals and special offers.\n\nBrowse our stock: {{stockUrl}}\n\nContact us: <EMAIL> or +************`,\n        variables: [\n            'customerName',\n            'stockUrl'\n        ]\n    },\n    lead_followup: {\n        id: 'lead_followup',\n        name: 'Lead Follow-up',\n        subject: 'Following up on your inquiry - EBAM Motors',\n        htmlBody: `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <h2 style=\"color: #333;\">Thank You for Your Inquiry!</h2>\n        <p>Hi {{customerName}},</p>\n        <p>Thank you for reaching out to EBAM Motors regarding {{inquirySubject}}.</p>\n        <p>We wanted to follow up and see if you have any additional questions or if there's anything specific we can help you with.</p>\n        <div style=\"background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n          <h3>Your Inquiry:</h3>\n          <p>{{inquiryMessage}}</p>\n          <p><em>Submitted: {{inquiryDate}}</em></p>\n        </div>\n        <p>Our team is ready to assist you with:</p>\n        <ul>\n          <li>Vehicle recommendations based on your needs</li>\n          <li>Pricing and financing options</li>\n          <li>Shipping arrangements to Ghana</li>\n          <li>Documentation and import procedures</li>\n        </ul>\n        <a href=\"{{contactUrl}}\" style=\"background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;\">Contact Us</a>\n        <p style=\"margin-top: 30px; color: #666; font-size: 14px;\">\n          Direct contact: <EMAIL> or +************\n        </p>\n      </div>\n    `,\n        textBody: `Hi {{customerName}},\n\nThank you for your inquiry about {{inquirySubject}}.\n\nYour message: {{inquiryMessage}}\n\nWe're here to help with vehicle recommendations, pricing, shipping, and documentation.\n\nContact us: {{contactUrl}} or <EMAIL> or +************`,\n        variables: [\n            'customerName',\n            'inquirySubject',\n            'inquiryMessage',\n            'inquiryDate',\n            'contactUrl'\n        ]\n    }\n};\n/**\n * Replace template variables with actual values\n */ function replaceTemplateVariables(template, variables) {\n    let result = template;\n    Object.entries(variables).forEach(([key, value])=>{\n        const regex = new RegExp(`{{${key}}}`, 'g');\n        result = result.replace(regex, value || '');\n    });\n    return result;\n}\n/**\n * Get email template by ID\n */ function getEmailTemplate(templateId) {\n    return EMAIL_TEMPLATES[templateId] || null;\n}\n/**\n * Prepare email from template\n */ function prepareEmailFromTemplate(templateId, variables, recipientEmail) {\n    const template = getEmailTemplate(templateId);\n    if (!template) return null;\n    return {\n        to: recipientEmail,\n        subject: replaceTemplateVariables(template.subject, variables),\n        htmlBody: replaceTemplateVariables(template.htmlBody, variables),\n        textBody: replaceTemplateVariables(template.textBody, variables),\n        from: 'EBAM Motors <<EMAIL>>',\n        replyTo: '<EMAIL>'\n    };\n}\n/**\n * Send email (mock implementation)\n * In production, integrate with actual email service\n */ async function sendEmail(emailData) {\n    try {\n        // Mock email sending - replace with actual email service integration\n        console.log('📧 Email would be sent:', {\n            to: emailData.to,\n            subject: emailData.subject,\n            from: emailData.from\n        });\n        // Simulate API call delay\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        // Mock success response\n        return {\n            success: true,\n            messageId: `mock_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n        };\n    /* \n    // Example integration with SendGrid:\n    const sgMail = require('@sendgrid/mail');\n    sgMail.setApiKey(process.env.SENDGRID_API_KEY);\n    \n    const msg = {\n      to: emailData.to,\n      from: emailData.from,\n      subject: emailData.subject,\n      text: emailData.textBody,\n      html: emailData.htmlBody,\n    };\n    \n    const response = await sgMail.send(msg);\n    return { success: true, messageId: response[0].headers['x-message-id'] };\n    */ } catch (error) {\n        console.error('Email sending failed:', error);\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : 'Unknown error'\n        };\n    }\n}\n/**\n * Send abandoned cart email\n */ async function sendAbandonedCartEmail(customerEmail, customerName, vehicleTitle, vehiclePrice, checkoutUrl) {\n    const emailData = prepareEmailFromTemplate('abandoned_cart', {\n        customerName,\n        vehicleTitle,\n        vehiclePrice: vehiclePrice.toLocaleString(),\n        checkoutUrl\n    }, customerEmail);\n    if (!emailData) return false;\n    const result = await sendEmail(emailData);\n    return result.success;\n}\n/**\n * Send order delivered follow-up email\n */ async function sendOrderDeliveredEmail(customerEmail, customerName, vehicleTitle, orderNumber, deliveryDate, reviewUrl) {\n    const emailData = prepareEmailFromTemplate('order_delivered', {\n        customerName,\n        vehicleTitle,\n        orderNumber,\n        deliveryDate,\n        reviewUrl\n    }, customerEmail);\n    if (!emailData) return false;\n    const result = await sendEmail(emailData);\n    return result.success;\n}\n/**\n * Send customer re-engagement email\n */ async function sendReengagementEmail(customerEmail, customerName, stockUrl) {\n    const emailData = prepareEmailFromTemplate('customer_reengagement', {\n        customerName,\n        stockUrl\n    }, customerEmail);\n    if (!emailData) return false;\n    const result = await sendEmail(emailData);\n    return result.success;\n}\n/**\n * Send lead follow-up email\n */ async function sendLeadFollowUpEmail(customerEmail, customerName, inquirySubject, inquiryMessage, inquiryDate, contactUrl) {\n    const emailData = prepareEmailFromTemplate('lead_followup', {\n        customerName,\n        inquirySubject,\n        inquiryMessage,\n        inquiryDate,\n        contactUrl\n    }, customerEmail);\n    if (!emailData) return false;\n    const result = await sendEmail(emailData);\n    return result.success;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/emailService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/emailTemplates.ts":
/*!***********************************!*\
  !*** ./src/lib/emailTemplates.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmailTemplates: () => (/* binding */ EmailTemplates)\n/* harmony export */ });\n// Base email styles\nconst emailStyles = `\n  <style>\n    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }\n    .container { max-width: 600px; margin: 0 auto; background-color: white; }\n    .header { background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); color: white; padding: 30px; text-align: center; }\n    .logo { font-size: 28px; font-weight: bold; margin-bottom: 10px; }\n    .content { padding: 30px; }\n    .vehicle-card { border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; margin: 20px 0; }\n    .button { display: inline-block; background: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }\n    .footer { background-color: #f9fafb; padding: 20px; text-align: center; color: #6b7280; font-size: 14px; }\n    .highlight { background-color: #eff6ff; padding: 15px; border-radius: 6px; margin: 15px 0; }\n    .status-badge { display: inline-block; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: bold; }\n    .status-confirmed { background-color: #dcfce7; color: #166534; }\n    .divider { height: 1px; background-color: #e5e7eb; margin: 20px 0; }\n  </style>\n`;\nclass EmailTemplates {\n    /**\n   * Generate Order Confirmation HTML\n   */ static generateOrderConfirmationHTML(data) {\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>Order Confirmation - ${data.orderNumber}</title>\n        ${emailStyles}\n      </head>\n      <body>\n        <div class=\"container\">\n          <!-- Header -->\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors</div>\n            <p>Your trusted partner for quality vehicles from Japan to Ghana</p>\n          </div>\n\n          <!-- Content -->\n          <div class=\"content\">\n            <h1>Order Confirmation</h1>\n            <p>Dear ${data.customerName},</p>\n            <p>Thank you for your order! We're excited to help you get your new vehicle.</p>\n\n            <div class=\"highlight\">\n              <h3>Order Details</h3>\n              <p><strong>Order Number:</strong> ${data.orderNumber}</p>\n              <p><strong>Order Date:</strong> ${data.orderDate}</p>\n              <p><strong>Status:</strong> <span class=\"status-badge status-confirmed\">Confirmed</span></p>\n            </div>\n\n            <!-- Vehicle Details -->\n            <div class=\"vehicle-card\">\n              <h3>Vehicle Information</h3>\n              <img src=\"${data.vehicle.image}\" alt=\"${data.vehicle.title}\" style=\"width: 100%; max-width: 300px; height: 200px; object-fit: cover; border-radius: 6px; margin-bottom: 15px;\">\n              <h4>${data.vehicle.title}</h4>\n              <p><strong>Price:</strong> ${data.vehicle.price}</p>\n            </div>\n\n            <!-- Shipping Information -->\n            <div class=\"highlight\">\n              <h3>Shipping Address</h3>\n              <p>\n                ${data.shippingAddress.street}<br>\n                ${data.shippingAddress.city}, ${data.shippingAddress.state}<br>\n                ${data.shippingAddress.country} ${data.shippingAddress.postalCode}\n              </p>\n              <p><strong>Estimated Delivery:</strong> ${data.estimatedDelivery}</p>\n            </div>\n\n            <!-- Order Summary -->\n            <div class=\"divider\"></div>\n            <div style=\"text-align: right;\">\n              <h3>Order Total: ${data.total}</h3>\n            </div>\n\n            <!-- Next Steps -->\n            <div class=\"highlight\">\n              <h3>What's Next?</h3>\n              <ul>\n                <li>We'll prepare your vehicle for shipping</li>\n                <li>You'll receive tracking information once shipped</li>\n                <li>Our team will contact you for any updates</li>\n              </ul>\n            </div>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://yourdomain.com/tracking?order=${data.orderNumber}\" class=\"button\">Track Your Order</a>\n            </div>\n\n            <p>If you have any questions, please don't hesitate to contact us:</p>\n            <ul>\n              <li>📧 Email: <EMAIL></li>\n              <li>📱 WhatsApp: +************</li>\n              <li>📍 Location: Kumasi, Ghana</li>\n            </ul>\n          </div>\n\n          <!-- Footer -->\n          <div class=\"footer\">\n            <p>Thank you for choosing EBAM Motors!</p>\n            <p>© 2024 EBAM Motors. All rights reserved.</p>\n            <p>This email was sent to confirm your order. Please keep this for your records.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    /**\n   * Generate Order Confirmation Text\n   */ static generateOrderConfirmationText(data) {\n        return `\nORDER CONFIRMATION - EBAM Motors\n\nDear ${data.customerName},\n\nThank you for your order! We're excited to help you get your new vehicle.\n\nORDER DETAILS:\n- Order Number: ${data.orderNumber}\n- Order Date: ${data.orderDate}\n- Status: Confirmed\n\nVEHICLE:\n- ${data.vehicle.title}\n- Price: ${data.vehicle.price}\n\nSHIPPING ADDRESS:\n${data.shippingAddress.street}\n${data.shippingAddress.city}, ${data.shippingAddress.state}\n${data.shippingAddress.country} ${data.shippingAddress.postalCode}\n\nEstimated Delivery: ${data.estimatedDelivery}\n\nORDER TOTAL: ${data.total}\n\nWHAT'S NEXT:\n- We'll prepare your vehicle for shipping\n- You'll receive tracking information once shipped\n- Our team will contact you for any updates\n\nTrack your order: https://yourdomain.com/tracking?order=${data.orderNumber}\n\nCONTACT US:\n- Email: <EMAIL>\n- WhatsApp: +************\n- Location: Kumasi, Ghana\n\nThank you for choosing EBAM Motors!\n© 2024 EBAM Motors. All rights reserved.\n    `;\n    }\n    /**\n   * Generate Review Notification HTML for Admin\n   */ static generateReviewNotificationHTML(data) {\n        const stars = '⭐'.repeat(data.rating) + '☆'.repeat(5 - data.rating);\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>New Review Submitted</title>\n        ${emailStyles}\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors Admin</div>\n            <p>New Review Notification</p>\n          </div>\n\n          <div class=\"content\">\n            <h1>New Review Submitted</h1>\n            \n            <div class=\"highlight\">\n              <h3>Review Details</h3>\n              <p><strong>Customer:</strong> ${data.customerName}</p>\n              <p><strong>Vehicle:</strong> ${data.vehicleTitle}</p>\n              <p><strong>Rating:</strong> ${stars} (${data.rating}/5)</p>\n              <p><strong>Date:</strong> ${data.reviewDate}</p>\n            </div>\n\n            <div class=\"vehicle-card\">\n              <h3>Review Content</h3>\n              <p>\"${data.review}\"</p>\n            </div>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://yourdomain.com/admin/reviews\" class=\"button\">Review & Approve</a>\n            </div>\n          </div>\n\n          <div class=\"footer\">\n            <p>EBAM Motors Admin Panel</p>\n            <p>Please review and approve/reject this review in the admin panel.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    /**\n   * Generate Review Approval HTML for Customer\n   */ static generateReviewApprovalHTML(data) {\n        const stars = '⭐'.repeat(data.rating) + '☆'.repeat(5 - data.rating);\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>Review Approved</title>\n        ${emailStyles}\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors</div>\n            <p>Thank you for your feedback!</p>\n          </div>\n\n          <div class=\"content\">\n            <h1>Your Review Has Been Approved!</h1>\n            <p>Dear ${data.customerName},</p>\n            <p>Thank you for taking the time to review your experience with us. Your review has been approved and is now live on our website!</p>\n\n            <div class=\"highlight\">\n              <h3>Your Review</h3>\n              <p><strong>Vehicle:</strong> ${data.vehicleTitle}</p>\n              <p><strong>Rating:</strong> ${stars} (${data.rating}/5)</p>\n              <p><strong>Review:</strong> \"${data.review}\"</p>\n            </div>\n\n            <p>Your feedback helps other customers make informed decisions and helps us improve our services.</p>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://yourdomain.com/reviews\" class=\"button\">View All Reviews</a>\n            </div>\n          </div>\n\n          <div class=\"footer\">\n            <p>Thank you for choosing EBAM Motors!</p>\n            <p>© 2024 EBAM Motors. All rights reserved.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    /**\n   * Generate Contact Form Admin Notification HTML\n   */ static generateContactFormAdminHTML(data) {\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>New Contact Form Submission</title>\n        ${emailStyles}\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors Admin</div>\n            <p>New Contact Form Submission</p>\n          </div>\n\n          <div class=\"content\">\n            <h1>New Contact Form Submission</h1>\n            \n            <div class=\"highlight\">\n              <h3>Contact Details</h3>\n              <p><strong>Name:</strong> ${data.name}</p>\n              <p><strong>Email:</strong> ${data.email}</p>\n              <p><strong>Subject:</strong> ${data.subject}</p>\n              <p><strong>Submitted:</strong> ${data.submissionDate}</p>\n            </div>\n\n            <div class=\"vehicle-card\">\n              <h3>Message</h3>\n              <p>${data.message.replace(/\\n/g, '<br>')}</p>\n            </div>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"mailto:${data.email}?subject=Re: ${data.subject}\" class=\"button\">Reply to Customer</a>\n            </div>\n          </div>\n\n          <div class=\"footer\">\n            <p>EBAM Motors Admin Panel</p>\n            <p>Please respond to this customer inquiry promptly.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    /**\n   * Generate Abandoned Cart Follow-up HTML\n   */ static generateAbandonedCartHTML(data) {\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>Complete Your Purchase</title>\n        ${emailStyles}\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors</div>\n            <p>Don't miss out on your perfect vehicle!</p>\n          </div>\n\n          <div class=\"content\">\n            <h1>Complete Your Purchase</h1>\n            <p>Hi ${data.customerName},</p>\n            <p>We noticed you were interested in some amazing vehicles but didn't complete your purchase. Don't worry - we've saved your items!</p>\n\n            <div class=\"highlight\">\n              <h3>Your Saved Items</h3>\n              ${data.data?.items?.map((item)=>`\n                <div class=\"vehicle-card\">\n                  <h4>${item.title}</h4>\n                  <p><strong>Price:</strong> ${item.price}</p>\n                  <p>Quantity: ${item.quantity}</p>\n                </div>\n              `).join('') || '<p>Your selected vehicles are waiting for you!</p>'}\n            </div>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://yourdomain.com/stock\" class=\"button\">Complete Your Purchase</a>\n            </div>\n\n            <div class=\"highlight\">\n              <h3>Why Choose EBAM Motors?</h3>\n              <ul>\n                <li>✅ Quality guaranteed vehicles from Japan</li>\n                <li>✅ Competitive pricing with transparent costs</li>\n                <li>✅ Reliable shipping to Ghana and Africa</li>\n                <li>✅ Expert support throughout the process</li>\n              </ul>\n            </div>\n\n            <p>Need help deciding? Our team is here to assist you:</p>\n            <ul>\n              <li>📱 WhatsApp: +************</li>\n              <li>📧 Email: <EMAIL></li>\n            </ul>\n          </div>\n\n          <div class=\"footer\">\n            <p>This offer won't last forever!</p>\n            <p>© 2024 EBAM Motors. All rights reserved.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    /**\n   * Generate Delivery Update HTML\n   */ static generateDeliveryUpdateHTML(data) {\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>Delivery Update</title>\n        ${emailStyles}\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors</div>\n            <p>Your vehicle is on its way!</p>\n          </div>\n\n          <div class=\"content\">\n            <h1>Delivery Update</h1>\n            <p>Hi ${data.customerName},</p>\n            <p>Great news! We have an update on your vehicle delivery.</p>\n\n            <div class=\"highlight\">\n              <h3>Delivery Status</h3>\n              <p><strong>Current Status:</strong> ${data.data?.status || 'In Transit'}</p>\n              <p><strong>Location:</strong> ${data.data?.location || 'En route to destination'}</p>\n              <p><strong>Estimated Arrival:</strong> ${data.data?.estimatedArrival || 'To be confirmed'}</p>\n            </div>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://yourdomain.com/tracking?order=${data.data?.orderId}\" class=\"button\">Track Your Order</a>\n            </div>\n\n            <div class=\"vehicle-card\">\n              <h3>What to Expect Next</h3>\n              <ul>\n                <li>We'll notify you 24 hours before delivery</li>\n                <li>Our delivery team will contact you directly</li>\n                <li>Ensure someone is available to receive the vehicle</li>\n                <li>Have your ID and order confirmation ready</li>\n              </ul>\n            </div>\n          </div>\n\n          <div class=\"footer\">\n            <p>Thank you for choosing EBAM Motors!</p>\n            <p>© 2024 EBAM Motors. All rights reserved.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    /**\n   * Generate Feedback Request HTML\n   */ static generateFeedbackRequestHTML(data) {\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>How was your experience?</title>\n        ${emailStyles}\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors</div>\n            <p>We'd love to hear from you!</p>\n          </div>\n\n          <div class=\"content\">\n            <h1>How Was Your Experience?</h1>\n            <p>Hi ${data.customerName},</p>\n            <p>We hope you're enjoying your new vehicle! Your feedback helps us improve our services and helps other customers make informed decisions.</p>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://yourdomain.com/reviews\" class=\"button\">Leave a Review</a>\n            </div>\n\n            <div class=\"highlight\">\n              <h3>Share Your Experience</h3>\n              <p>Tell us about:</p>\n              <ul>\n                <li>🚗 Vehicle quality and condition</li>\n                <li>📦 Shipping and delivery experience</li>\n                <li>👥 Customer service quality</li>\n                <li>💰 Value for money</li>\n                <li>🌟 Overall satisfaction</li>\n              </ul>\n            </div>\n\n            <div class=\"vehicle-card\">\n              <h3>Why Your Review Matters</h3>\n              <ul>\n                <li>Helps other customers make confident decisions</li>\n                <li>Helps us improve our services</li>\n                <li>Builds trust in our community</li>\n                <li>Takes less than 2 minutes to complete</li>\n              </ul>\n            </div>\n\n            <p>As a thank you, customers who leave reviews get priority support and exclusive offers!</p>\n          </div>\n\n          <div class=\"footer\">\n            <p>Thank you for choosing EBAM Motors!</p>\n            <p>© 2024 EBAM Motors. All rights reserved.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    /**\n   * Generate Contact Form Customer Confirmation HTML\n   */ static generateContactFormCustomerHTML(data) {\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>Thank you for contacting us</title>\n        ${emailStyles}\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors</div>\n            <p>Thank you for reaching out!</p>\n          </div>\n\n          <div class=\"content\">\n            <h1>Thank You for Contacting Us!</h1>\n            <p>Dear ${data.name},</p>\n            <p>We've received your message and appreciate you taking the time to contact us. Our team will review your inquiry and respond within 24 hours.</p>\n\n            <div class=\"highlight\">\n              <h3>Your Message Summary</h3>\n              <p><strong>Subject:</strong> ${data.subject}</p>\n              <p><strong>Submitted:</strong> ${data.submissionDate}</p>\n              <p><strong>Reference ID:</strong> #${Date.now().toString().slice(-6)}</p>\n            </div>\n\n            <div class=\"vehicle-card\">\n              <h3>What to Expect Next</h3>\n              <ul>\n                <li>Our team will review your inquiry</li>\n                <li>You'll receive a response within 24 hours</li>\n                <li>For urgent matters, contact us on WhatsApp</li>\n              </ul>\n            </div>\n\n            <p>In the meantime, feel free to:</p>\n            <ul>\n              <li>Browse our latest vehicle inventory</li>\n              <li>Check out customer reviews</li>\n              <li>Learn more about our services</li>\n            </ul>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://yourdomain.com/stock\" class=\"button\">Browse Vehicles</a>\n            </div>\n\n            <p><strong>Need immediate assistance?</strong></p>\n            <ul>\n              <li>📱 WhatsApp: +************</li>\n              <li>📧 Email: <EMAIL></li>\n              <li>📍 Location: Kumasi, Ghana</li>\n            </ul>\n          </div>\n\n          <div class=\"footer\">\n            <p>Thank you for choosing EBAM Motors!</p>\n            <p>© 2024 EBAM Motors. All rights reserved.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/emailTemplates.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/followupScheduler.ts":
/*!**************************************!*\
  !*** ./src/lib/followupScheduler.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initializeFollowUpScheduler: () => (/* binding */ initializeFollowUpScheduler),\n/* harmony export */   processPendingFollowUps: () => (/* binding */ processPendingFollowUps),\n/* harmony export */   scheduleAbandonedCartFollowUp: () => (/* binding */ scheduleAbandonedCartFollowUp),\n/* harmony export */   scheduleInactiveCustomerFollowUps: () => (/* binding */ scheduleInactiveCustomerFollowUps),\n/* harmony export */   scheduleOrderDeliveryFollowUp: () => (/* binding */ scheduleOrderDeliveryFollowUp),\n/* harmony export */   sendAbandonedCartFollowUpResend: () => (/* binding */ sendAbandonedCartFollowUpResend),\n/* harmony export */   sendDeliveryUpdateFollowUpResend: () => (/* binding */ sendDeliveryUpdateFollowUpResend),\n/* harmony export */   sendFeedbackRequestFollowUpResend: () => (/* binding */ sendFeedbackRequestFollowUpResend)\n/* harmony export */ });\n/* harmony import */ var _crmStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./crmStorage */ \"(rsc)/./src/lib/crmStorage.ts\");\n/* harmony import */ var _emailService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./emailService */ \"(rsc)/./src/lib/emailService.ts\");\n/* harmony import */ var _resendService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./resendService */ \"(rsc)/./src/lib/resendService.ts\");\n\n\n\n/**\n * Process pending follow-ups\n * This should be called periodically (e.g., every hour via cron job)\n */ async function processPendingFollowUps() {\n    try {\n        console.log('🔄 Processing pending follow-ups...');\n        const pendingFollowUps = await (0,_crmStorage__WEBPACK_IMPORTED_MODULE_0__.getPendingFollowUps)();\n        console.log(`Found ${pendingFollowUps.length} pending follow-ups`);\n        for (const followup of pendingFollowUps){\n            await processFollowUp(followup);\n        }\n        console.log('✅ Finished processing follow-ups');\n    } catch (error) {\n        console.error('❌ Error processing follow-ups:', error);\n    }\n}\n/**\n * Process individual follow-up\n */ async function processFollowUp(followup) {\n    try {\n        console.log(`Processing follow-up: ${followup.title} (${followup.type})`);\n        let success = false;\n        let resultMessage = '';\n        switch(followup.type){\n            case 'email':\n                success = await processEmailFollowUp(followup);\n                resultMessage = success ? 'Email sent successfully' : 'Failed to send email';\n                break;\n            case 'task':\n                // For tasks, just mark as completed if it's past due\n                success = true;\n                resultMessage = 'Task reminder processed';\n                break;\n            case 'phone':\n                // For phone follow-ups, create a reminder for admin\n                success = true;\n                resultMessage = 'Phone follow-up reminder created';\n                break;\n            case 'whatsapp':\n                // For WhatsApp follow-ups, create a reminder for admin\n                success = true;\n                resultMessage = 'WhatsApp follow-up reminder created';\n                break;\n            default:\n                console.log(`Unknown follow-up type: ${followup.type}`);\n                return;\n        }\n        // Update follow-up status\n        await (0,_crmStorage__WEBPACK_IMPORTED_MODULE_0__.updateFollowUp)(followup.id, {\n            status: success ? 'completed' : 'failed',\n            completedDate: success ? new Date().toISOString() : undefined,\n            result: {\n                success,\n                message: resultMessage,\n                responseReceived: false,\n                nextAction: success ? undefined : 'retry_later'\n            }\n        });\n        console.log(`✅ Follow-up processed: ${followup.title} - ${resultMessage}`);\n    } catch (error) {\n        console.error(`❌ Error processing follow-up ${followup.id}:`, error);\n        // Mark as failed\n        await (0,_crmStorage__WEBPACK_IMPORTED_MODULE_0__.updateFollowUp)(followup.id, {\n            status: 'failed',\n            result: {\n                success: false,\n                message: error instanceof Error ? error.message : 'Unknown error',\n                responseReceived: false,\n                nextAction: 'manual_review'\n            }\n        });\n    }\n}\n/**\n * Process email follow-up\n */ async function processEmailFollowUp(followup) {\n    if (!followup.customerId && !followup.leadId) {\n        console.error('Follow-up missing customer or lead ID');\n        return false;\n    }\n    // Get customer information\n    let customerEmail = '';\n    let customerName = '';\n    if (followup.customerId) {\n        const customer = await (0,_crmStorage__WEBPACK_IMPORTED_MODULE_0__.getCustomerById)(followup.customerId);\n        if (!customer) {\n            console.error(`Customer not found: ${followup.customerId}`);\n            return false;\n        }\n        customerEmail = customer.personalInfo.email;\n        customerName = customer.personalInfo.name;\n    }\n    if (!customerEmail) {\n        console.error('Customer email not found');\n        return false;\n    }\n    // Send email based on automation rule\n    if (followup.automationRule) {\n        switch(followup.automationRule.trigger){\n            case 'abandoned_cart':\n                return await (0,_emailService__WEBPACK_IMPORTED_MODULE_1__.sendAbandonedCartEmail)(customerEmail, customerName, followup.automationRule.conditions?.vehicleTitle || 'Selected Vehicle', followup.automationRule.conditions?.cartValue || 0, followup.automationRule.conditions?.checkoutUrl || 'https://ebammotors.com/checkout');\n            case 'order_delivered':\n                return await (0,_emailService__WEBPACK_IMPORTED_MODULE_1__.sendOrderDeliveredEmail)(customerEmail, customerName, followup.automationRule.conditions?.vehicleTitle || 'Your Vehicle', followup.automationRule.conditions?.orderNumber || 'N/A', new Date().toLocaleDateString(), 'https://ebammotors.com/leave-review');\n            case 'no_activity':\n                return await (0,_emailService__WEBPACK_IMPORTED_MODULE_1__.sendReengagementEmail)(customerEmail, customerName, 'https://ebammotors.com/stock');\n            default:\n                // Generic follow-up email\n                return await (0,_emailService__WEBPACK_IMPORTED_MODULE_1__.sendLeadFollowUpEmail)(customerEmail, customerName, followup.title, followup.description, new Date(followup.createdAt).toLocaleDateString(), 'https://ebammotors.com/contact');\n        }\n    }\n    // If no automation rule, send generic follow-up\n    return await (0,_emailService__WEBPACK_IMPORTED_MODULE_1__.sendLeadFollowUpEmail)(customerEmail, customerName, followup.title, followup.description, new Date(followup.createdAt).toLocaleDateString(), 'https://ebammotors.com/contact');\n}\n/**\n * Create automated follow-ups for abandoned carts\n * Call this when a customer adds items to cart but doesn't complete purchase\n */ async function scheduleAbandonedCartFollowUp(customerId, cartData) {\n    try {\n        const customer = await (0,_crmStorage__WEBPACK_IMPORTED_MODULE_0__.getCustomerById)(customerId);\n        if (!customer) return;\n        // Schedule follow-up for 24 hours later\n        await (0,_crmStorage__WEBPACK_IMPORTED_MODULE_0__.createFollowUp)({\n            type: 'email',\n            status: 'pending',\n            priority: 'medium',\n            customerId,\n            title: 'Abandoned Cart Follow-up',\n            description: `Customer abandoned cart with ${cartData.items.length} items worth ¥${cartData.totalValue.toLocaleString()}`,\n            scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),\n            automationRule: {\n                trigger: 'abandoned_cart',\n                delay: 24,\n                conditions: {\n                    vehicleTitle: cartData.items[0]?.title,\n                    cartValue: cartData.totalValue,\n                    checkoutUrl: cartData.checkoutUrl\n                }\n            },\n            createdBy: 'system'\n        });\n        console.log(`📅 Scheduled abandoned cart follow-up for customer ${customerId}`);\n    } catch (error) {\n        console.error('Error scheduling abandoned cart follow-up:', error);\n    }\n}\n/**\n * Create automated follow-ups for delivered orders\n * Call this when an order is marked as delivered\n */ async function scheduleOrderDeliveryFollowUp(customerId, orderId, orderData) {\n    try {\n        // Schedule follow-up for 7 days after delivery\n        await (0,_crmStorage__WEBPACK_IMPORTED_MODULE_0__.createFollowUp)({\n            type: 'email',\n            status: 'pending',\n            priority: 'low',\n            customerId,\n            orderId,\n            title: 'Order Delivery Follow-up',\n            description: `Follow up on delivered order ${orderData.orderNumber}`,\n            scheduledDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),\n            automationRule: {\n                trigger: 'order_delivered',\n                delay: 168,\n                conditions: {\n                    vehicleTitle: orderData.vehicleTitle,\n                    orderNumber: orderData.orderNumber\n                }\n            },\n            createdBy: 'system'\n        });\n        console.log(`📅 Scheduled order delivery follow-up for order ${orderId}`);\n    } catch (error) {\n        console.error('Error scheduling order delivery follow-up:', error);\n    }\n}\n/**\n * Create re-engagement follow-ups for inactive customers\n * Call this periodically to identify and re-engage inactive customers\n */ async function scheduleInactiveCustomerFollowUps() {\n    try {\n        const customers = await (0,_crmStorage__WEBPACK_IMPORTED_MODULE_0__.getAllCustomers)();\n        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);\n        for (const customer of customers){\n            // Check if customer has been inactive for 30+ days\n            const lastActivity = await (0,_crmStorage__WEBPACK_IMPORTED_MODULE_0__.getActivitiesByCustomerId)(customer.id);\n            const recentActivity = lastActivity.find((activity)=>new Date(activity.timestamp) > thirtyDaysAgo);\n            if (!recentActivity && customer.status === 'active') {\n                // Check if we haven't already sent a re-engagement email recently\n                const existingFollowUps = await (0,_crmStorage__WEBPACK_IMPORTED_MODULE_0__.getPendingFollowUps)();\n                const hasRecentReengagement = existingFollowUps.some((f)=>f.customerId === customer.id && f.automationRule?.trigger === 'no_activity' && new Date(f.createdAt) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000));\n                if (!hasRecentReengagement) {\n                    await (0,_crmStorage__WEBPACK_IMPORTED_MODULE_0__.createFollowUp)({\n                        type: 'email',\n                        status: 'pending',\n                        priority: 'low',\n                        customerId: customer.id,\n                        title: 'Customer Re-engagement',\n                        description: `Re-engage inactive customer (30+ days since last activity)`,\n                        scheduledDate: new Date().toISOString(),\n                        automationRule: {\n                            trigger: 'no_activity',\n                            delay: 0,\n                            conditions: {\n                                daysSinceLastActivity: 30\n                            }\n                        },\n                        createdBy: 'system'\n                    });\n                    console.log(`📅 Scheduled re-engagement follow-up for customer ${customer.id}`);\n                }\n            }\n        }\n    } catch (error) {\n        console.error('Error scheduling inactive customer follow-ups:', error);\n    }\n}\n/**\n * Initialize follow-up scheduler\n * This should be called when the application starts\n */ function initializeFollowUpScheduler() {\n    console.log('🚀 Initializing follow-up scheduler...');\n    // Process pending follow-ups every hour\n    setInterval(processPendingFollowUps, 60 * 60 * 1000);\n    // Check for inactive customers daily\n    setInterval(scheduleInactiveCustomerFollowUps, 24 * 60 * 60 * 1000);\n    console.log('✅ Follow-up scheduler initialized');\n}\n/**\n * Send abandoned cart follow-up using Resend\n */ async function sendAbandonedCartFollowUpResend(customerData) {\n    const followUpData = {\n        customerName: customerData.customerName,\n        customerEmail: customerData.customerEmail,\n        type: 'abandoned_cart',\n        data: {\n            items: customerData.cartItems,\n            totalItems: customerData.cartItems.length\n        }\n    };\n    try {\n        await _resendService__WEBPACK_IMPORTED_MODULE_2__.emailService.sendFollowUpEmail(followUpData);\n        console.log('Abandoned cart follow-up email sent via Resend');\n        return {\n            success: true\n        };\n    } catch (error) {\n        console.error('Failed to send abandoned cart follow-up via Resend:', error);\n        return {\n            success: false,\n            error\n        };\n    }\n}\n/**\n * Send delivery update follow-up using Resend\n */ async function sendDeliveryUpdateFollowUpResend(orderData) {\n    const followUpData = {\n        customerName: orderData.customerName,\n        customerEmail: orderData.customerEmail,\n        type: 'delivery_update',\n        data: {\n            orderId: orderData.orderId,\n            status: orderData.status,\n            location: orderData.location,\n            estimatedArrival: orderData.estimatedArrival\n        }\n    };\n    try {\n        await _resendService__WEBPACK_IMPORTED_MODULE_2__.emailService.sendFollowUpEmail(followUpData);\n        console.log('Delivery update follow-up email sent via Resend');\n        return {\n            success: true\n        };\n    } catch (error) {\n        console.error('Failed to send delivery update follow-up via Resend:', error);\n        return {\n            success: false,\n            error\n        };\n    }\n}\n/**\n * Send feedback request follow-up using Resend\n */ async function sendFeedbackRequestFollowUpResend(customerData) {\n    const followUpData = {\n        customerName: customerData.customerName,\n        customerEmail: customerData.customerEmail,\n        type: 'feedback_request',\n        data: {\n            orderId: customerData.orderId,\n            vehicleTitle: customerData.vehicleTitle\n        }\n    };\n    try {\n        await _resendService__WEBPACK_IMPORTED_MODULE_2__.emailService.sendFollowUpEmail(followUpData);\n        console.log('Feedback request follow-up email sent via Resend');\n        return {\n            success: true\n        };\n    } catch (error) {\n        console.error('Failed to send feedback request follow-up via Resend:', error);\n        return {\n            success: false,\n            error\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/followupScheduler.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/invoiceGenerator.ts":
/*!*************************************!*\
  !*** ./src/lib/invoiceGenerator.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateAndEmailInvoice: () => (/* binding */ generateAndEmailInvoice),\n/* harmony export */   generateInvoiceFromOrder: () => (/* binding */ generateInvoiceFromOrder),\n/* harmony export */   generateInvoicePDF: () => (/* binding */ generateInvoicePDF),\n/* harmony export */   getInvoiceByOrderId: () => (/* reexport safe */ _orderStorage__WEBPACK_IMPORTED_MODULE_1__.getInvoiceByOrderId),\n/* harmony export */   getInvoiceSummary: () => (/* binding */ getInvoiceSummary),\n/* harmony export */   markInvoiceAsPaid: () => (/* binding */ markInvoiceAsPaid),\n/* harmony export */   saveInvoicePDF: () => (/* binding */ saveInvoicePDF)\n/* harmony export */ });\n/* harmony import */ var jspdf__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jspdf */ \"(rsc)/./node_modules/jspdf/dist/jspdf.es.min.js\");\n/* harmony import */ var _orderStorage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./orderStorage */ \"(rsc)/./src/lib/orderStorage.ts\");\n\n\n/**\n * Generate invoice items from order\n */ function generateInvoiceItems(order) {\n    const items = [\n        {\n            id: '1',\n            description: order.vehicle.title,\n            quantity: 1,\n            unitPrice: order.vehicle.price,\n            total: order.vehicle.price\n        }\n    ];\n    if (order.shipping.cost > 0) {\n        items.push({\n            id: '2',\n            description: `Shipping (${order.shipping.method.name})`,\n            quantity: 1,\n            unitPrice: order.shipping.cost,\n            total: order.shipping.cost\n        });\n    }\n    return items;\n}\n/**\n * Calculate invoice totals\n */ function calculateInvoiceTotals(items) {\n    const subtotal = items.reduce((sum, item)=>sum + item.total, 0);\n    const tax = 0; // No tax for international sales\n    const total = subtotal + tax;\n    return {\n        subtotal,\n        tax,\n        total\n    };\n}\n/**\n * Generate invoice data from order\n */ async function generateInvoiceFromOrder(order) {\n    const items = generateInvoiceItems(order);\n    const { subtotal, tax, total } = calculateInvoiceTotals(items);\n    const invoiceData = {\n        orderId: order.id,\n        issuedDate: new Date().toISOString().split('T')[0],\n        dueDate: order.payment.dueDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\n        status: 'sent',\n        items,\n        subtotal,\n        tax,\n        shipping: order.shipping.cost,\n        total,\n        currency: order.currency,\n        paymentTerms: 'Payment due within 30 days of invoice date',\n        notes: `Order: ${order.orderNumber}\\nVehicle: ${order.vehicle.title}\\nShipping to: ${order.shipping.address.city}, ${order.shipping.address.country}`\n    };\n    return await (0,_orderStorage__WEBPACK_IMPORTED_MODULE_1__.createInvoice)(invoiceData);\n}\n/**\n * Generate PDF invoice\n */ function generateInvoicePDF(invoice, order) {\n    const doc = new jspdf__WEBPACK_IMPORTED_MODULE_0__[\"default\"]();\n    const pageWidth = doc.internal.pageSize.width;\n    const margin = 20;\n    // Company header\n    doc.setFontSize(24);\n    doc.setFont('helvetica', 'bold');\n    doc.text('EBAM MOTORS', margin, 30);\n    doc.setFontSize(12);\n    doc.setFont('helvetica', 'normal');\n    doc.text('Premium Japanese Vehicles Export', margin, 40);\n    doc.text('Japan Office: Tokyo, Japan', margin, 50);\n    doc.text('Ghana Office: Kumasi, Ghana', margin, 60);\n    doc.text('Phone: +************', margin, 70);\n    doc.text('Email: <EMAIL>', margin, 80);\n    // Invoice title and number\n    doc.setFontSize(20);\n    doc.setFont('helvetica', 'bold');\n    doc.text('INVOICE', pageWidth - margin - 40, 30);\n    doc.setFontSize(12);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`Invoice #: ${invoice.invoiceNumber}`, pageWidth - margin - 60, 45);\n    doc.text(`Date: ${invoice.issuedDate}`, pageWidth - margin - 60, 55);\n    doc.text(`Due Date: ${invoice.dueDate}`, pageWidth - margin - 60, 65);\n    doc.text(`Order #: ${order.orderNumber}`, pageWidth - margin - 60, 75);\n    // Customer information\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text('Bill To:', margin, 110);\n    doc.setFontSize(12);\n    doc.setFont('helvetica', 'normal');\n    doc.text(order.customerInfo.name, margin, 125);\n    doc.text(order.customerInfo.email, margin, 135);\n    doc.text(order.customerInfo.phone, margin, 145);\n    doc.text(order.customerInfo.address.street, margin, 155);\n    doc.text(`${order.customerInfo.address.city}, ${order.customerInfo.address.state}`, margin, 165);\n    doc.text(`${order.customerInfo.address.country} ${order.customerInfo.address.postalCode}`, margin, 175);\n    // Shipping information\n    doc.setFontSize(14);\n    doc.setFont('helvetica', 'bold');\n    doc.text('Ship To:', pageWidth - margin - 80, 110);\n    doc.setFontSize(12);\n    doc.setFont('helvetica', 'normal');\n    doc.text(order.shipping.address.street, pageWidth - margin - 80, 125);\n    doc.text(`${order.shipping.address.city}, ${order.shipping.address.state}`, pageWidth - margin - 80, 135);\n    doc.text(`${order.shipping.address.country} ${order.shipping.address.postalCode}`, pageWidth - margin - 80, 145);\n    // Items table\n    const tableTop = 200;\n    const itemHeight = 20;\n    // Table headers\n    doc.setFontSize(12);\n    doc.setFont('helvetica', 'bold');\n    doc.text('Description', margin, tableTop);\n    doc.text('Qty', pageWidth - 120, tableTop);\n    doc.text('Unit Price', pageWidth - 80, tableTop);\n    doc.text('Total', pageWidth - 40, tableTop);\n    // Draw header line\n    doc.line(margin, tableTop + 5, pageWidth - margin, tableTop + 5);\n    // Table items\n    doc.setFont('helvetica', 'normal');\n    let currentY = tableTop + 15;\n    invoice.items.forEach((item, index)=>{\n        doc.text(item.description, margin, currentY);\n        doc.text(item.quantity.toString(), pageWidth - 120, currentY);\n        doc.text(`¥${item.unitPrice.toLocaleString()}`, pageWidth - 80, currentY);\n        doc.text(`¥${item.total.toLocaleString()}`, pageWidth - 40, currentY);\n        currentY += itemHeight;\n    });\n    // Draw line before totals\n    doc.line(margin, currentY, pageWidth - margin, currentY);\n    currentY += 10;\n    // Totals\n    doc.setFont('helvetica', 'normal');\n    doc.text('Subtotal:', pageWidth - 80, currentY);\n    doc.text(`¥${invoice.subtotal.toLocaleString()}`, pageWidth - 40, currentY);\n    currentY += 15;\n    if (invoice.tax > 0) {\n        doc.text('Tax:', pageWidth - 80, currentY);\n        doc.text(`¥${invoice.tax.toLocaleString()}`, pageWidth - 40, currentY);\n        currentY += 15;\n    }\n    doc.setFont('helvetica', 'bold');\n    doc.text('Total:', pageWidth - 80, currentY);\n    doc.text(`¥${invoice.total.toLocaleString()}`, pageWidth - 40, currentY);\n    // Payment terms\n    currentY += 30;\n    doc.setFontSize(10);\n    doc.setFont('helvetica', 'normal');\n    doc.text('Payment Terms:', margin, currentY);\n    currentY += 10;\n    doc.text(invoice.paymentTerms, margin, currentY);\n    // Notes\n    if (invoice.notes) {\n        currentY += 20;\n        doc.text('Notes:', margin, currentY);\n        currentY += 10;\n        const splitNotes = doc.splitTextToSize(invoice.notes, pageWidth - 2 * margin);\n        doc.text(splitNotes, margin, currentY);\n    }\n    // Footer\n    const footerY = doc.internal.pageSize.height - 30;\n    doc.setFontSize(8);\n    doc.text('Thank you for your business!', margin, footerY);\n    doc.text('For questions about this invoice, please contact <NAME_EMAIL>', margin, footerY + 10);\n    return doc;\n}\n/**\n * Save invoice PDF to file system (for development/local storage)\n */ async function saveInvoicePDF(invoice, order) {\n    const doc = generateInvoicePDF(invoice, order);\n    const pdfBlob = doc.output('blob');\n    // In a real application, you would upload this to cloud storage\n    // For now, we'll return a mock URL\n    const mockUrl = `/invoices/${invoice.invoiceNumber}.pdf`;\n    // Update invoice with PDF URL\n    await (0,_orderStorage__WEBPACK_IMPORTED_MODULE_1__.updateInvoice)(invoice.id, {\n        pdfUrl: mockUrl\n    });\n    return mockUrl;\n}\n/**\n * Generate and email invoice\n */ async function generateAndEmailInvoice(order) {\n    // Generate invoice\n    const invoice = await generateInvoiceFromOrder(order);\n    // Generate PDF\n    const pdfUrl = await saveInvoicePDF(invoice, order);\n    // In a real application, you would send email here\n    console.log(`Invoice ${invoice.invoiceNumber} generated for order ${order.orderNumber}`);\n    console.log(`PDF URL: ${pdfUrl}`);\n    return invoice;\n}\n/**\n * Mark invoice as paid\n */ async function markInvoiceAsPaid(invoiceId) {\n    return await (0,_orderStorage__WEBPACK_IMPORTED_MODULE_1__.updateInvoice)(invoiceId, {\n        status: 'paid'\n    });\n}\n/**\n * Get invoice by order ID (re-export from orderStorage)\n */ \n/**\n * Get invoice summary for display\n */ function getInvoiceSummary(invoice) {\n    const totalItems = invoice.items.length;\n    const totalAmount = `¥${invoice.total.toLocaleString()}`;\n    const dueDate = new Date(invoice.dueDate);\n    const today = new Date();\n    const daysUntilDue = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n    const isOverdue = daysUntilDue < 0;\n    return {\n        totalItems,\n        totalAmount,\n        daysUntilDue,\n        isOverdue\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/invoiceGenerator.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/orderStorage.ts":
/*!*********************************!*\
  !*** ./src/lib/orderStorage.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addShippingUpdate: () => (/* binding */ addShippingUpdate),\n/* harmony export */   createInvoice: () => (/* binding */ createInvoice),\n/* harmony export */   createOrder: () => (/* binding */ createOrder),\n/* harmony export */   getAllOrders: () => (/* binding */ getAllOrders),\n/* harmony export */   getInvoiceByOrderId: () => (/* binding */ getInvoiceByOrderId),\n/* harmony export */   getOrderById: () => (/* binding */ getOrderById),\n/* harmony export */   getOrdersByCustomerId: () => (/* binding */ getOrdersByCustomerId),\n/* harmony export */   getStorageType: () => (/* binding */ getStorageType),\n/* harmony export */   updateInvoice: () => (/* binding */ updateInvoice),\n/* harmony export */   updateOrder: () => (/* binding */ updateOrder),\n/* harmony export */   updateOrderStatus: () => (/* binding */ updateOrderStatus),\n/* harmony export */   updatePaymentStatus: () => (/* binding */ updatePaymentStatus)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/esm/v4.js\");\n\n\n\n// Check if we're in a serverless environment\nconst isServerless = process.env.VERCEL || process.env.NETLIFY || process.env.AWS_LAMBDA_FUNCTION_NAME;\n// File paths\nconst DATA_DIR = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'data');\nconst ORDERS_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DATA_DIR, 'orders.json');\nconst INVOICES_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DATA_DIR, 'invoices.json');\n// In-memory storage for serverless environments\nlet ordersMemoryStore = [];\nlet invoicesMemoryStore = [];\n/**\n * Get storage type for logging/debugging\n */ function getStorageType() {\n    return isServerless ? 'memory' : 'file';\n}\n/**\n * Ensure data directory exists\n */ async function ensureDataDirectory() {\n    if (isServerless) return;\n    try {\n        await fs__WEBPACK_IMPORTED_MODULE_0__.promises.access(DATA_DIR);\n    } catch  {\n        await fs__WEBPACK_IMPORTED_MODULE_0__.promises.mkdir(DATA_DIR, {\n            recursive: true\n        });\n    }\n}\n/**\n * Load orders from storage\n */ async function loadOrders() {\n    if (isServerless) {\n        return ordersMemoryStore;\n    }\n    try {\n        await ensureDataDirectory();\n        const data = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.readFile(ORDERS_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        // File doesn't exist or is invalid, return empty array\n        return [];\n    }\n}\n/**\n * Save orders to storage\n */ async function saveOrders(orders) {\n    if (isServerless) {\n        ordersMemoryStore = orders;\n        return;\n    }\n    await ensureDataDirectory();\n    await fs__WEBPACK_IMPORTED_MODULE_0__.promises.writeFile(ORDERS_FILE, JSON.stringify(orders, null, 2));\n}\n/**\n * Load invoices from storage\n */ async function loadInvoices() {\n    if (isServerless) {\n        return invoicesMemoryStore;\n    }\n    try {\n        await ensureDataDirectory();\n        const data = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.readFile(INVOICES_FILE, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        return [];\n    }\n}\n/**\n * Save invoices to storage\n */ async function saveInvoices(invoices) {\n    if (isServerless) {\n        invoicesMemoryStore = invoices;\n        return;\n    }\n    await ensureDataDirectory();\n    await fs__WEBPACK_IMPORTED_MODULE_0__.promises.writeFile(INVOICES_FILE, JSON.stringify(invoices, null, 2));\n}\n/**\n * Generate order number\n */ function generateOrderNumber() {\n    const timestamp = Date.now().toString();\n    const random = Math.random().toString(36).substr(2, 4).toUpperCase();\n    return `EB${timestamp.slice(-6)}${random}`;\n}\n/**\n * Generate invoice number\n */ function generateInvoiceNumber() {\n    const timestamp = Date.now().toString();\n    const random = Math.random().toString(36).substr(2, 3).toUpperCase();\n    return `INV-${timestamp.slice(-6)}-${random}`;\n}\n/**\n * Create a new order\n */ async function createOrder(orderData) {\n    const orders = await loadOrders();\n    const newOrder = {\n        ...orderData,\n        id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n        orderNumber: generateOrderNumber(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    };\n    orders.push(newOrder);\n    await saveOrders(orders);\n    return newOrder;\n}\n/**\n * Get all orders\n */ async function getAllOrders() {\n    return await loadOrders();\n}\n/**\n * Get order by ID\n */ async function getOrderById(orderId) {\n    const orders = await loadOrders();\n    return orders.find((order)=>order.id === orderId) || null;\n}\n/**\n * Get orders by customer ID\n */ async function getOrdersByCustomerId(customerId) {\n    const orders = await loadOrders();\n    return orders.filter((order)=>order.customerId === customerId);\n}\n/**\n * Update order\n */ async function updateOrder(orderId, updates) {\n    const orders = await loadOrders();\n    const orderIndex = orders.findIndex((order)=>order.id === orderId);\n    if (orderIndex === -1) {\n        return false;\n    }\n    orders[orderIndex] = {\n        ...orders[orderIndex],\n        ...updates,\n        updatedAt: new Date().toISOString()\n    };\n    await saveOrders(orders);\n    return true;\n}\n/**\n * Update order status\n */ async function updateOrderStatus(orderId, status) {\n    return await updateOrder(orderId, {\n        status\n    });\n}\n/**\n * Update payment status\n */ async function updatePaymentStatus(orderId, paymentStatus) {\n    const order = await getOrderById(orderId);\n    if (!order) return false;\n    const updatedPayment = {\n        ...order.payment,\n        ...paymentStatus\n    };\n    return await updateOrder(orderId, {\n        payment: updatedPayment\n    });\n}\n/**\n * Add shipping update\n */ async function addShippingUpdate(orderId, update) {\n    const order = await getOrderById(orderId);\n    if (!order) return false;\n    const newUpdate = {\n        ...update,\n        id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])()\n    };\n    const updatedShipping = {\n        ...order.shipping,\n        updates: [\n            ...order.shipping.updates,\n            newUpdate\n        ]\n    };\n    return await updateOrder(orderId, {\n        shipping: updatedShipping\n    });\n}\n/**\n * Create invoice\n */ async function createInvoice(invoiceData) {\n    const invoices = await loadInvoices();\n    const newInvoice = {\n        ...invoiceData,\n        id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n        invoiceNumber: generateInvoiceNumber()\n    };\n    invoices.push(newInvoice);\n    await saveInvoices(invoices);\n    return newInvoice;\n}\n/**\n * Get invoice by order ID\n */ async function getInvoiceByOrderId(orderId) {\n    const invoices = await loadInvoices();\n    return invoices.find((invoice)=>invoice.orderId === orderId) || null;\n}\n/**\n * Update invoice\n */ async function updateInvoice(invoiceId, updates) {\n    const invoices = await loadInvoices();\n    const invoiceIndex = invoices.findIndex((invoice)=>invoice.id === invoiceId);\n    if (invoiceIndex === -1) {\n        return false;\n    }\n    invoices[invoiceIndex] = {\n        ...invoices[invoiceIndex],\n        ...updates\n    };\n    await saveInvoices(invoices);\n    return true;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/orderStorage.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/paymentConfig.ts":
/*!**********************************!*\
  !*** ./src/lib/paymentConfig.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PAYMENT_METHODS: () => (/* binding */ PAYMENT_METHODS),\n/* harmony export */   SHIPPING_METHODS: () => (/* binding */ SHIPPING_METHODS),\n/* harmony export */   VEHICLE_WEIGHTS: () => (/* binding */ VEHICLE_WEIGHTS),\n/* harmony export */   calculateShippingCost: () => (/* binding */ calculateShippingCost),\n/* harmony export */   getEnabledPaymentMethods: () => (/* binding */ getEnabledPaymentMethods),\n/* harmony export */   getEstimatedDeliveryDate: () => (/* binding */ getEstimatedDeliveryDate),\n/* harmony export */   getPaymentInstructions: () => (/* binding */ getPaymentInstructions),\n/* harmony export */   getPaymentMethodById: () => (/* binding */ getPaymentMethodById),\n/* harmony export */   getShippingMethodById: () => (/* binding */ getShippingMethodById),\n/* harmony export */   validatePaymentMethodForLocation: () => (/* binding */ validatePaymentMethodForLocation)\n/* harmony export */ });\n/**\n * Available payment methods for Ghana and international customers\n */ const PAYMENT_METHODS = [\n    {\n        id: 'bank_transfer_ghana',\n        type: 'bank_transfer',\n        name: 'Bank Transfer (Ghana)',\n        description: 'Direct bank transfer to our Ghana account',\n        icon: '🏦',\n        enabled: true,\n        config: {\n            bankName: 'Ghana Commercial Bank',\n            accountNumber: '*************',\n            accountName: 'EBAM Motors Ghana Ltd',\n            swiftCode: 'GCBLGHAC'\n        }\n    },\n    {\n        id: 'bank_transfer_japan',\n        type: 'bank_transfer',\n        name: 'Bank Transfer (Japan)',\n        description: 'Direct bank transfer to our Japan account',\n        icon: '🏦',\n        enabled: true,\n        config: {\n            bankName: 'Mizuho Bank',\n            accountNumber: '*************',\n            accountName: 'EBAM Motors Japan KK',\n            swiftCode: 'MHCBJPJT'\n        }\n    },\n    {\n        id: 'mtn_mobile_money',\n        type: 'mobile_money',\n        name: 'MTN Mobile Money',\n        description: 'Pay using MTN Mobile Money',\n        icon: '📱',\n        enabled: true,\n        config: {\n            provider: 'mtn',\n            number: '+************'\n        }\n    },\n    {\n        id: 'vodafone_cash',\n        type: 'mobile_money',\n        name: 'Vodafone Cash',\n        description: 'Pay using Vodafone Cash',\n        icon: '📱',\n        enabled: true,\n        config: {\n            provider: 'vodafone',\n            number: '+************'\n        }\n    },\n    {\n        id: 'airtel_money',\n        type: 'mobile_money',\n        name: 'AirtelTigo Money',\n        description: 'Pay using AirtelTigo Money',\n        icon: '📱',\n        enabled: true,\n        config: {\n            provider: 'airtel',\n            number: '+************'\n        }\n    },\n    {\n        id: 'stripe_card',\n        type: 'stripe',\n        name: 'Credit/Debit Card',\n        description: 'Pay securely with your credit or debit card',\n        icon: '💳',\n        enabled: true,\n        config: {\n            publishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY\n        }\n    },\n    {\n        id: 'cash_agent_kumasi',\n        type: 'cash_agent',\n        name: 'Cash Payment (Kumasi)',\n        description: 'Pay cash through our agent in Kumasi',\n        icon: '💵',\n        enabled: true,\n        config: {\n            agentLocations: [\n                'Kumasi Central Market',\n                'Adum Shopping Center',\n                'KNUST Campus'\n            ]\n        }\n    },\n    {\n        id: 'cash_agent_accra',\n        type: 'cash_agent',\n        name: 'Cash Payment (Accra)',\n        description: 'Pay cash through our agent in Accra',\n        icon: '💵',\n        enabled: true,\n        config: {\n            agentLocations: [\n                'Makola Market',\n                'Osu Oxford Street',\n                'East Legon'\n            ]\n        }\n    }\n];\n/**\n * Available shipping methods\n */ const SHIPPING_METHODS = [\n    {\n        id: 'sea_freight_standard',\n        name: 'Sea Freight (Standard)',\n        description: 'Most economical option, 4-6 weeks delivery',\n        estimatedDays: 35,\n        basePrice: 150000,\n        pricePerKg: 50,\n        maxWeight: 2000,\n        trackingIncluded: true,\n        insuranceIncluded: true\n    },\n    {\n        id: 'sea_freight_express',\n        name: 'Sea Freight (Express)',\n        description: 'Faster sea shipping, 3-4 weeks delivery',\n        estimatedDays: 25,\n        basePrice: 200000,\n        pricePerKg: 75,\n        maxWeight: 2000,\n        trackingIncluded: true,\n        insuranceIncluded: true\n    },\n    {\n        id: 'air_freight',\n        name: 'Air Freight',\n        description: 'Fastest option, 1-2 weeks delivery',\n        estimatedDays: 10,\n        basePrice: 500000,\n        pricePerKg: 200,\n        maxWeight: 500,\n        trackingIncluded: true,\n        insuranceIncluded: true\n    }\n];\n/**\n * Vehicle weight estimation based on category\n */ const VEHICLE_WEIGHTS = {\n    'compact': 1200,\n    'sedan': 1400,\n    'suv': 1800,\n    'van': 1600,\n    'truck': 2500,\n    'motorcycle': 200,\n    'default': 1500\n};\n/**\n * Get payment method by ID\n */ function getPaymentMethodById(id) {\n    return PAYMENT_METHODS.find((method)=>method.id === id) || null;\n}\n/**\n * Get enabled payment methods\n */ function getEnabledPaymentMethods() {\n    return PAYMENT_METHODS.filter((method)=>method.enabled);\n}\n/**\n * Get shipping method by ID\n */ function getShippingMethodById(id) {\n    return SHIPPING_METHODS.find((method)=>method.id === id) || null;\n}\n/**\n * Calculate shipping cost\n */ function calculateShippingCost(shippingMethodId, vehicleCategory, destination = 'ghana') {\n    const method = getShippingMethodById(shippingMethodId);\n    if (!method) return 0;\n    const weight = VEHICLE_WEIGHTS[vehicleCategory.toLowerCase()] || VEHICLE_WEIGHTS.default;\n    const weightCost = method.pricePerKg ? weight * method.pricePerKg : 0;\n    // Add destination multiplier\n    let destinationMultiplier = 1;\n    if (destination.toLowerCase() !== 'ghana') {\n        destinationMultiplier = 1.2; // 20% extra for other destinations\n    }\n    return Math.round((method.basePrice + weightCost) * destinationMultiplier);\n}\n/**\n * Get estimated delivery date\n */ function getEstimatedDeliveryDate(shippingMethodId) {\n    const method = getShippingMethodById(shippingMethodId);\n    if (!method) return '';\n    const deliveryDate = new Date();\n    deliveryDate.setDate(deliveryDate.getDate() + method.estimatedDays);\n    return deliveryDate.toISOString().split('T')[0];\n}\n/**\n * Validate payment method for customer location\n */ function validatePaymentMethodForLocation(paymentMethodId, customerCountry) {\n    const method = getPaymentMethodById(paymentMethodId);\n    if (!method) return false;\n    // Mobile money only available in Ghana\n    if (method.type === 'mobile_money' && customerCountry.toLowerCase() !== 'ghana') {\n        return false;\n    }\n    // Cash agents only available in Ghana\n    if (method.type === 'cash_agent' && customerCountry.toLowerCase() !== 'ghana') {\n        return false;\n    }\n    return true;\n}\n/**\n * Get payment instructions for a method\n */ function getPaymentInstructions(paymentMethodId, amount, orderId) {\n    const method = getPaymentMethodById(paymentMethodId);\n    if (!method) return '';\n    switch(method.type){\n        case 'bank_transfer':\n            return `Transfer ¥${amount.toLocaleString()} to:\nBank: ${method.config?.bankName}\nAccount: ${method.config?.accountNumber}\nName: ${method.config?.accountName}\nReference: ${orderId}`;\n        case 'mobile_money':\n            return `Send ¥${amount.toLocaleString()} to ${method.config?.number}\nReference: ${orderId}\nNetwork: ${method.name}`;\n        case 'cash_agent':\n            return `Visit one of our agent locations with ¥${amount.toLocaleString()}:\n${method.config?.agentLocations?.join(', ')}\nReference: ${orderId}`;\n        case 'stripe':\n            return `You will be redirected to secure payment page to complete your payment of ¥${amount.toLocaleString()}.`;\n        default:\n            return `Payment amount: ¥${amount.toLocaleString()}\nOrder reference: ${orderId}`;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3BheW1lbnRDb25maWcudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUVBOztDQUVDLEdBQ00sTUFBTUEsa0JBQW1DO0lBQzlDO1FBQ0VDLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxNQUFNO1FBQ05DLGFBQWE7UUFDYkMsTUFBTTtRQUNOQyxTQUFTO1FBQ1RDLFFBQVE7WUFDTkMsVUFBVTtZQUNWQyxlQUFlO1lBQ2ZDLGFBQWE7WUFDYkMsV0FBVztRQUNiO0lBQ0Y7SUFDQTtRQUNFVixJQUFJO1FBQ0pDLE1BQU07UUFDTkMsTUFBTTtRQUNOQyxhQUFhO1FBQ2JDLE1BQU07UUFDTkMsU0FBUztRQUNUQyxRQUFRO1lBQ05DLFVBQVU7WUFDVkMsZUFBZTtZQUNmQyxhQUFhO1lBQ2JDLFdBQVc7UUFDYjtJQUNGO0lBQ0E7UUFDRVYsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsUUFBUTtZQUNOSyxVQUFVO1lBQ1ZDLFFBQVE7UUFDVjtJQUNGO0lBQ0E7UUFDRVosSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsUUFBUTtZQUNOSyxVQUFVO1lBQ1ZDLFFBQVE7UUFDVjtJQUNGO0lBQ0E7UUFDRVosSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsUUFBUTtZQUNOSyxVQUFVO1lBQ1ZDLFFBQVE7UUFDVjtJQUNGO0lBQ0E7UUFDRVosSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsUUFBUTtZQUNOTyxnQkFBZ0JDLFFBQVFDLEdBQUcsQ0FBQ0Msa0NBQWtDO1FBQ2hFO0lBQ0Y7SUFDQTtRQUNFaEIsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsUUFBUTtZQUNOVyxnQkFBZ0I7Z0JBQUM7Z0JBQXlCO2dCQUF3QjthQUFlO1FBQ25GO0lBQ0Y7SUFDQTtRQUNFakIsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsUUFBUTtZQUNOVyxnQkFBZ0I7Z0JBQUM7Z0JBQWlCO2dCQUFxQjthQUFhO1FBQ3RFO0lBQ0Y7Q0FDRCxDQUFDO0FBRUY7O0NBRUMsR0FDTSxNQUFNQyxtQkFBcUM7SUFDaEQ7UUFDRWxCLElBQUk7UUFDSkUsTUFBTTtRQUNOQyxhQUFhO1FBQ2JnQixlQUFlO1FBQ2ZDLFdBQVc7UUFDWEMsWUFBWTtRQUNaQyxXQUFXO1FBQ1hDLGtCQUFrQjtRQUNsQkMsbUJBQW1CO0lBQ3JCO0lBQ0E7UUFDRXhCLElBQUk7UUFDSkUsTUFBTTtRQUNOQyxhQUFhO1FBQ2JnQixlQUFlO1FBQ2ZDLFdBQVc7UUFDWEMsWUFBWTtRQUNaQyxXQUFXO1FBQ1hDLGtCQUFrQjtRQUNsQkMsbUJBQW1CO0lBQ3JCO0lBQ0E7UUFDRXhCLElBQUk7UUFDSkUsTUFBTTtRQUNOQyxhQUFhO1FBQ2JnQixlQUFlO1FBQ2ZDLFdBQVc7UUFDWEMsWUFBWTtRQUNaQyxXQUFXO1FBQ1hDLGtCQUFrQjtRQUNsQkMsbUJBQW1CO0lBQ3JCO0NBQ0QsQ0FBQztBQUVGOztDQUVDLEdBQ00sTUFBTUMsa0JBQTBDO0lBQ3JELFdBQVc7SUFDWCxTQUFTO0lBQ1QsT0FBTztJQUNQLE9BQU87SUFDUCxTQUFTO0lBQ1QsY0FBYztJQUNkLFdBQVc7QUFDYixFQUFFO0FBRUY7O0NBRUMsR0FDTSxTQUFTQyxxQkFBcUIxQixFQUFVO0lBQzdDLE9BQU9ELGdCQUFnQjRCLElBQUksQ0FBQ0MsQ0FBQUEsU0FBVUEsT0FBTzVCLEVBQUUsS0FBS0EsT0FBTztBQUM3RDtBQUVBOztDQUVDLEdBQ00sU0FBUzZCO0lBQ2QsT0FBTzlCLGdCQUFnQitCLE1BQU0sQ0FBQ0YsQ0FBQUEsU0FBVUEsT0FBT3ZCLE9BQU87QUFDeEQ7QUFFQTs7Q0FFQyxHQUNNLFNBQVMwQixzQkFBc0IvQixFQUFVO0lBQzlDLE9BQU9rQixpQkFBaUJTLElBQUksQ0FBQ0MsQ0FBQUEsU0FBVUEsT0FBTzVCLEVBQUUsS0FBS0EsT0FBTztBQUM5RDtBQUVBOztDQUVDLEdBQ00sU0FBU2dDLHNCQUNkQyxnQkFBd0IsRUFDeEJDLGVBQXVCLEVBQ3ZCQyxjQUFzQixPQUFPO0lBRTdCLE1BQU1QLFNBQVNHLHNCQUFzQkU7SUFDckMsSUFBSSxDQUFDTCxRQUFRLE9BQU87SUFFcEIsTUFBTVEsU0FBU1gsZUFBZSxDQUFDUyxnQkFBZ0JHLFdBQVcsR0FBRyxJQUFJWixnQkFBZ0JhLE9BQU87SUFDeEYsTUFBTUMsYUFBYVgsT0FBT1AsVUFBVSxHQUFHZSxTQUFTUixPQUFPUCxVQUFVLEdBQUc7SUFFcEUsNkJBQTZCO0lBQzdCLElBQUltQix3QkFBd0I7SUFDNUIsSUFBSUwsWUFBWUUsV0FBVyxPQUFPLFNBQVM7UUFDekNHLHdCQUF3QixLQUFLLG1DQUFtQztJQUNsRTtJQUVBLE9BQU9DLEtBQUtDLEtBQUssQ0FBQyxDQUFDZCxPQUFPUixTQUFTLEdBQUdtQixVQUFTLElBQUtDO0FBQ3REO0FBRUE7O0NBRUMsR0FDTSxTQUFTRyx5QkFBeUJWLGdCQUF3QjtJQUMvRCxNQUFNTCxTQUFTRyxzQkFBc0JFO0lBQ3JDLElBQUksQ0FBQ0wsUUFBUSxPQUFPO0lBRXBCLE1BQU1nQixlQUFlLElBQUlDO0lBQ3pCRCxhQUFhRSxPQUFPLENBQUNGLGFBQWFHLE9BQU8sS0FBS25CLE9BQU9ULGFBQWE7SUFFbEUsT0FBT3lCLGFBQWFJLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO0FBQ2pEO0FBRUE7O0NBRUMsR0FDTSxTQUFTQyxpQ0FDZEMsZUFBdUIsRUFDdkJDLGVBQXVCO0lBRXZCLE1BQU14QixTQUFTRixxQkFBcUJ5QjtJQUNwQyxJQUFJLENBQUN2QixRQUFRLE9BQU87SUFFcEIsdUNBQXVDO0lBQ3ZDLElBQUlBLE9BQU8zQixJQUFJLEtBQUssa0JBQWtCbUQsZ0JBQWdCZixXQUFXLE9BQU8sU0FBUztRQUMvRSxPQUFPO0lBQ1Q7SUFFQSxzQ0FBc0M7SUFDdEMsSUFBSVQsT0FBTzNCLElBQUksS0FBSyxnQkFBZ0JtRCxnQkFBZ0JmLFdBQVcsT0FBTyxTQUFTO1FBQzdFLE9BQU87SUFDVDtJQUVBLE9BQU87QUFDVDtBQUVBOztDQUVDLEdBQ00sU0FBU2dCLHVCQUF1QkYsZUFBdUIsRUFBRUcsTUFBYyxFQUFFQyxPQUFlO0lBQzdGLE1BQU0zQixTQUFTRixxQkFBcUJ5QjtJQUNwQyxJQUFJLENBQUN2QixRQUFRLE9BQU87SUFFcEIsT0FBUUEsT0FBTzNCLElBQUk7UUFDakIsS0FBSztZQUNILE9BQU8sQ0FBQyxVQUFVLEVBQUVxRCxPQUFPRSxjQUFjLEdBQUc7TUFDNUMsRUFBRTVCLE9BQU90QixNQUFNLEVBQUVDLFNBQVM7U0FDdkIsRUFBRXFCLE9BQU90QixNQUFNLEVBQUVFLGNBQWM7TUFDbEMsRUFBRW9CLE9BQU90QixNQUFNLEVBQUVHLFlBQVk7V0FDeEIsRUFBRThDLFNBQVM7UUFFbEIsS0FBSztZQUNILE9BQU8sQ0FBQyxNQUFNLEVBQUVELE9BQU9FLGNBQWMsR0FBRyxJQUFJLEVBQUU1QixPQUFPdEIsTUFBTSxFQUFFTSxPQUFPO1dBQy9ELEVBQUUyQyxRQUFRO1NBQ1osRUFBRTNCLE9BQU8xQixJQUFJLEVBQUU7UUFFcEIsS0FBSztZQUNILE9BQU8sQ0FBQyx1Q0FBdUMsRUFBRW9ELE9BQU9FLGNBQWMsR0FBRztBQUMvRSxFQUFFNUIsT0FBT3RCLE1BQU0sRUFBRVcsZ0JBQWdCd0MsS0FBSyxNQUFNO1dBQ2pDLEVBQUVGLFNBQVM7UUFFbEIsS0FBSztZQUNILE9BQU8sQ0FBQywyRUFBMkUsRUFBRUQsT0FBT0UsY0FBYyxHQUFHLENBQUMsQ0FBQztRQUVqSDtZQUNFLE9BQU8sQ0FBQyxpQkFBaUIsRUFBRUYsT0FBT0UsY0FBYyxHQUFHO2lCQUN4QyxFQUFFRCxTQUFTO0lBQzFCO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVXNlclxcRGVza3RvcFxcd2Vic2l0ZVxcZWJhbW1vdG9yc1xcc3JjXFxsaWJcXHBheW1lbnRDb25maWcudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGF5bWVudE1ldGhvZCwgU2hpcHBpbmdNZXRob2QgfSBmcm9tICdAL3R5cGVzL3BheW1lbnQnO1xuXG4vKipcbiAqIEF2YWlsYWJsZSBwYXltZW50IG1ldGhvZHMgZm9yIEdoYW5hIGFuZCBpbnRlcm5hdGlvbmFsIGN1c3RvbWVyc1xuICovXG5leHBvcnQgY29uc3QgUEFZTUVOVF9NRVRIT0RTOiBQYXltZW50TWV0aG9kW10gPSBbXG4gIHtcbiAgICBpZDogJ2JhbmtfdHJhbnNmZXJfZ2hhbmEnLFxuICAgIHR5cGU6ICdiYW5rX3RyYW5zZmVyJyxcbiAgICBuYW1lOiAnQmFuayBUcmFuc2ZlciAoR2hhbmEpJyxcbiAgICBkZXNjcmlwdGlvbjogJ0RpcmVjdCBiYW5rIHRyYW5zZmVyIHRvIG91ciBHaGFuYSBhY2NvdW50JyxcbiAgICBpY29uOiAn8J+PpicsXG4gICAgZW5hYmxlZDogdHJ1ZSxcbiAgICBjb25maWc6IHtcbiAgICAgIGJhbmtOYW1lOiAnR2hhbmEgQ29tbWVyY2lhbCBCYW5rJyxcbiAgICAgIGFjY291bnROdW1iZXI6ICcxMjM0NTY3ODkwMTIzJyxcbiAgICAgIGFjY291bnROYW1lOiAnRUJBTSBNb3RvcnMgR2hhbmEgTHRkJyxcbiAgICAgIHN3aWZ0Q29kZTogJ0dDQkxHSEFDJyxcbiAgICB9LFxuICB9LFxuICB7XG4gICAgaWQ6ICdiYW5rX3RyYW5zZmVyX2phcGFuJyxcbiAgICB0eXBlOiAnYmFua190cmFuc2ZlcicsXG4gICAgbmFtZTogJ0JhbmsgVHJhbnNmZXIgKEphcGFuKScsXG4gICAgZGVzY3JpcHRpb246ICdEaXJlY3QgYmFuayB0cmFuc2ZlciB0byBvdXIgSmFwYW4gYWNjb3VudCcsXG4gICAgaWNvbjogJ/Cfj6YnLFxuICAgIGVuYWJsZWQ6IHRydWUsXG4gICAgY29uZmlnOiB7XG4gICAgICBiYW5rTmFtZTogJ01penVobyBCYW5rJyxcbiAgICAgIGFjY291bnROdW1iZXI6ICc5ODc2NTQzMjEwOTg3JyxcbiAgICAgIGFjY291bnROYW1lOiAnRUJBTSBNb3RvcnMgSmFwYW4gS0snLFxuICAgICAgc3dpZnRDb2RlOiAnTUhDQkpQSlQnLFxuICAgIH0sXG4gIH0sXG4gIHtcbiAgICBpZDogJ210bl9tb2JpbGVfbW9uZXknLFxuICAgIHR5cGU6ICdtb2JpbGVfbW9uZXknLFxuICAgIG5hbWU6ICdNVE4gTW9iaWxlIE1vbmV5JyxcbiAgICBkZXNjcmlwdGlvbjogJ1BheSB1c2luZyBNVE4gTW9iaWxlIE1vbmV5JyxcbiAgICBpY29uOiAn8J+TsScsXG4gICAgZW5hYmxlZDogdHJ1ZSxcbiAgICBjb25maWc6IHtcbiAgICAgIHByb3ZpZGVyOiAnbXRuJyxcbiAgICAgIG51bWJlcjogJysyMzMyNDUzNzU2OTInLFxuICAgIH0sXG4gIH0sXG4gIHtcbiAgICBpZDogJ3ZvZGFmb25lX2Nhc2gnLFxuICAgIHR5cGU6ICdtb2JpbGVfbW9uZXknLFxuICAgIG5hbWU6ICdWb2RhZm9uZSBDYXNoJyxcbiAgICBkZXNjcmlwdGlvbjogJ1BheSB1c2luZyBWb2RhZm9uZSBDYXNoJyxcbiAgICBpY29uOiAn8J+TsScsXG4gICAgZW5hYmxlZDogdHJ1ZSxcbiAgICBjb25maWc6IHtcbiAgICAgIHByb3ZpZGVyOiAndm9kYWZvbmUnLFxuICAgICAgbnVtYmVyOiAnKzIzMzI0NTM3NTY5MicsXG4gICAgfSxcbiAgfSxcbiAge1xuICAgIGlkOiAnYWlydGVsX21vbmV5JyxcbiAgICB0eXBlOiAnbW9iaWxlX21vbmV5JyxcbiAgICBuYW1lOiAnQWlydGVsVGlnbyBNb25leScsXG4gICAgZGVzY3JpcHRpb246ICdQYXkgdXNpbmcgQWlydGVsVGlnbyBNb25leScsXG4gICAgaWNvbjogJ/Cfk7EnLFxuICAgIGVuYWJsZWQ6IHRydWUsXG4gICAgY29uZmlnOiB7XG4gICAgICBwcm92aWRlcjogJ2FpcnRlbCcsXG4gICAgICBudW1iZXI6ICcrMjMzMjQ1Mzc1NjkyJyxcbiAgICB9LFxuICB9LFxuICB7XG4gICAgaWQ6ICdzdHJpcGVfY2FyZCcsXG4gICAgdHlwZTogJ3N0cmlwZScsXG4gICAgbmFtZTogJ0NyZWRpdC9EZWJpdCBDYXJkJyxcbiAgICBkZXNjcmlwdGlvbjogJ1BheSBzZWN1cmVseSB3aXRoIHlvdXIgY3JlZGl0IG9yIGRlYml0IGNhcmQnLFxuICAgIGljb246ICfwn5KzJyxcbiAgICBlbmFibGVkOiB0cnVlLFxuICAgIGNvbmZpZzoge1xuICAgICAgcHVibGlzaGFibGVLZXk6IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NUUklQRV9QVUJMSVNIQUJMRV9LRVksXG4gICAgfSxcbiAgfSxcbiAge1xuICAgIGlkOiAnY2FzaF9hZ2VudF9rdW1hc2knLFxuICAgIHR5cGU6ICdjYXNoX2FnZW50JyxcbiAgICBuYW1lOiAnQ2FzaCBQYXltZW50IChLdW1hc2kpJyxcbiAgICBkZXNjcmlwdGlvbjogJ1BheSBjYXNoIHRocm91Z2ggb3VyIGFnZW50IGluIEt1bWFzaScsXG4gICAgaWNvbjogJ/CfkrUnLFxuICAgIGVuYWJsZWQ6IHRydWUsXG4gICAgY29uZmlnOiB7XG4gICAgICBhZ2VudExvY2F0aW9uczogWydLdW1hc2kgQ2VudHJhbCBNYXJrZXQnLCAnQWR1bSBTaG9wcGluZyBDZW50ZXInLCAnS05VU1QgQ2FtcHVzJ10sXG4gICAgfSxcbiAgfSxcbiAge1xuICAgIGlkOiAnY2FzaF9hZ2VudF9hY2NyYScsXG4gICAgdHlwZTogJ2Nhc2hfYWdlbnQnLFxuICAgIG5hbWU6ICdDYXNoIFBheW1lbnQgKEFjY3JhKScsXG4gICAgZGVzY3JpcHRpb246ICdQYXkgY2FzaCB0aHJvdWdoIG91ciBhZ2VudCBpbiBBY2NyYScsXG4gICAgaWNvbjogJ/CfkrUnLFxuICAgIGVuYWJsZWQ6IHRydWUsXG4gICAgY29uZmlnOiB7XG4gICAgICBhZ2VudExvY2F0aW9uczogWydNYWtvbGEgTWFya2V0JywgJ09zdSBPeGZvcmQgU3RyZWV0JywgJ0Vhc3QgTGVnb24nXSxcbiAgICB9LFxuICB9LFxuXTtcblxuLyoqXG4gKiBBdmFpbGFibGUgc2hpcHBpbmcgbWV0aG9kc1xuICovXG5leHBvcnQgY29uc3QgU0hJUFBJTkdfTUVUSE9EUzogU2hpcHBpbmdNZXRob2RbXSA9IFtcbiAge1xuICAgIGlkOiAnc2VhX2ZyZWlnaHRfc3RhbmRhcmQnLFxuICAgIG5hbWU6ICdTZWEgRnJlaWdodCAoU3RhbmRhcmQpJyxcbiAgICBkZXNjcmlwdGlvbjogJ01vc3QgZWNvbm9taWNhbCBvcHRpb24sIDQtNiB3ZWVrcyBkZWxpdmVyeScsXG4gICAgZXN0aW1hdGVkRGF5czogMzUsXG4gICAgYmFzZVByaWNlOiAxNTAwMDAsIC8vIMKlMTUwLDAwMFxuICAgIHByaWNlUGVyS2c6IDUwLFxuICAgIG1heFdlaWdodDogMjAwMCxcbiAgICB0cmFja2luZ0luY2x1ZGVkOiB0cnVlLFxuICAgIGluc3VyYW5jZUluY2x1ZGVkOiB0cnVlLFxuICB9LFxuICB7XG4gICAgaWQ6ICdzZWFfZnJlaWdodF9leHByZXNzJyxcbiAgICBuYW1lOiAnU2VhIEZyZWlnaHQgKEV4cHJlc3MpJyxcbiAgICBkZXNjcmlwdGlvbjogJ0Zhc3RlciBzZWEgc2hpcHBpbmcsIDMtNCB3ZWVrcyBkZWxpdmVyeScsXG4gICAgZXN0aW1hdGVkRGF5czogMjUsXG4gICAgYmFzZVByaWNlOiAyMDAwMDAsIC8vIMKlMjAwLDAwMFxuICAgIHByaWNlUGVyS2c6IDc1LFxuICAgIG1heFdlaWdodDogMjAwMCxcbiAgICB0cmFja2luZ0luY2x1ZGVkOiB0cnVlLFxuICAgIGluc3VyYW5jZUluY2x1ZGVkOiB0cnVlLFxuICB9LFxuICB7XG4gICAgaWQ6ICdhaXJfZnJlaWdodCcsXG4gICAgbmFtZTogJ0FpciBGcmVpZ2h0JyxcbiAgICBkZXNjcmlwdGlvbjogJ0Zhc3Rlc3Qgb3B0aW9uLCAxLTIgd2Vla3MgZGVsaXZlcnknLFxuICAgIGVzdGltYXRlZERheXM6IDEwLFxuICAgIGJhc2VQcmljZTogNTAwMDAwLCAvLyDCpTUwMCwwMDBcbiAgICBwcmljZVBlcktnOiAyMDAsXG4gICAgbWF4V2VpZ2h0OiA1MDAsXG4gICAgdHJhY2tpbmdJbmNsdWRlZDogdHJ1ZSxcbiAgICBpbnN1cmFuY2VJbmNsdWRlZDogdHJ1ZSxcbiAgfSxcbl07XG5cbi8qKlxuICogVmVoaWNsZSB3ZWlnaHQgZXN0aW1hdGlvbiBiYXNlZCBvbiBjYXRlZ29yeVxuICovXG5leHBvcnQgY29uc3QgVkVISUNMRV9XRUlHSFRTOiBSZWNvcmQ8c3RyaW5nLCBudW1iZXI+ID0ge1xuICAnY29tcGFjdCc6IDEyMDAsIC8vIGtnXG4gICdzZWRhbic6IDE0MDAsXG4gICdzdXYnOiAxODAwLFxuICAndmFuJzogMTYwMCxcbiAgJ3RydWNrJzogMjUwMCxcbiAgJ21vdG9yY3ljbGUnOiAyMDAsXG4gICdkZWZhdWx0JzogMTUwMCxcbn07XG5cbi8qKlxuICogR2V0IHBheW1lbnQgbWV0aG9kIGJ5IElEXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRQYXltZW50TWV0aG9kQnlJZChpZDogc3RyaW5nKTogUGF5bWVudE1ldGhvZCB8IG51bGwge1xuICByZXR1cm4gUEFZTUVOVF9NRVRIT0RTLmZpbmQobWV0aG9kID0+IG1ldGhvZC5pZCA9PT0gaWQpIHx8IG51bGw7XG59XG5cbi8qKlxuICogR2V0IGVuYWJsZWQgcGF5bWVudCBtZXRob2RzXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRFbmFibGVkUGF5bWVudE1ldGhvZHMoKTogUGF5bWVudE1ldGhvZFtdIHtcbiAgcmV0dXJuIFBBWU1FTlRfTUVUSE9EUy5maWx0ZXIobWV0aG9kID0+IG1ldGhvZC5lbmFibGVkKTtcbn1cblxuLyoqXG4gKiBHZXQgc2hpcHBpbmcgbWV0aG9kIGJ5IElEXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRTaGlwcGluZ01ldGhvZEJ5SWQoaWQ6IHN0cmluZyk6IFNoaXBwaW5nTWV0aG9kIHwgbnVsbCB7XG4gIHJldHVybiBTSElQUElOR19NRVRIT0RTLmZpbmQobWV0aG9kID0+IG1ldGhvZC5pZCA9PT0gaWQpIHx8IG51bGw7XG59XG5cbi8qKlxuICogQ2FsY3VsYXRlIHNoaXBwaW5nIGNvc3RcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNhbGN1bGF0ZVNoaXBwaW5nQ29zdChcbiAgc2hpcHBpbmdNZXRob2RJZDogc3RyaW5nLFxuICB2ZWhpY2xlQ2F0ZWdvcnk6IHN0cmluZyxcbiAgZGVzdGluYXRpb246IHN0cmluZyA9ICdnaGFuYSdcbik6IG51bWJlciB7XG4gIGNvbnN0IG1ldGhvZCA9IGdldFNoaXBwaW5nTWV0aG9kQnlJZChzaGlwcGluZ01ldGhvZElkKTtcbiAgaWYgKCFtZXRob2QpIHJldHVybiAwO1xuXG4gIGNvbnN0IHdlaWdodCA9IFZFSElDTEVfV0VJR0hUU1t2ZWhpY2xlQ2F0ZWdvcnkudG9Mb3dlckNhc2UoKV0gfHwgVkVISUNMRV9XRUlHSFRTLmRlZmF1bHQ7XG4gIGNvbnN0IHdlaWdodENvc3QgPSBtZXRob2QucHJpY2VQZXJLZyA/IHdlaWdodCAqIG1ldGhvZC5wcmljZVBlcktnIDogMDtcbiAgXG4gIC8vIEFkZCBkZXN0aW5hdGlvbiBtdWx0aXBsaWVyXG4gIGxldCBkZXN0aW5hdGlvbk11bHRpcGxpZXIgPSAxO1xuICBpZiAoZGVzdGluYXRpb24udG9Mb3dlckNhc2UoKSAhPT0gJ2doYW5hJykge1xuICAgIGRlc3RpbmF0aW9uTXVsdGlwbGllciA9IDEuMjsgLy8gMjAlIGV4dHJhIGZvciBvdGhlciBkZXN0aW5hdGlvbnNcbiAgfVxuXG4gIHJldHVybiBNYXRoLnJvdW5kKChtZXRob2QuYmFzZVByaWNlICsgd2VpZ2h0Q29zdCkgKiBkZXN0aW5hdGlvbk11bHRpcGxpZXIpO1xufVxuXG4vKipcbiAqIEdldCBlc3RpbWF0ZWQgZGVsaXZlcnkgZGF0ZVxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0RXN0aW1hdGVkRGVsaXZlcnlEYXRlKHNoaXBwaW5nTWV0aG9kSWQ6IHN0cmluZyk6IHN0cmluZyB7XG4gIGNvbnN0IG1ldGhvZCA9IGdldFNoaXBwaW5nTWV0aG9kQnlJZChzaGlwcGluZ01ldGhvZElkKTtcbiAgaWYgKCFtZXRob2QpIHJldHVybiAnJztcblxuICBjb25zdCBkZWxpdmVyeURhdGUgPSBuZXcgRGF0ZSgpO1xuICBkZWxpdmVyeURhdGUuc2V0RGF0ZShkZWxpdmVyeURhdGUuZ2V0RGF0ZSgpICsgbWV0aG9kLmVzdGltYXRlZERheXMpO1xuICBcbiAgcmV0dXJuIGRlbGl2ZXJ5RGF0ZS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF07XG59XG5cbi8qKlxuICogVmFsaWRhdGUgcGF5bWVudCBtZXRob2QgZm9yIGN1c3RvbWVyIGxvY2F0aW9uXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB2YWxpZGF0ZVBheW1lbnRNZXRob2RGb3JMb2NhdGlvbihcbiAgcGF5bWVudE1ldGhvZElkOiBzdHJpbmcsXG4gIGN1c3RvbWVyQ291bnRyeTogc3RyaW5nXG4pOiBib29sZWFuIHtcbiAgY29uc3QgbWV0aG9kID0gZ2V0UGF5bWVudE1ldGhvZEJ5SWQocGF5bWVudE1ldGhvZElkKTtcbiAgaWYgKCFtZXRob2QpIHJldHVybiBmYWxzZTtcblxuICAvLyBNb2JpbGUgbW9uZXkgb25seSBhdmFpbGFibGUgaW4gR2hhbmFcbiAgaWYgKG1ldGhvZC50eXBlID09PSAnbW9iaWxlX21vbmV5JyAmJiBjdXN0b21lckNvdW50cnkudG9Mb3dlckNhc2UoKSAhPT0gJ2doYW5hJykge1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxuXG4gIC8vIENhc2ggYWdlbnRzIG9ubHkgYXZhaWxhYmxlIGluIEdoYW5hXG4gIGlmIChtZXRob2QudHlwZSA9PT0gJ2Nhc2hfYWdlbnQnICYmIGN1c3RvbWVyQ291bnRyeS50b0xvd2VyQ2FzZSgpICE9PSAnZ2hhbmEnKSB7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG5cbiAgcmV0dXJuIHRydWU7XG59XG5cbi8qKlxuICogR2V0IHBheW1lbnQgaW5zdHJ1Y3Rpb25zIGZvciBhIG1ldGhvZFxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0UGF5bWVudEluc3RydWN0aW9ucyhwYXltZW50TWV0aG9kSWQ6IHN0cmluZywgYW1vdW50OiBudW1iZXIsIG9yZGVySWQ6IHN0cmluZyk6IHN0cmluZyB7XG4gIGNvbnN0IG1ldGhvZCA9IGdldFBheW1lbnRNZXRob2RCeUlkKHBheW1lbnRNZXRob2RJZCk7XG4gIGlmICghbWV0aG9kKSByZXR1cm4gJyc7XG5cbiAgc3dpdGNoIChtZXRob2QudHlwZSkge1xuICAgIGNhc2UgJ2JhbmtfdHJhbnNmZXInOlxuICAgICAgcmV0dXJuIGBUcmFuc2ZlciDCpSR7YW1vdW50LnRvTG9jYWxlU3RyaW5nKCl9IHRvOlxuQmFuazogJHttZXRob2QuY29uZmlnPy5iYW5rTmFtZX1cbkFjY291bnQ6ICR7bWV0aG9kLmNvbmZpZz8uYWNjb3VudE51bWJlcn1cbk5hbWU6ICR7bWV0aG9kLmNvbmZpZz8uYWNjb3VudE5hbWV9XG5SZWZlcmVuY2U6ICR7b3JkZXJJZH1gO1xuXG4gICAgY2FzZSAnbW9iaWxlX21vbmV5JzpcbiAgICAgIHJldHVybiBgU2VuZCDCpSR7YW1vdW50LnRvTG9jYWxlU3RyaW5nKCl9IHRvICR7bWV0aG9kLmNvbmZpZz8ubnVtYmVyfVxuUmVmZXJlbmNlOiAke29yZGVySWR9XG5OZXR3b3JrOiAke21ldGhvZC5uYW1lfWA7XG5cbiAgICBjYXNlICdjYXNoX2FnZW50JzpcbiAgICAgIHJldHVybiBgVmlzaXQgb25lIG9mIG91ciBhZ2VudCBsb2NhdGlvbnMgd2l0aCDCpSR7YW1vdW50LnRvTG9jYWxlU3RyaW5nKCl9OlxuJHttZXRob2QuY29uZmlnPy5hZ2VudExvY2F0aW9ucz8uam9pbignLCAnKX1cblJlZmVyZW5jZTogJHtvcmRlcklkfWA7XG5cbiAgICBjYXNlICdzdHJpcGUnOlxuICAgICAgcmV0dXJuIGBZb3Ugd2lsbCBiZSByZWRpcmVjdGVkIHRvIHNlY3VyZSBwYXltZW50IHBhZ2UgdG8gY29tcGxldGUgeW91ciBwYXltZW50IG9mIMKlJHthbW91bnQudG9Mb2NhbGVTdHJpbmcoKX0uYDtcblxuICAgIGRlZmF1bHQ6XG4gICAgICByZXR1cm4gYFBheW1lbnQgYW1vdW50OiDCpSR7YW1vdW50LnRvTG9jYWxlU3RyaW5nKCl9XG5PcmRlciByZWZlcmVuY2U6ICR7b3JkZXJJZH1gO1xuICB9XG59XG4iXSwibmFtZXMiOlsiUEFZTUVOVF9NRVRIT0RTIiwiaWQiLCJ0eXBlIiwibmFtZSIsImRlc2NyaXB0aW9uIiwiaWNvbiIsImVuYWJsZWQiLCJjb25maWciLCJiYW5rTmFtZSIsImFjY291bnROdW1iZXIiLCJhY2NvdW50TmFtZSIsInN3aWZ0Q29kZSIsInByb3ZpZGVyIiwibnVtYmVyIiwicHVibGlzaGFibGVLZXkiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1RSSVBFX1BVQkxJU0hBQkxFX0tFWSIsImFnZW50TG9jYXRpb25zIiwiU0hJUFBJTkdfTUVUSE9EUyIsImVzdGltYXRlZERheXMiLCJiYXNlUHJpY2UiLCJwcmljZVBlcktnIiwibWF4V2VpZ2h0IiwidHJhY2tpbmdJbmNsdWRlZCIsImluc3VyYW5jZUluY2x1ZGVkIiwiVkVISUNMRV9XRUlHSFRTIiwiZ2V0UGF5bWVudE1ldGhvZEJ5SWQiLCJmaW5kIiwibWV0aG9kIiwiZ2V0RW5hYmxlZFBheW1lbnRNZXRob2RzIiwiZmlsdGVyIiwiZ2V0U2hpcHBpbmdNZXRob2RCeUlkIiwiY2FsY3VsYXRlU2hpcHBpbmdDb3N0Iiwic2hpcHBpbmdNZXRob2RJZCIsInZlaGljbGVDYXRlZ29yeSIsImRlc3RpbmF0aW9uIiwid2VpZ2h0IiwidG9Mb3dlckNhc2UiLCJkZWZhdWx0Iiwid2VpZ2h0Q29zdCIsImRlc3RpbmF0aW9uTXVsdGlwbGllciIsIk1hdGgiLCJyb3VuZCIsImdldEVzdGltYXRlZERlbGl2ZXJ5RGF0ZSIsImRlbGl2ZXJ5RGF0ZSIsIkRhdGUiLCJzZXREYXRlIiwiZ2V0RGF0ZSIsInRvSVNPU3RyaW5nIiwic3BsaXQiLCJ2YWxpZGF0ZVBheW1lbnRNZXRob2RGb3JMb2NhdGlvbiIsInBheW1lbnRNZXRob2RJZCIsImN1c3RvbWVyQ291bnRyeSIsImdldFBheW1lbnRJbnN0cnVjdGlvbnMiLCJhbW91bnQiLCJvcmRlcklkIiwidG9Mb2NhbGVTdHJpbmciLCJqb2luIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/paymentConfig.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/resendService.ts":
/*!**********************************!*\
  !*** ./src/lib/resendService.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EMAIL_CONFIG: () => (/* binding */ EMAIL_CONFIG),\n/* harmony export */   ResendEmailService: () => (/* binding */ ResendEmailService),\n/* harmony export */   emailService: () => (/* binding */ emailService)\n/* harmony export */ });\n/* harmony import */ var resend__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! resend */ \"(rsc)/./node_modules/resend/dist/index.mjs\");\n/* harmony import */ var _emailTemplates__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./emailTemplates */ \"(rsc)/./src/lib/emailTemplates.ts\");\n\n\n// Initialize Resend with API key\nconst resend = new resend__WEBPACK_IMPORTED_MODULE_0__.Resend(process.env.RESEND_API_KEY);\n// Email configuration\nconst EMAIL_CONFIG = {\n    from: process.env.RESEND_FROM_EMAIL || 'EBAM Motors <<EMAIL>>',\n    adminEmail: process.env.ADMIN_EMAIL || '<EMAIL>',\n    supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',\n    noReplyEmail: process.env.NO_REPLY_EMAIL || '<EMAIL>'\n};\n// Base email service class\nclass ResendEmailService {\n    constructor(){\n        this.resend = resend;\n    }\n    /**\n   * Send a generic email\n   */ async sendEmail(template) {\n        try {\n            if (!process.env.RESEND_API_KEY) {\n                console.warn('Resend API key not configured. Email not sent.');\n                return {\n                    success: false,\n                    error: 'Resend API key not configured'\n                };\n            }\n            const result = await this.resend.emails.send({\n                from: EMAIL_CONFIG.from,\n                to: template.to,\n                subject: template.subject,\n                html: template.html,\n                text: template.text\n            });\n            if (result.error) {\n                console.error('Resend email error:', result.error);\n                return {\n                    success: false,\n                    error: result.error.message\n                };\n            }\n            console.log('Email sent successfully:', result.data?.id);\n            return {\n                success: true,\n                messageId: result.data?.id\n            };\n        } catch (error) {\n            console.error('Email service error:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    /**\n   * Send order confirmation email\n   */ async sendOrderConfirmation(customerEmail, data) {\n        const template = {\n            to: customerEmail,\n            subject: `Order Confirmation - ${data.orderNumber} | EBAM Motors`,\n            html: this.generateOrderConfirmationHTML(data),\n            text: this.generateOrderConfirmationText(data)\n        };\n        const result = await this.sendEmail(template);\n        return result;\n    }\n    /**\n   * Send review notification to admin\n   */ async sendReviewNotificationToAdmin(data) {\n        const template = {\n            to: EMAIL_CONFIG.adminEmail,\n            subject: `New Review Submitted - ${data.vehicleTitle} | EBAM Motors`,\n            html: this.generateReviewNotificationHTML(data),\n            text: this.generateReviewNotificationText(data)\n        };\n        const result = await this.sendEmail(template);\n        return result;\n    }\n    /**\n   * Send review approval notification to customer\n   */ async sendReviewApprovalNotification(customerEmail, data) {\n        const template = {\n            to: customerEmail,\n            subject: `Your Review Has Been Approved | EBAM Motors`,\n            html: this.generateReviewApprovalHTML(data),\n            text: this.generateReviewApprovalText(data)\n        };\n        const result = await this.sendEmail(template);\n        return result;\n    }\n    /**\n   * Send contact form submission notification\n   */ async sendContactFormNotification(data) {\n        // Send to admin\n        const adminTemplate = {\n            to: EMAIL_CONFIG.adminEmail,\n            subject: `New Contact Form Submission - ${data.subject} | EBAM Motors`,\n            html: this.generateContactFormAdminHTML(data),\n            text: this.generateContactFormAdminText(data)\n        };\n        // Send confirmation to customer\n        const customerTemplate = {\n            to: data.email,\n            subject: `Thank you for contacting EBAM Motors`,\n            html: this.generateContactFormCustomerHTML(data),\n            text: this.generateContactFormCustomerText(data)\n        };\n        const [adminResult, customerResult] = await Promise.all([\n            this.sendEmail(adminTemplate),\n            this.sendEmail(customerTemplate)\n        ]);\n        return {\n            success: adminResult.success && customerResult.success,\n            error: adminResult.error || customerResult.error\n        };\n    }\n    /**\n   * Send admin notification\n   */ async sendAdminNotification(data) {\n        const template = {\n            to: EMAIL_CONFIG.adminEmail,\n            subject: `${data.title} | EBAM Motors Admin`,\n            html: this.generateAdminNotificationHTML(data),\n            text: this.generateAdminNotificationText(data)\n        };\n        const result = await this.sendEmail(template);\n        return result;\n    }\n    /**\n   * Send follow-up email\n   */ async sendFollowUpEmail(data) {\n        let subject = '';\n        let html = '';\n        let text = '';\n        switch(data.type){\n            case 'abandoned_cart':\n                subject = 'Complete Your Purchase - Items Still Available | EBAM Motors';\n                html = this.generateAbandonedCartHTML(data);\n                text = this.generateAbandonedCartText(data);\n                break;\n            case 'delivery_update':\n                subject = 'Delivery Update for Your Order | EBAM Motors';\n                html = this.generateDeliveryUpdateHTML(data);\n                text = this.generateDeliveryUpdateText(data);\n                break;\n            case 'feedback_request':\n                subject = 'How was your experience with EBAM Motors?';\n                html = this.generateFeedbackRequestHTML(data);\n                text = this.generateFeedbackRequestText(data);\n                break;\n            case 'maintenance_reminder':\n                subject = 'Vehicle Maintenance Reminder | EBAM Motors';\n                html = this.generateMaintenanceReminderHTML(data);\n                text = this.generateMaintenanceReminderText(data);\n                break;\n            default:\n                return {\n                    success: false,\n                    error: 'Unknown follow-up type'\n                };\n        }\n        const template = {\n            to: data.customerEmail,\n            subject,\n            html,\n            text\n        };\n        const result = await this.sendEmail(template);\n        return result;\n    }\n    // HTML template generators using EmailTemplates class\n    generateOrderConfirmationHTML(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateOrderConfirmationHTML(data);\n    }\n    generateOrderConfirmationText(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateOrderConfirmationText(data);\n    }\n    generateReviewNotificationHTML(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateReviewNotificationHTML(data);\n    }\n    generateReviewNotificationText(data) {\n        return `New Review from ${data.customerName} for ${data.vehicleTitle} - Rating: ${data.rating}/5`;\n    }\n    generateReviewApprovalHTML(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateReviewApprovalHTML(data);\n    }\n    generateReviewApprovalText(data) {\n        return `Your review for ${data.vehicleTitle} has been approved. Thank you ${data.customerName}!`;\n    }\n    generateContactFormAdminHTML(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateContactFormAdminHTML(data);\n    }\n    generateContactFormAdminText(data) {\n        return `New Contact Form from ${data.name} (${data.email}) - Subject: ${data.subject}`;\n    }\n    generateContactFormCustomerHTML(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateContactFormCustomerHTML(data);\n    }\n    generateContactFormCustomerText(data) {\n        return `Thank you for contacting EBAM Motors, ${data.name}. We received your message about \"${data.subject}\" and will respond within 24 hours.`;\n    }\n    generateAdminNotificationHTML(data) {\n        return `<h1>${data.title}</h1><p>${data.message}</p>`;\n    }\n    generateAdminNotificationText(data) {\n        return `${data.title}: ${data.message}`;\n    }\n    generateAbandonedCartHTML(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateAbandonedCartHTML(data);\n    }\n    generateAbandonedCartText(data) {\n        return `Hi ${data.customerName}! You left some amazing vehicles in your cart. Complete your purchase at EBAM Motors and get them shipped to Ghana. Don't miss out!`;\n    }\n    generateDeliveryUpdateHTML(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateDeliveryUpdateHTML(data);\n    }\n    generateDeliveryUpdateText(data) {\n        return `Delivery update for ${data.customerName}: ${data.data?.status || 'Your vehicle is on its way'}. Track your order at ebammotors.com`;\n    }\n    generateFeedbackRequestHTML(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateFeedbackRequestHTML(data);\n    }\n    generateFeedbackRequestText(data) {\n        return `Hi ${data.customerName}! How was your experience with EBAM Motors? We'd love to hear your feedback. Leave a review at ebammotors.com/reviews`;\n    }\n    generateMaintenanceReminderHTML(data) {\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>Vehicle Maintenance Reminder</title>\n        <style>\n          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }\n          .container { max-width: 600px; margin: 0 auto; background-color: white; }\n          .header { background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); color: white; padding: 30px; text-align: center; }\n          .logo { font-size: 28px; font-weight: bold; margin-bottom: 10px; }\n          .content { padding: 30px; }\n          .highlight { background-color: #eff6ff; padding: 15px; border-radius: 6px; margin: 15px 0; }\n          .button { display: inline-block; background: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }\n          .footer { background-color: #f9fafb; padding: 20px; text-align: center; color: #6b7280; font-size: 14px; }\n        </style>\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors</div>\n            <p>Keep your vehicle in perfect condition</p>\n          </div>\n          <div class=\"content\">\n            <h1>Vehicle Maintenance Reminder</h1>\n            <p>Hi ${data.customerName},</p>\n            <p>It's time for your vehicle's scheduled maintenance to keep it running smoothly and safely.</p>\n            <div class=\"highlight\">\n              <h3>Recommended Maintenance</h3>\n              <p><strong>Vehicle:</strong> ${data.data?.vehicleTitle || 'Your vehicle'}</p>\n              <p><strong>Mileage:</strong> ${data.data?.currentMileage || 'Check your odometer'}</p>\n              <p><strong>Service Due:</strong> ${data.data?.serviceType || 'Regular maintenance'}</p>\n            </div>\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://yourdomain.com/contact\" class=\"button\">Schedule Service</a>\n            </div>\n            <p>Regular maintenance helps ensure:</p>\n            <ul>\n              <li>🔧 Optimal performance and fuel efficiency</li>\n              <li>🛡️ Safety and reliability</li>\n              <li>💰 Prevention of costly repairs</li>\n              <li>📈 Maintained resale value</li>\n            </ul>\n          </div>\n          <div class=\"footer\">\n            <p>Thank you for choosing EBAM Motors!</p>\n            <p>© 2024 EBAM Motors. All rights reserved.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    generateMaintenanceReminderText(data) {\n        return `Hi ${data.customerName}! It's time for your vehicle's scheduled maintenance. Contact EBAM Motors to schedule service and keep your vehicle running smoothly.`;\n    }\n}\n// Export singleton instance\nconst emailService = new ResendEmailService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/resendService.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("async_hooks");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "prettier/plugins/html":
/*!****************************************!*\
  !*** external "prettier/plugins/html" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/plugins/html");;

/***/ }),

/***/ "prettier/standalone":
/*!**************************************!*\
  !*** external "prettier/standalone" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/standalone");;

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@babel","vendor-chunks/uuid","vendor-chunks/resend","vendor-chunks/fflate","vendor-chunks/jspdf"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Forders%2Froute&page=%2Fapi%2Forders%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Forders%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();